<template>
    <view>
        <u-popup class="pop-mobile" v-model="showPopup" mode="center" border-radius="20" :mask-close-able="false">
            <view class="mobile-module">
                <view class="mobile-title">绑定手机号</view>
                <view class="mobile-desc">绑定手机号可享受更多服务</view>
                <view class="mobile-form">
                    <view class="form-item">
                        <view class="form-item-l">
                            <u-input v-model="mobile" :clearable="false" placeholder="请输入手机号" />
                        </view>
                    </view>
                    <view class="form-item">
                        <view class="form-item-l">
                            <u-input v-model="code" :clearable="false" placeholder="请输入验证码" />
                        </view>
                        <view class="form-item-r" @click="getCode">{{ tips }}</view>
                    </view>
                    <view class="btn-group">
                        <button class="cancel-btn" @click.stop="hide">取消</button>
                        <button class="confirm-btn" @click.stop="bindMobile">确认绑定</button>
                    </view>
                </view>
            </view>
        </u-popup>
        <u-verification-code change-text="xs" ref="uCode" @change="codeChange"></u-verification-code>
    </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import { sendCode, updatePhone } from '../../utils/vmeitime-http/user.js';

export default {
    name: 'mobile-popup',
    computed: {
        ...mapState(['hasLogin', 'userInfo']),
    },
    data() {
        return {
            showPopup: false,
            mobile: '',
            code: '',
            tips: '获取验证码',
            codeId: '', // 验证码ID
        };
    },
    methods: {
        ...mapMutations(['updateUserInfo']),
        
        // 显示弹窗
        show() {
            this.showPopup = true;
            // 如果用户已经有手机号，则预填充
            if (this.userInfo && this.userInfo.mobile) {
                this.mobile = this.userInfo.mobile;
            }
        },
        
        // 隐藏弹窗
        hide() {
            this.showPopup = false;
            this.resetForm();
        },
        
        // 重置表单
        resetForm() {
            this.mobile = '';
            this.code = '';
            this.codeId = '';
            this.tips = '获取验证码';
        },
        
        // 验证码变化
        codeChange(text) {
            this.tips = text;
        },
        
        // 获取验证码
        async getCode() {
            if (!this.$u.test.mobile(this.mobile)) {
                this.$u.toast('请输入正确的手机号码', 2000);
                return;
            }
            
            if (this.$refs.uCode.canGetCode) {
                uni.showLoading({
                    title: '获取验证码',
                });
                
                try {
                    // 发送验证码
                    const res = await sendCode({
                        mobile: this.mobile,
                        access_token: uni.getStorageSync('token')
                    });
                    
                    if (res.status === 0) {
                        this.codeId = res.data;
                        uni.hideLoading();
                        this.$u.toast('验证码已发送', 2000);
                        // 开始倒计时
                        this.$refs.uCode.start();
                    } else {
                        uni.hideLoading();
                        this.$u.toast(res.message || '发送验证码失败', 2000);
                    }
                } catch (error) {
                    uni.hideLoading();
                    this.$u.toast('发送验证码失败', 2000);
                    console.error('发送验证码失败', error);
                }
            }
        },
        
        // 绑定手机号
        async bindMobile() {
            if (!this.$u.test.mobile(this.mobile)) {
                this.$u.toast('请输入正确的手机号码', 2000);
                return;
            }
            
            if (!this.code) {
                this.$u.toast('请输入验证码', 2000);
                return;
            }
            
            if (!this.codeId) {
                this.$u.toast('请先获取验证码', 2000);
                return;
            }
            
            uni.showLoading({
                title: '绑定中',
                mask: true
            });
            
            try {
                // 更新手机号
                const res = await updatePhone({
                    mobile: this.mobile,
                    code: this.code,
                    code_id: this.codeId,
                    access_token: uni.getStorageSync('token')
                });
                
                if (res.status === 0) {
                    // 更新用户信息
                    const userInfo = { ...this.userInfo, mobile: this.mobile };
                    this.updateUserInfo(userInfo);
                    
                    uni.hideLoading();
                    this.$u.toast('手机号绑定成功', 2000);
                    
                    // 通知父组件绑定成功
                    this.$emit('bindSuccess', this.mobile);
                    
                    // 关闭弹窗
                    this.hide();
                } else {
                    uni.hideLoading();
                    this.$u.toast(res.message || '绑定失败', 2000);
                }
            } catch (error) {
                uni.hideLoading();
                this.$u.toast('绑定失败', 2000);
                console.error('绑定手机号失败', error);
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.mobile-module {
    background: #ffffff;
    padding: 40rpx 30rpx;
    width: 560rpx;
    border-radius: 20rpx;
}

.mobile-title {
    font-size: 36rpx;
    font-weight: 500;
    color: #333;
    text-align: center;
    margin-bottom: 20rpx;
}

.mobile-desc {
    font-size: 28rpx;
    color: #666;
    text-align: center;
    margin-bottom: 40rpx;
}

.mobile-form {
    .form-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #EEEEEE;
        margin-bottom: 30rpx;
        padding-bottom: 10rpx;
        
        &-l {
            flex: 1;
        }
        
        &-r {
            width: 180rpx;
            text-align: center;
            font-size: 28rpx;
            color: #4a90e2;
            padding: 10rpx 0;
            border-left: 1px solid #EEEEEE;
        }
    }
    
    .btn-group {
        display: flex;
        justify-content: space-between;
        margin-top: 50rpx;
        
        button {
            width: 45%;
            height: 80rpx;
            line-height: 80rpx;
            border-radius: 40rpx;
            font-size: 32rpx;
            
            &.cancel-btn {
                color: #666;
                background: #F2F2F2;
            }
            
            &.confirm-btn {
                color: #fff;
                background: linear-gradient(135deg, #4a90e2, #5e62b0);
            }
        }
    }
}
</style> 