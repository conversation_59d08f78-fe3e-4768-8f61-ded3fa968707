# 送礼物打赏功能使用指南

## 功能概述

送礼物打赏功能允许用户向作品作者赠送虚拟礼物，支持余额检查、充值引导和确认弹窗等完整流程。

## ✅ 已完成功能

### 1. 接口对接完成
- ✅ 获取用户余额接口 (`getUserWallet`)
- ✅ 打赏作者接口 (`rewardAuthor`)
- ✅ 充值余额接口 (`createBalanceOrder`)
- ✅ 微信支付接口 (`createMiniPay`)

### 2. 组件功能完成
- ✅ 新建 `reward-popup.vue` 专业打赏组件
- ✅ 更新 `gift-chat-popup.vue` 聊天礼物组件
- ✅ 余额检查逻辑
- ✅ 充值引导功能
- ✅ 确认弹窗防误操作

### 3. 页面集成完成
- ✅ 作品详情页面 (`views/moments/detail.vue`) 集成打赏功能
- ✅ 添加专门的打赏按钮
- ✅ 完善样式和交互

### 4. 测试页面
- ✅ 创建测试页面 (`pages/test/reward-test.vue`)
- ✅ 包含完整的功能测试和日志记录

## 核心组件

### 1. reward-popup.vue - 打赏弹窗组件

**功能特点：**
- 显示礼物选项（5朵花、10朵花、20朵花、99朵花）
- 实时显示用户余额
- 余额不足时引导充值
- 确认弹窗防止误操作
- 支持自定义礼物配置

**使用方法：**
```vue
<reward-popup 
    v-model="showReward" 
    :work-id="workInfo.id"
    :author-info="workInfo.user"
    @reward-success="handleRewardSuccess"
    @close="handleRewardClose">
</reward-popup>
```

**Props参数：**
- `value`: Boolean - 控制弹窗显示/隐藏
- `workId`: String/Number - 作品ID（必填）
- `authorInfo`: Object - 作者信息对象

**Events事件：**
- `reward-success`: 打赏成功回调，参数包含礼物信息和作品ID
- `close`: 弹窗关闭回调

### 2. gift-chat-popup.vue - 聊天礼物弹窗（已更新）

**更新内容：**
- 添加余额检查逻辑
- 支持充值引导
- 完善确认弹窗
- 集成打赏接口调用

## 接口说明

### 1. 获取用户余额
```javascript
// 接口：getUserWallet
// 文件：utils/vmeitime-http/user.js
const res = await this.$api.getUserWallet({
    access_token: uni.getStorageSync('token')
});
```

### 2. 打赏作者
```javascript
// 接口：rewardAuthor  
// 文件：utils/vmeitime-http/order.js
const res = await this.$api.rewardAuthor({
    good_id: giftGoodId,    // 礼物商品ID
    num: giftCount,         // 礼物数量
    work_id: workId,        // 作品ID
    access_token: uni.getStorageSync('token')
});
```

### 3. 充值余额
```javascript
// 接口：createBalanceOrder
// 文件：utils/vmeitime-http/order.js
const res = await this.$api.createBalanceOrder({
    good_id: selectedAmount,  // 充值商品ID
    num: 1,                   // 数量
    access_token: uni.getStorageSync('token')
});
```

## 完整流程

### 1. 打赏流程
1. 用户点击打赏按钮
2. 显示打赏弹窗，加载用户余额
3. 用户选择礼物类型
4. 检查余额是否足够
5. 如果余额不足，显示充值引导
6. 如果余额足够，显示确认弹窗
7. 用户确认后调用打赏接口
8. 显示打赏结果

### 2. 充值流程
1. 用户点击充值按钮或余额不足时引导
2. 跳转到充值页面
3. 选择充值金额
4. 创建充值订单
5. 调用微信支付
6. 支付成功后更新余额

## 使用示例

### 在作品详情页面添加打赏功能

```vue
<template>
    <view class="work-detail">
        <!-- 作品内容 -->
        <view class="work-content">
            <!-- 作品信息 -->
        </view>
        
        <!-- 操作按钮 -->
        <view class="action-buttons">
            <button @click="showRewardPopup" v-if="!isOwnWork">
                打赏作者
            </button>
        </view>
        
        <!-- 打赏弹窗 -->
        <reward-popup 
            v-model="showReward" 
            :work-id="workInfo.id"
            :author-info="workInfo.user"
            @reward-success="handleRewardSuccess">
        </reward-popup>
    </view>
</template>

<script>
import rewardPopup from '@/components/reward-popup.vue';

export default {
    components: { rewardPopup },
    data() {
        return {
            showReward: false,
            workInfo: {}, // 作品信息
        };
    },
    methods: {
        showRewardPopup() {
            if (!this.$store.state.hasLogin) {
                uni.showToast({ title: '请先登录', icon: 'none' });
                return;
            }
            this.showReward = true;
        },
        
        handleRewardSuccess(data) {
            console.log('打赏成功', data);
            // 处理打赏成功逻辑
        },
    },
};
</script>
```

## 配置说明

### 礼物配置
在 `reward-popup.vue` 中的 `giftList` 数组可以配置礼物选项：

```javascript
giftList: [
    {
        id: 1,
        name: '5朵鲜花',
        price: 5,
        count: 5,
        icon: '/static/images/hua.png',
        good_id: 1, // 对应后端商品ID
    },
    // 更多礼物配置...
]
```

### 主题色配置
组件使用项目主题色 `#8966ef` 和 `#f0cc6c`，与项目整体风格保持一致。

## 注意事项

1. **登录检查**：使用打赏功能前需要检查用户登录状态
2. **余额检查**：每次打赏前都会检查用户余额
3. **确认弹窗**：防止用户误操作，提升用户体验
4. **错误处理**：完善的错误提示和网络异常处理
5. **商品ID配置**：需要根据后端实际的商品ID配置礼物选项

## 扩展功能

1. **动画效果**：可以添加礼物飞行动画
2. **礼物记录**：显示用户的打赏历史
3. **排行榜**：显示作品的打赏排行
4. **自定义金额**：支持用户输入自定义打赏金额
