# 跃汇街 - 体育社交小程序

一个基于 uni-app 框架开发的体育社交小程序应用，支持多端发布（微信小程序、支付宝小程序、App等）。

## 📱 项目介绍

跃汇街是一款专注于体育社交的移动应用，为用户提供：
- 🏠 **首页** - 浏览体育相关内容和动态
- 💬 **消息** - 实时聊天和消息通知
- 👤 **个人中心** - 用户信息管理和设置
- 💰 **钱包** - 余额管理和充值功能
- 🤖 **智能机器人** - 自动回复和智能客服
- 📱 **朋友圈** - 发布动态、点赞评论、关注好友

## 🛠 技术栈

- **框架**: uni-app (Vue 2.x)
- **UI组件库**: uView UI 1.8.4
- **状态管理**: Vuex
- **开发工具**: HBuilderX
- **支持平台**:
  - 微信小程序
  - 支付宝小程序
  - 百度小程序
  - 头条小程序
  - Android App
  - iOS App

## 📁 项目结构

```
social-mini-app/
├── components/          # 公共组件
│   ├── UserCard.vue    # 用户卡片组件
│   ├── moments/        # 朋友圈相关组件
│   ├── popup/          # 弹窗组件
│   └── ...
├── pages/              # 页面文件
│   ├── index/          # 主要页面
│   │   ├── index.vue   # 首页
│   │   ├── chat-list.vue # 消息列表
│   │   ├── mine.vue    # 个人中心
│   │   └── ...
│   └── wallet/         # 钱包相关页面
├── views/              # 子包页面
│   ├── chat_page/      # 聊天页面
│   ├── moments/        # 朋友圈页面
│   └── robot/          # 机器人页面
├── static/             # 静态资源
├── store/              # Vuex状态管理
├── utils/              # 工具类
│   ├── config.js       # 配置文件
│   ├── utils.js        # 通用工具
│   └── vmeitime-http/  # HTTP请求封装
├── App.vue             # 应用入口
├── main.js             # 主入口文件
├── pages.json          # 页面配置
└── manifest.json       # 应用配置
```

## 🚀 快速开始

### 环境要求

- Node.js >= 12.0.0
- HBuilderX 或 uni-app CLI
- 微信开发者工具（用于微信小程序调试）

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd social-mini-app
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **配置开发环境**
   - 在 `utils/config.js` 中配置API接口地址
   - 在 `manifest.json` 中配置小程序AppID

4. **运行项目**

   使用HBuilderX：
   - 用HBuilderX打开项目
   - 点击运行 -> 运行到小程序模拟器 -> 微信开发者工具

   使用CLI：
   ```bash
   # 微信小程序
   npm run dev:mp-weixin

   # 支付宝小程序
   npm run dev:mp-alipay

   # App
   npm run dev:app-plus
   ```

## 🔧 配置说明

### API配置
在 `main.js` 中配置API基础地址：
```javascript
Vue.prototype.$apiHost = 'https://gaoshi.wdaoyun.cn';
```

### 小程序配置
在 `manifest.json` 中配置各平台的AppID和权限：
- 微信小程序：`mp-weixin.appid`
- 支付宝小程序：`mp-alipay`
- 其他平台配置

## 📱 功能特性

### 核心功能
- ✅ 用户注册登录
- ✅ 个人信息管理
- ✅ 实时聊天消息
- ✅ 朋友圈动态发布
- ✅ 点赞评论互动
- ✅ 钱包余额管理
- ✅ 智能机器人客服

### 技术特性
- 📱 多端适配（小程序、App）
- 🎨 响应式UI设计
- 🔄 下拉刷新、上拉加载
- 📍 地理位置服务
- 📷 图片上传处理
- 🔔 消息推送通知

## 🛠 开发指南

### 添加新页面
1. 在 `pages` 或 `views` 目录下创建页面文件
2. 在 `pages.json` 中注册页面路由
3. 配置页面标题和样式

### 使用组件
项目集成了uView UI组件库，可直接使用：
```vue
<template>
  <u-button type="primary">按钮</u-button>
</template>
```

### API请求
使用封装的HTTP工具：
```javascript
this.$api.get('/api/user/info').then(res => {
  // 处理响应
});
```

## 📦 构建部署

### 小程序发布
1. 使用HBuilderX或CLI构建项目
2. 上传到对应小程序平台
3. 提交审核发布

### App打包
1. 配置App图标和启动页
2. 使用HBuilderX云打包
3. 生成安装包

## 🤝 参与贡献

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 ISC 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件至：[联系邮箱]
- 项目讨论群：[群号]

---

⭐ 如果这个项目对你有帮助，请给它一个星标！
