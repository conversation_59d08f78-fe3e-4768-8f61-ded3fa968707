<template>
    <view class="content">
        <view>
            <view class="card-title-box">
                <view class="ct-left">
                    <image :src="info.headimgurl" mode="aspectFill"></image>
                </view>
                <view class="ct-right" v-show="info.nickname">
                    <view class="ct-name">{{ info.nickname }}</view>
                </view>
                <view class="header-decoration">
                    <view class="decoration-circle circle-1"></view>
                    <view class="decoration-circle circle-2"></view>
                </view>
            </view>

            <view class="inventory-box">
                <view class="fang-view"></view>
                <view class="title">{{ inventory.topic }}</view>

                <view class="content-box">
                    <view class="content-item" v-for="(item, index) in inventory.content" :key="index">
                        <view v-if="item.type == 1" class="introduction">
                            <text>{{ item.content }}</text>
                        </view>
                        <view v-if="item.type == 2" class="one-img">
                            <u-image @click="reviewImg(item, 0)" width="100%" :lazy-load="false" :src="item.content" mode="widthFix" :show-menu-by-longpress="false" border-radius="10rpx"></u-image>
                        </view>
                        <view v-if="item.type == 3" class="img-list">
                            <u-image
                                class="img-list-item"
                                v-for="(item1, index1) in item.content"
                                :key="index1"
                                @click="reviewImg(item, index1)"
                                width="200rpx"
                                height="200rpx"
                                mode="aspectFill"
                                border-radius="10rpx"
                                :lazy-load="false"
                                :show-menu-by-longpress="false"
                                :src="item1"
                            ></u-image>
                        </view>
                    </view>
                </view>

                <!-- 商品规格预览 -->
                <view class="goods-preview" v-if="inventory.listtype == 1">
                    <view class="box-title">
                        <u-divider border-color="#999">商品规格</u-divider>
                    </view>
                    <view class="specs-preview">
                        <view class="specs-info">
                            <text class="specs-count" v-if="inventory.goods_data[0] && inventory.goods_data[0].specs_data">{{ inventory.goods_data[0].specs_data.length }}个规格</text>
                            <text class="price-range">¥{{ inventory.goods_data[0].min_retail_price }} - ¥{{ inventory.goods_data[0].max_retail_price }}</text>
                        </view>
                        <view class="specs-images" v-if="inventory.goods_data[0] && inventory.goods_data[0].specs_data">
                            <u-image
                                v-for="(sku, index) in inventory.goods_data[0].specs_data.slice(0, 3)"
                                :key="index"
                                width="80rpx"
                                height="80rpx"
                                :src="sku.image"
                                mode="aspectFill"
                                border-radius="6rpx"
                                style="margin-left: 10rpx"
                            ></u-image>
                            <view v-if="inventory.goods_data[0].specs_data.length > 3" class="more-specs">+{{ inventory.goods_data[0].specs_data.length - 3 }}</view>
                        </view>
                    </view>
                </view>
            </view>

            <view class="footer-box">
                <view class="buy-box">
                    <!-- 左侧店铺信息 -->
                    <view class="shop-box">
                        <view class="shop-info" @click="goToShop">
                            <view class="shop-icon">
                                <u-icon name="shopping-cart-fill" size="32" color="#8966ef"></u-icon>
                            </view>
                            <text class="shop-text">店铺</text>
                        </view>

                        <view class="shop-info" @click="goToChat" v-if="!isOwnUser">
                            <view class="shop-icon">
                                <u-icon name="kefu-ermai" size="32" color="#8966ef"></u-icon>
                            </view>
                            <text class="shop-text">聊一聊</text>
                        </view>
                    </view>

                    <!-- 右侧按钮组 -->
                    <view class="button-group">
                        <button class="footer-btn share-btn" open-type="share">分享</button>
                        <button class="footer-btn buy-btn" @click="showShoppingPopup">去下单</button>
                    </view>
                </view>
            </view>
        </view>

        <!-- 购物弹窗 -->
        <u-popup v-model="showPopup" mode="bottom" border-radius="20" :safe-area-inset-bottom="true">
            <view class="shopping-popup">
                <view class="popup-header">
                    <view class="popup-title">选择商品</view>
                    <view class="popup-close" @click="closeShoppingPopup">
                        <u-icon name="close" size="32" color="#666"></u-icon>
                    </view>
                </view>

                <view class="popup-content">
                    <!-- 商品信息 -->
                    <view class="popup-goods-info">
                        <view class="goods-image" @click="previewImage(selectedSku.image)">
                            <u-image width="120rpx" height="120rpx" :src="selectedSku.image" mode="aspectFill" border-radius="8rpx"></u-image>
                        </view>
                        <view class="goods-detail">
                            <view class="goods-name">{{ inventory.topic }}</view>
                            <view class="goods-price">
                                <text class="price-symbol">¥</text>
                                <text class="price-value">{{ selectedSku.specs_retail_price || '0.00' }}</text>
                                <text class="original-price" v-if="selectedSku.specs_original_price && selectedSku.specs_original_price != selectedSku.specs_retail_price">
                                    ¥{{ selectedSku.specs_original_price }}
                                </text>
                            </view>
                            <view class="goods-stock">库存：{{ selectedSku.specs_stock || 999 }}件</view>
                        </view>
                    </view>

                    <!-- 规格选择 -->
                    <view class="specs-section">
                        <view class="section-title">选择规格</view>
                        <view class="specs-list" v-if="inventory.goods_data[0] && inventory.goods_data[0].specs_data">
                            <view
                                class="spec-item"
                                v-for="(sku, index) in inventory.goods_data[0].specs_data"
                                :key="index"
                                :class="{ active: selectedSkuIndex === index }"
                                @click="selectSku(sku, index)"
                            >
                                <u-image width="60rpx" height="60rpx" :src="sku.image" mode="aspectFill" border-radius="6rpx"></u-image>
                                <view class="spec-name">{{ sku.specs_name }}</view>
                                <view class="spec-price">¥{{ sku.specs_retail_price }}</view>
                            </view>
                        </view>
                    </view>

                    <!-- 数量选择 -->
                    <view class="quantity-section">
                        <view class="section-title">购买数量</view>
                        <view class="quantity-selector-popup">
                            <view class="quantity-btn" @click="decreasePopupQuantity" :class="{ disabled: popupQuantity <= 1 }">
                                <u-icon name="minus" size="24" color="#8966ef"></u-icon>
                            </view>
                            <view class="quantity-input">{{ popupQuantity }}</view>
                            <view class="quantity-btn" @click="increasePopupQuantity">
                                <u-icon name="plus" size="24" color="#8966ef"></u-icon>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="popup-footer">
                    <view class="total-info">
                        <text>小计：</text>
                        <text class="total-price">¥{{ totalPopupPrice }}</text>
                    </view>
                    <button class="confirm-btn" @click="confirmOrder">确认下单</button>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex';
import { getUserGoodDetail } from '../../utils/vmeitime-http/shop.js';

export default {
    onShareAppMessage: function () {
        //  分享清单
        let shareData = {
            shareUid: this.userInfo.uid || '',
            type: 'shop',
        };
        return {
            title: this.inventory.topic || '商品详情',
            path: `/views/shop/detail?shareData=${JSON.stringify(shareData)}&id=${this.inventory.goods_data[0]?.id}`,
            imageUrl: this.inventory.goods_data[0]?.img,
        };
    },
    computed: {
        ...mapState(['userInfo', 'hasLogin']),

        // 获取当前选择的SKU
        selectedSku() {
            if (!this.inventory.goods_data || this.inventory.goods_data.length === 0) return {};
            const specs = this.inventory.goods_data[0]?.specs_data || [];
            return specs[this.selectedSkuIndex] || {};
        },

        // 计算弹窗总价
        totalPopupPrice() {
            const price = parseFloat(this.selectedSku.specs_retail_price || 0);
            return (price * this.popupQuantity).toFixed(2);
        },

        isOwnUser() {
            return this.userInfo && this.inventory.web_user_id && this.userInfo.uid === this.inventory.web_user_id;
        },
    },
    data() {
        return {
            inventory: {
                topic: '',
                content: [],
                listtype: 1,
                goods_data: [],
                web_user_id: '',
            },
            familyData: {},
            // 弹窗相关数据
            showPopup: false,
            selectedSkuIndex: 0,
            popupQuantity: 1,
            info: {},
        };
    },
    onLoad(e) {
        this.loadGoodDetail(e.id);
        if (e.shareData) {
            this.setShareData(e.shareData);
        }
        if(e.showCart){
            this.showShoppingPopup();
        }
    },
    methods: {
        ...mapMutations(['setShareData']),
        ...mapActions(['VAgetUser']),

        // 加载商品详情
        loadGoodDetail(goodId) {
            getUserGoodDetail({
                access_token: uni.getStorageSync('token'),
                good_id: goodId,
            })
                .then(res => {
                    console.log('商品详情API返回:', res);
                    this.processGoodData(res.data.good);
                })
                .catch(err => {
                    uni.showToast({
                        title: '商品不存在',
                        icon: 'none',
                    });
                    setTimeout(() => {
                        uni.navigateBack();
                    }, 800);
                });
        },

        // 处理商品数据，转换为页面需要的格式
        processGoodData(goodData) {
            // 解析图片数据
            let images = [];
            try {
                images = JSON.parse(goodData.images || '[]');
            } catch (e) {
                console.error('解析图片数据失败:', e);
                images = [];
            }

            // 构建内容数组，包含文本和图片
            let content = [];

            // 添加商品描述
            if (goodData.content) {
                content.push({
                    type: 1, // 文本类型
                    content: goodData.content,
                });
            }

            // 添加图片
            if (images.length > 0) {
                if (images.length === 1) {
                    // 单张图片
                    content.push({
                        type: 2, // 单图类型
                        content: images[0],
                    });
                } else {
                    // 多张图片
                    content.push({
                        type: 3, // 图片列表类型
                        content: images,
                    });
                }
            }

            // 构建商品数据
            this.inventory = {
                topic: goodData.title,
                web_user_id: goodData.web_user_id,
                content: content,
                listtype: 1, // 商品类型
                goods_data: [
                    {
                        id: goodData.id,
                        name: goodData.title,
                        img: goodData.cover_img,
                        price: goodData.sku_list && goodData.sku_list.length > 0 ? goodData.sku_list[0].price : '0',
                        min_retail_price: this.getMinPrice(goodData.sku_list),
                        max_retail_price: this.getMaxPrice(goodData.sku_list),
                        specs_data: this.convertSkuToSpecs(goodData.sku_list),
                        specs_title: [],
                        num: 0,
                    },
                ],
            };
            this.info = goodData.web_user;

            console.log('处理后的商品数据:', this.inventory);
        },

        // 获取SKU最低价格
        getMinPrice(skuList) {
            if (!skuList || skuList.length === 0) return '0';
            return Math.min(...skuList.map(sku => parseFloat(sku.price))).toFixed(2);
        },

        // 获取SKU最高价格
        getMaxPrice(skuList) {
            if (!skuList || skuList.length === 0) return '0';
            return Math.max(...skuList.map(sku => parseFloat(sku.price))).toFixed(2);
        },

        // 将SKU数据转换为规格数据
        convertSkuToSpecs(skuList) {
            if (!skuList || skuList.length === 0) return [];

            return skuList.map(sku => ({
                specsid: sku.id,
                specs_name: sku.sku_name,
                specs_retail_price: sku.price,
                specs_original_price: sku.org_price,
                specs_stock: 999, // 默认库存，实际应该从API获取
                profit: 0, // 默认佣金
                image: sku.image,
                quantity: 0, // 初始化数量为0
            }));
        },

        // 预览图片
        reviewImg(item, index) {
            let type = typeof item.content;
            if (type == 'string') {
                let arr = [];
                arr.push(item.content);
                uni.previewImage({
                    urls: arr,
                });
            } else {
                uni.previewImage({
                    current: item.content[index],
                    urls: item.content,
                });
            }
        },

        previewImage(url) {
            uni.previewImage({
                current: url,
                urls: [url],
            });
        },

        // 显示购物弹窗
        showShoppingPopup() {
            this.showPopup = true;
            this.selectedSkuIndex = 0;
            this.popupQuantity = 1;
        },

        // 关闭购物弹窗
        closeShoppingPopup() {
            this.showPopup = false;
        },

        // 选择SKU
        selectSku(sku, index) {
            console.log(sku);
            this.selectedSkuIndex = index;
        },

        // 增加弹窗数量
        increasePopupQuantity() {
            this.popupQuantity++;
        },

        // 减少弹窗数量
        decreasePopupQuantity() {
            if (this.popupQuantity > 1) {
                this.popupQuantity--;
            }
        },

        // 确认下单
        confirmOrder() {
            if (!this.selectedSku.specsid) {
                uni.showToast({
                    title: '请选择商品规格',
                    icon: 'none',
                });
                return;
            }

            // 关闭弹窗
            this.closeShoppingPopup();

            // 跳转到下单页面
            uni.navigateTo({
                url: `/views/shop/goodsCommit?id=${this.inventory.goods_data[0].id}&child=${this.selectedSkuIndex}&num=${this.popupQuantity}`,
            });
        },

        // 跳转到店铺页面
        goToShop() {
            uni.navigateTo({
                url: `/views/shop/goodsList?userId=${this.inventory.web_user_id}`,
            });
        },

        async goToChat() {
            const res = await this.$api.initSession({
                access_token: uni.getStorageSync('token'),
                to_uid: this.inventory.web_user_id,
            });
            const sessionId = res.data.msg_session_id;
            // 导航到聊天页面
            uni.navigateTo({
                url: `/views/moments/chat?sessionId=${sessionId}&userId=${this.info.uid}&username=${encodeURIComponent(this.info.nickname)}&avatar=${encodeURIComponent(this.info.headimgurl || '')}`,
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.content {
    padding-bottom: 200rpx;
   background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);
    min-height: 100vh;
}

.card-title-box {
    background: linear-gradient(135deg, #8966ef, #a584f2, #f0cc6c);
    display: flex;
    align-items: center;
    padding: 20rpx 25rpx;
    box-sizing: border-box;
    padding-bottom: 300rpx;
    position: relative;
    .ct-left {
        margin-right: 20rpx;
        image {
            width: 125rpx;
            height: 125rpx;
            border-radius: 50%;
            border: 2rpx solid #ffffff;
        }
    }
    .ct-right {
        width: 100%;
        color: $uni-bg-color;
        position: relative;
        .ct-name {
            font-weight: 500;
            font-size: 34rpx;
        }
    }
    .header-decoration {
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        z-index: 1;

        .decoration-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);

            &.circle-1 {
                width: 120rpx;
                height: 120rpx;
                top: -30rpx;
                right: 50rpx;
                animation: float 6s ease-in-out infinite;
            }

            &.circle-2 {
                width: 80rpx;
                height: 80rpx;
                top: 60rpx;
                right: 150rpx;
                animation: float 4s ease-in-out infinite reverse;
            }
        }
    }
}

.inventory-box {
    position: relative;
    background: #ffffff;
    width: 700rpx;
    padding: 30rpx 20rpx;
    box-sizing: border-box;
    border-radius: 20rpx;
    margin: -270rpx auto 0 auto;
    border: 1rpx solid #e9ecef;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    .fang-view {
        width: 20rpx;
        height: 20rpx;
        background: #ffffff;
        transform: rotate(45deg);
        position: absolute;
        left: 50rpx;
        top: -10rpx;
    }
    .title {
        font-size: 34rpx;
        color: #020203;
        font-weight: 500;
        margin-bottom: 20rpx;
    }

    .content-box {
        .content-item {
            margin-bottom: 20rpx;
            .introduction {
                width: 100%;
                font-size: 28rpx;
                color: #0b1539;
                margin: 20rpx 0;
                text {
                    width: 100%;
                    display: block;
                    line-height: 1.4em;
                }
            }
            .one-img,
            .one-img image {
                width: 100%;
                border-radius: 10rpx;
            }
            .img-list {
                overflow: hidden;
            }
            .img-list-item {
                display: inline;
                float: left;
                margin-right: 15rpx;
                margin-bottom: 15rpx;
            }
            .img-list-item:nth-of-type(3n) {
                margin-right: 0;
            }
        }
    }
}

.goods-list {
    padding: 20rpx 0;
    .box-title {
        line-height: 80rpx;
        text-align: center;
        color: #666666;
        margin: 20rpx 0;
    }
    .goods-item {
        display: flex;
        align-items: center;
        position: relative;
        margin-bottom: 25rpx;
        padding: 20rpx;
        background: #fafbfc;
        border-radius: 16rpx;
        border: 1rpx solid #f0f1f5;
        transition: all 0.3s ease;

        &:hover {
            border-color: rgba(137, 102, 239, 0.2);
            background: rgba(137, 102, 239, 0.02);
        }
        .gi-left {
            margin-right: 20rpx;
        }
        .gi-right {
            width: 100%;
            height: 180rpx;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .g-title {
                font-size: 32rpx;
                color: #020203;
                font-weight: 500;
            }

            .g-price-row {
                display: flex;
                align-items: center;
                justify-content: space-between;
                width: 100%;
            }

            .g-price {
                font-size: 38rpx;
                color: #8966ef;
                font-weight: 500;
                display: flex;
                align-items: center;

                .current-price {
                    font-size: 32rpx;
                    color: #8966ef;
                    font-weight: 500;
                    margin-right: 15rpx;
                }
                .original-price {
                    font-size: 24rpx;
                    color: #999;
                    text-decoration: line-through;
                }
            }

            .quantity-selector {
                display: flex;
                align-items: center;
                background: #f8f9fa;
                border-radius: 24rpx;
                border: 1rpx solid #e9ecef;

                .quantity-btn {
                    width: 48rpx;
                    height: 48rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #ffffff;
                    transition: all 0.3s ease;

                    &:first-child {
                        border-radius: 24rpx 0 0 24rpx;
                    }

                    &:last-child {
                        border-radius: 0 24rpx 24rpx 0;
                    }

                    &:active {
                        background: rgba(137, 102, 239, 0.1);
                        transform: scale(0.95);
                    }

                    &.disabled {
                        opacity: 0.3;
                        pointer-events: none;
                    }
                }

                .quantity-input {
                    min-width: 60rpx;
                    height: 48rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 28rpx;
                    font-weight: 500;
                    color: #333;
                    background: #ffffff;
                    border-left: 1rpx solid #e9ecef;
                    border-right: 1rpx solid #e9ecef;
                }
            }
        }
    }

    .sku-list {
        margin-top: 30rpx;
        .sku-title {
            font-size: 30rpx;
            font-weight: 500;
            color: #333;
            margin-bottom: 20rpx;
        }
        .sku-item {
            display: flex;
            align-items: center;
            padding: 20rpx;
            margin-bottom: 15rpx;
            background: #f8f8f8;
            border-radius: 10rpx;
            .sku-left {
                margin-right: 20rpx;
                image {
                    width: 80rpx;
                    height: 80rpx;
                    border-radius: 8rpx;
                }
            }
            .sku-right {
                flex: 1;
                .sku-name {
                    font-size: 28rpx;
                    color: #333;
                    margin-bottom: 10rpx;
                }
                .sku-price {
                    display: flex;
                    align-items: center;
                    .current-price {
                        font-size: 32rpx;
                        color: #fe6242;
                        font-weight: 500;
                        margin-right: 15rpx;
                    }
                    .original-price {
                        font-size: 24rpx;
                        color: #999;
                        text-decoration: line-through;
                    }
                }
            }
        }
    }
}

.footer-box {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 120rpx;
    background: #ffffff;
    display: flex;
    align-items: center;
    font-size: 30rpx;
    border-top: 1rpx solid #f0f0f0;
    box-sizing: content-box;
    z-index: 99;
    padding-bottom: 0;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    .buy-box {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 30rpx;
        .shop-box {
            display: flex;
            align-items: center;
            // justify-content: center;
            gap: 10rpx;
        }
        .shop-info {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 10rpx 20rpx;
            border-radius: 12rpx;
            transition: all 0.3s ease;

            &:active {
                background: rgba(137, 102, 239, 0.1);
                transform: scale(0.95);
            }

            .shop-icon {
                margin-bottom: 8rpx;
            }

            .shop-text {
                font-size: 24rpx;
                color: #8966ef;
                font-weight: 500;
            }
        }

        .button-group {
            display: flex;
            align-items: center;
            // gap: 20rpx;
        }

        .footer-btn {
            width: 160rpx;
            height: 80rpx;
            line-height: 80rpx;
            font-size: 28rpx;
            border-radius: 40rpx;
            text-align: center;
            font-weight: 500;
            border: none;
            transition: all 0.3s ease;

            &:active {
                transform: scale(0.95);
            }
        }

        .share-btn {
            background: #ffffff;
            color: #8966ef;
            border: 2rpx solid #8966ef;
            border-radius: 49rpx 0 0 49rpx;
        }

        .buy-btn {
            background: linear-gradient(135deg, #8966ef, #a584f2);
            color: #ffffff;
            box-shadow: 0 4rpx 12rpx rgba(137, 102, 239, 0.3);
            border-radius: 0rpx 49rpx 49rpx 0rpx;
            &.disabled {
                background: #e9ecef;
                color: #999;
                box-shadow: none;
                pointer-events: none;
            }
        }
    }
}

// 商品规格预览样式
.goods-preview {
    padding: 20rpx 0;
    .box-title {
        line-height: 80rpx;
        text-align: center;
        color: #666666;
        margin: 20rpx 0;
    }
    .specs-preview {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx;
        background: #fafbfc;
        border-radius: 16rpx;
        border: 1rpx solid #f0f1f5;

        .specs-info {
            display: flex;
            flex-direction: column;
            gap: 8rpx;

            .specs-count {
                font-size: 28rpx;
                color: #333;
                font-weight: 500;
            }

            .price-range {
                font-size: 32rpx;
                color: #8966ef;
                font-weight: 500;
            }
        }

        .specs-images {
            display: flex;
            align-items: center;

            .more-specs {
                width: 80rpx;
                height: 80rpx;
                background: #f0f1f5;
                border-radius: 6rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24rpx;
                color: #666;
                margin-left: 10rpx;
            }
        }
    }
}

// 购物弹窗样式
.shopping-popup {
    background: #ffffff;
    border-radius: 20rpx 20rpx 0 0;
    max-height: 80vh;

    .popup-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 30rpx;
        border-bottom: 1rpx solid #f0f1f5;

        .popup-title {
            font-size: 32rpx;
            font-weight: 500;
            color: #333;
        }

        .popup-close {
            width: 60rpx;
            height: 60rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: #f8f9fa;

            &:active {
                background: #e9ecef;
                transform: scale(0.95);
            }
        }
    }

    .popup-content {
        padding: 30rpx;
        max-height: 60vh;
        overflow-y: auto;

        .popup-goods-info {
            display: flex;
            align-items: flex-start;
            margin-bottom: 40rpx;

            .goods-image {
                margin-right: 20rpx;
            }

            .goods-detail {
                flex: 1;

                .goods-name {
                    font-size: 32rpx;
                    color: #333;
                    font-weight: 500;
                    margin-bottom: 15rpx;
                    line-height: 1.4;
                }

                .goods-price {
                    display: flex;
                    align-items: center;
                    margin-bottom: 10rpx;

                    .price-symbol {
                        font-size: 24rpx;
                        color: #8966ef;
                    }

                    .price-value {
                        font-size: 36rpx;
                        color: #8966ef;
                        font-weight: 600;
                        margin-right: 15rpx;
                    }

                    .original-price {
                        font-size: 24rpx;
                        color: #999;
                        text-decoration: line-through;
                    }
                }

                .goods-stock {
                    font-size: 24rpx;
                    color: #666;
                }
            }
        }

        .specs-section,
        .quantity-section {
            margin-bottom: 40rpx;

            .section-title {
                font-size: 28rpx;
                color: #333;
                font-weight: 500;
                margin-bottom: 20rpx;
            }
        }
        .quantity-section {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            .section-title {
                font-size: 28rpx;
                color: #333;
                font-weight: 500;
                margin-right: 20rpx;
                margin-bottom: 0;
            }
        }

        .specs-list {
            display: flex;
            flex-wrap: wrap;
            gap: 15rpx;

            .spec-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 20rpx;
                background: #f8f9fa;
                border-radius: 12rpx;
                border: 2rpx solid transparent;
                min-width: 120rpx;
                transition: all 0.3s ease;

                &.active {
                    border-color: #8966ef;
                    background: rgba(137, 102, 239, 0.1);
                }

                .spec-name {
                    font-size: 24rpx;
                    color: #333;
                    margin: 10rpx 0 5rpx;
                    text-align: center;
                }

                .spec-price {
                    font-size: 22rpx;
                    color: #8966ef;
                    font-weight: 500;
                }
            }
        }

        .quantity-selector-popup {
            display: flex;
            align-items: center;
            background: #f8f9fa;
            border-radius: 24rpx;
            border: 1rpx solid #e9ecef;
            width: fit-content;

            .quantity-btn {
                width: 60rpx;
                height: 60rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #ffffff;
                transition: all 0.3s ease;

                &:first-child {
                    border-radius: 24rpx 0 0 24rpx;
                }

                &:last-child {
                    border-radius: 0 24rpx 24rpx 0;
                }

                &:active {
                    background: rgba(137, 102, 239, 0.1);
                    transform: scale(0.95);
                }

                &.disabled {
                    opacity: 0.3;
                    pointer-events: none;
                }
            }

            .quantity-input {
                min-width: 80rpx;
                height: 60rpx;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 32rpx;
                font-weight: 500;
                color: #333;
                background: #ffffff;
                border-left: 1rpx solid #e9ecef;
                border-right: 1rpx solid #e9ecef;
            }
        }
    }

    .popup-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 30rpx;
        border-top: 1rpx solid #f0f1f5;
        background: #ffffff;

        .total-info {
            display: flex;
            align-items: center;
            font-size: 28rpx;
            color: #333;

            .total-price {
                font-size: 36rpx;
                color: #8966ef;
                font-weight: 600;
                margin-left: 10rpx;
            }
        }

        .confirm-btn {
            margin: 0;
            background: linear-gradient(135deg, #8966ef, #a584f2);
            color: #ffffff;
            border: none;
            border-radius: 40rpx;
            padding: 0 40rpx;
            height: 80rpx;
            line-height: 80rpx;
            font-size: 28rpx;
            font-weight: 500;
            box-shadow: 0 4rpx 12rpx rgba(137, 102, 239, 0.3);
            transition: all 0.3s ease;

            &:active {
                transform: scale(0.95);
            }
        }
    }
}
</style>
