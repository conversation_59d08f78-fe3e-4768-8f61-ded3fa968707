<template>
    <view class="empty-state" v-if="show">
        <!-- 空状态图片 -->
        <image 
            class="empty-image" 
            :src="image || defaultImage" 
            mode="aspectFit"
        ></image>
        
        <!-- 空状态文字 -->
        <view class="empty-text" v-if="text">{{ text }}</view>
        <view class="empty-tip" v-if="tip">{{ tip }}</view>
        
        <!-- 操作按钮 -->
        <view 
            class="empty-button" 
            :class="{ disabled: buttonDisabled }"
            v-if="buttonText" 
            @click="handleButtonClick"
        >
            <text>{{ buttonText }}</text>
        </view>
        
        <!-- 自定义插槽 -->
        <slot name="custom"></slot>
    </view>
</template>

<script>
export default {
    name: 'EmptyState',
    props: {
        // 是否显示空状态
        show: {
            type: Boolean,
            default: true
        },
        // 空状态图片
        image: {
            type: String,
            default: ''
        },
        // 主要文字
        text: {
            type: String,
            default: ''
        },
        // 提示文字
        tip: {
            type: String,
            default: ''
        },
        // 按钮文字
        buttonText: {
            type: String,
            default: ''
        },
        // 按钮是否禁用
        buttonDisabled: {
            type: Boolean,
            default: false
        },
        // 空状态类型，用于预设样式
        type: {
            type: String,
            default: 'default', // default, goods, order, message, search, cart, favorite
            validator: value => ['default', 'goods', 'order', 'message', 'search', 'cart', 'favorite'].includes(value)
        }
    },
    computed: {
        // 默认图片
        defaultImage() {
            const images = {
                default: 'https://static.wdaoyun.com/friend/2025-07-02/ef0fac060cb6f1abfde647fe1a55776d.png',
                goods: 'https://static.wdaoyun.com/friend/2025-07-02/ef0fac060cb6f1abfde647fe1a55776d.png',
                order: 'https://static.wdaoyun.com/friend/2025-07-02/ef0fac060cb6f1abfde647fe1a55776d.png',
                message: 'https://static.wdaoyun.com/friend/2025-07-02/ef0fac060cb6f1abfde647fe1a55776d.png',
                search: 'https://static.wdaoyun.com/friend/2025-07-02/ef0fac060cb6f1abfde647fe1a55776d.png',
                cart: 'https://static.wdaoyun.com/friend/2025-07-02/ef0fac060cb6f1abfde647fe1a55776d.png',
                favorite: 'https://static.wdaoyun.com/friend/2025-07-02/ef0fac060cb6f1abfde647fe1a55776d.png'
            };
            return images[this.type] || images.default;
        }
    },
    methods: {
        // 处理按钮点击
        handleButtonClick() {
            if (!this.buttonDisabled) {
                this.$emit('button-click');
            }
        }
    }
};
</script>

<style lang="scss" scoped>
// 引入项目主题色变量
$theme-primary: #8966ef;
$theme-primary-light: #a584f2;
$theme-secondary: #f0cc6c;
$border-color: #f0f0f0;
$text-color: #333;
$text-gray: #666;
$text-light: #999;
$bg-gray: #f7f8f7;

.empty-state {
    padding: 100rpx 30rpx;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400rpx;

    .empty-image {
        width: 400rpx;
        height: 314rpx;
        margin-bottom: 40rpx;
        opacity: 0.8;
    }

    .empty-text {
        color: $text-gray;
        font-size: 32rpx;
        margin-bottom: 16rpx;
        text-align: center;
        line-height: 1.4;
    }

    .empty-tip {
        color: $theme-primary;
        font-size: 28rpx;
        text-align: center;
        line-height: 1.4;
        margin-bottom: 20rpx;
    }

    .empty-button {
        width: 100%;
        max-width: 400rpx;
        height: 100rpx;
        background: linear-gradient(135deg, $theme-primary, $theme-primary-light);
        border-radius: 50rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 40rpx;
        box-shadow: 0 6rpx 20rpx rgba(137, 102, 239, 0.3);
        transition: all 0.3s ease;

        text {
            font-size: 32rpx;
            color: #ffffff;
            font-weight: 600;
        }

        &:active {
            transform: translateY(2rpx);
            box-shadow: 0 4rpx 15rpx rgba(137, 102, 239, 0.4);
        }

        &.disabled {
            background: linear-gradient(135deg, #d6d7d8, #e0e1e2);
            box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

            text {
                color: #999;
            }

            &:active {
                transform: none;
            }
        }
    }
}
</style>
