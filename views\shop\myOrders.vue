<template>
    <view class="my-orders-page">
        <!-- 页面标题 -->
        <view class="page-header">
            <view class="header-content">
                <text class="page-title">我的订单</text>
                <text class="page-subtitle">查看我购买的商品订单</text>
            </view>
            <view class="header-decoration">
                <view class="decoration-circle circle-1"></view>
                <view class="decoration-circle circle-2"></view>
            </view>
        </view>

        <!-- 订单状态筛选 -->
        <view class="filter-tabs">
            <view class="tab-item" :class="{ active: currentStatus === item.value }" v-for="item in statusTabs" :key="item.value" @click="switchStatus(item.value)">
                {{ item.label }}
                <view class="tab-badge" v-if="item.count > 0">{{ item.count }}</view>
            </view>
        </view>

        <!-- 订单列表 -->
        <view class="orders-list">
            <view class="order-item" v-for="(order, index) in orderList" :key="order.id" @click="goOrderDetail(order)">
                <!-- 订单头部信息 -->
                <view class="order-header">
                    <view class="order-info">
                        <view class="order-no">订单号：{{ order.order_no }}</view>
                        <view class="order-time">{{ formatTime(order.addtime) }}</view>
                    </view>
                    <view class="order-status" :class="[getStatusClass(order.ms_status, order.cancel_time)]">
                        {{ getStatusText(order.ms_status, order.cancel_time) }}
                    </view>
                </view>

                <!-- 商品信息 -->
                <view class="goods-info">
                    <view class="goods-image">
                        <u-image width="120rpx" height="120rpx" :src="order.good.cover_img || order.sku.image" mode="aspectFill" border-radius="12rpx"></u-image>
                    </view>
                    <view class="goods-detail">
                        <view class="goods-title">{{ order.good.title || '商品已删除' }}</view>
                        <view class="goods-spec" v-if="order.sku.sku_name">规格：{{ order.sku.sku_name }}</view>
                        <view class="goods-price-qty">
                            <text class="price">¥{{ order.amount }}</text>
                            <text class="quantity">×{{ order.num }}</text>
                        </view>
                    </view>
                </view>

                <!-- 订单备注 -->
                <view class="order-note" v-if="order.buy_note">
                    <view class="note-label">
                        <u-icon name="edit-pen" size="24" color="#666"></u-icon>
                        <text>订单备注</text>
                    </view>
                    <view class="note-content">{{ order.buy_note }}</view>
                </view>

                <!-- 物流信息 -->
                <view class="logistics-info" v-if="order.wu_no && order.status >= '3'">
                    <view class="logistics-label">
                        <u-icon name="car" size="24" color="#666"></u-icon>
                        <text>物流信息</text>
                    </view>
                    <view class="logistics-detail">
                        <text class="tracking-no">快递单号：{{ order.wu_no }}</text>
                        <text class="logistics-status">{{ getLogisticsStatus(order.status) }}</text>
                    </view>
                </view>

                <!-- 订单操作按钮 -->
                <view class="order-actions" @click.stop>
                    <view class="action-btn secondary" v-if="order.web_user_id != userInfo.uid" @click="contactSeller(order)">联系卖家</view>
                    <view class="action-btn primary" v-if="Number(order.ms_status) === 1 && !order.cancel_time" @click="payOrder(order)">立即付款</view>
                    <view class="action-btn primary" v-if="Number(order.ms_status) === 3" @click="confirmReceipt(order)">确认收货</view>
                    <view class="action-btn info" v-if="order.wu_no" @click="viewLogistics(order)">查看物流</view>
                    <view class="action-btn warning" v-if="canRefund(order.ms_status, order.cancel_time)" @click="requestRefund(order)">申请退款</view>
                    <!-- 发货后需要退款时提示联系商家 -->
                    <view class="action-btn secondary" v-if="needContactSellerForRefund(order.ms_status, order.cancel_time)" @click="contactSellerForRefund(order)">联系商家退款</view>
                </view>
            </view>
        </view>

        <!-- 空状态 -->
        <empty-state :show="orderList.length === 0 && !loading" type="order" :tip="'暂无订单'"></empty-state>

        <!-- 加载更多 -->
        <u-loadmore :status="loadmoreStatus" @loadmore="loadMore" v-if="orderList.length > 0"></u-loadmore>
    </view>
</template>

<script>
import { mapState } from 'vuex';
import { getUserGoodOrderList, userApplyRefund, userConfirmGood } from '@/utils/vmeitime-http/shop.js';
import EmptyState from '@/components/empty-state.vue';

export default {
    components: {
        EmptyState,
    },
    data() {
        return {
            // 订单列表数据
            orderList: [],
            currentStatus: '0', // 当前筛选状态

            // 分页数据
            page: 1,
            pagesize: 10,
            hasMore: true,
            loading: false,
            loadmoreStatus: 'loadmore',

            // 状态标签
            statusTabs: [
                { label: '全部', value: '0', count: 0 },
                { label: '待付款', value: '1', count: 0 },
                { label: '待发货', value: '2', count: 0 },
                { label: '待收货', value: '3', count: 0 },
                { label: '已完成', value: '5', count: 0 },
                { label: '退款', value: '4', count: 0 },
            ],
        };
    },
    computed: {
        ...mapState(['userInfo']),
    },
    onLoad() {
        this.initPage();
        this.$api.getShopGoodOrderDetail({
            access_token: uni.getStorageSync('token'),
            order_id: 114,
        });
    },
    onShow() {
        this.refreshList();
    },
    onPullDownRefresh() {
        this.refreshList();
    },
    onReachBottom() {
        this.loadMore();
    },
    methods: {
        // 初始化页面
        initPage() {
            this.loadOrderList();
        },

        // 刷新列表
        refreshList() {
            this.page = 1;
            this.hasMore = true;
            this.orderList = [];
            this.loadOrderList();
            uni.stopPullDownRefresh();
        },

        // 加载订单列表
        async loadOrderList() {
            if (this.loading || !this.hasMore) return;

            this.loading = true;
            this.loadmoreStatus = 'loading';

            try {
                const res = await getUserGoodOrderList({
                    access_token: uni.getStorageSync('token'),
                    ms_status: this.currentStatus,
                    page: this.page,
                    pagesize: this.pagesize,
                });

                if (res.status === 0) {
                    const newList = res.data.list || [];

                    if (this.page === 1) {
                        this.orderList = newList;
                    } else {
                        this.orderList.push(...newList);
                    }

                    // 更新状态标签计数
                    this.updateStatusCounts(res.data);

                    // 判断是否还有更多数据
                    this.hasMore = newList.length >= this.pagesize;
                    this.loadmoreStatus = this.hasMore ? 'loadmore' : 'nomore';
                } else {
                    uni.showToast({
                        title: res.msg || '加载失败',
                        icon: 'none',
                    });
                    this.loadmoreStatus = 'loadmore';
                }
            } catch (error) {
                console.error('加载订单列表失败:', error);
                uni.showToast({
                    title: '网络错误，请重试',
                    icon: 'none',
                });
                this.loadmoreStatus = 'loadmore';
            } finally {
                this.loading = false;
            }
        },

        // 加载更多
        loadMore() {
            if (this.hasMore && !this.loading) {
                this.page++;
                this.loadOrderList();
            }
        },

        // 切换状态筛选
        switchStatus(status) {
            if (this.currentStatus === status) return;

            this.currentStatus = status;
            this.refreshList();
        },

        // 更新状态标签计数
        updateStatusCounts(data) {
            // 如果API返回了各状态的计数，在这里更新
            // 这里是示例，实际需要根据API返回的数据结构调整
        },

        // 获取订单状态样式类
        getStatusClass(status, cancelTime) {
            // 如果是待付款状态且存在取消时间，则显示为已取消状态
            if (Number(status) === 1 && cancelTime) {
                return 'status-cancelled';
            }

            const classMap = {
                0: 'status-pending', // 待付款
                1: 'status-pending', // 待付款
                2: 'status-processing', // 待发货
                3: 'status-shipped', // 待收货
                5: 'status-completed', // 已完成
                4: 'status-refund', // 退款
            };
            return classMap[status] || 'status-unknown';
        },

        // 获取订单状态文本
        getStatusText(status, cancelTime) {
            // 如果是待付款状态且存在取消时间，则显示为已取消
            if (Number(status) === 1 && cancelTime) {
                return '已取消';
            }

            const textMap = {
                0: '待付款',
                1: '待付款',
                2: '待发货',
                3: '待收货',
                5: '已完成',
                4: '退款完成',
            };
            return textMap[status] || '未知状态';
        },

        // 获取物流状态文本
        getLogisticsStatus(status) {
            const statusMap = {
                3: '运输中',
                4: '已签收',
            };
            return statusMap[status] || '运输中';
        },

        // 判断是否可以申请退款
        canRefund(status, cancelTime) {
            status = Number(status);
            // 如果是已取消的订单，不能申请退款
            if (status === 1 && cancelTime) {
                return false;
            }
            // 用户只能在待发货状态申请退款，发货后不可申请退款
            return [2].includes(status);
        },

        // 判断是否需要联系商家退款（发货后的状态）
        needContactSellerForRefund(status, cancelTime) {
            status = Number(status);
            // 如果是已取消的订单，不需要联系商家退款
            if (status === 1 && cancelTime) {
                return false;
            }
            // 待收货、已完成状态需要联系商家退款
            return [3, 5].includes(status);
        },

        // 格式化时间
        formatTime(timeStr) {
            if (!timeStr) return '';
            const date = new Date(timeStr);
            const now = new Date();
            const diff = now - date;

            if (diff < 60000) return '刚刚';
            if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
            if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';

            return timeStr.split(' ')[0]; // 返回日期部分
        },

        // 跳转到订单详情
        goOrderDetail(order) {
            uni.navigateTo({
                url: `/views/shop/orderDetail?orderId=${order.id}&type=user`,
            });
        },

        // 联系卖家
        contactSeller(order) {
            uni.showModal({
                title: '联系卖家',
                content: '是否要联系卖家？',
                success: async res => {
                    if (res.confirm) {
                        console.log(order);
                        const infoRes = await this.$api.getOtherUserInfo({
                            access_token: uni.getStorageSync('token'),
                            to_user_id: order.web_user_id,
                        });
                        const info = infoRes.data;
                        // 这里可以跳转到聊天页面或显示卖家联系方式
                        const res = await this.$api.initSession({
                            access_token: uni.getStorageSync('token'),
                            to_uid: order.web_user_id,
                        });
                        const sessionId = res.data.msg_session_id;
                        // 导航到聊天页面
                        uni.navigateTo({
                            url: `/views/moments/chat?sessionId=${sessionId}&userId=${info.uid}&username=${encodeURIComponent(info.nickname)}&avatar=${encodeURIComponent(info.headimgurl || '')}`,
                        });
                    }
                },
            });
        },

        // 联系商家退款
        contactSellerForRefund(order) {
            uni.showModal({
                title: '联系商家退款',
                content: '商品已发货，如需退款请联系商家处理。是否要联系商家？',
                confirmText: '联系商家',
                cancelText: '取消',
                success: res => {
                    if (res.confirm) {
                        // 这里可以跳转到聊天页面或显示卖家联系方式
                        uni.showToast({
                            title: '功能开发中',
                            icon: 'none',
                        });
                    }
                },
            });
        },

        // 立即付款
        payOrder(order) {
            uni.showModal({
                title: '确认付款',
                content: `确认支付 ¥${order.amount}？`,
                success: async res => {
                    if (res.confirm) {
                        console.log(order);
                        try {
                            // 调用支付接口
                            const payRes = await this.$api.createMiniPay({
                                openid: this.userInfo.openid,
                                orderid: order.order_no,
                                access_token: uni.getStorageSync('token'),
                            });

                            // 调用微信支付
                            await this.requestPayment(payRes.data);
                            this.refreshList();
                            uni.hideLoading();
                            uni.showToast({
                                title: '支付成功',
                                icon: 'success',
                            });
                        } catch (error) {
                            uni.hideLoading();
                            uni.showToast({
                                title: '支付失败，请重试',
                                icon: 'none',
                            });
                        }
                    }
                },
            });
        },

        // 调用微信支付
        async requestPayment(paymentData) {
            return new Promise((resolve, reject) => {
                uni.requestPayment({
                    provider: 'wxpay',
                    timeStamp: paymentData.timeStamp,
                    nonceStr: paymentData.nonceStr,
                    package: paymentData.package,
                    signType: paymentData.signType,
                    paySign: paymentData.paySign,
                    success: res => {
                        console.log('支付成功:', res);
                        resolve(res);
                    },
                    fail: err => {
                        console.error('支付失败:', err);
                        reject(new Error('支付失败'));
                    },
                });
            });
        },

        // 确认收货
        confirmReceipt(order) {
            uni.showModal({
                title: '确认收货',
                content: '确认已收到商品？收货后订单将完成，无法撤销。',
                success: async res => {
                    if (res.confirm) {
                        await this.handleConfirmReceipt(order);
                    }
                },
            });
        },

        // 处理确认收货
        async handleConfirmReceipt(order) {
            uni.showLoading({
                title: '确认中...',
            });

            try {
                const res = await userConfirmGood({
                    access_token: uni.getStorageSync('token'),
                    order_id: order.id,
                });

                uni.hideLoading();

                if (res.status === 0) {
                    uni.showToast({
                        title: '确认收货成功',
                        icon: 'success',
                    });

                    // 刷新订单列表
                    this.refreshList();
                } else {
                    uni.showToast({
                        title: res.msg || '确认收货失败',
                        icon: 'none',
                    });
                }
            } catch (error) {
                uni.hideLoading();
                console.error('确认收货失败:', error);
                uni.showToast({
                    title: '网络错误，请重试',
                    icon: 'none',
                });
            }
        },

        // 查看物流
        viewLogistics(order) {
            uni.navigateTo({
                url: `/views/shop/logistics?orderId=${order.id}&trackingNo=${order.wu_no}`,
            });
        },

        // 申请退款
        requestRefund(order) {
            uni.showModal({
                title: '申请退款',
                content: `确认要对订单 ${order.order_no} 申请退款吗？\n退款金额：¥${order.amount}`,
                success: async res => {
                    if (res.confirm) {
                        await this.handleUserRefund(order);
                    }
                },
            });
        },

        // 处理用户退款申请
        async handleUserRefund(order) {
            uni.showLoading({
                title: '申请中...',
            });

            try {
                const res = await userApplyRefund({
                    access_token: uni.getStorageSync('token'),
                    order_id: order.id,
                });

                uni.hideLoading();

                if (res.status === 0) {
                    uni.showToast({
                        title: '退款申请成功',
                        icon: 'success',
                    });

                    // 刷新订单列表
                    this.refreshList();
                } else {
                    uni.showToast({
                        title: res.msg || '申请失败',
                        icon: 'none',
                    });
                }
            } catch (error) {
                uni.hideLoading();
                console.error('申请退款失败:', error);
                uni.showToast({
                    title: '网络错误，请重试',
                    icon: 'none',
                });
            }
        },
    },
};
</script>

<style lang="scss" scoped>
// 引入项目主题色
$theme-primary: #8966ef;
$theme-primary-light: #a584f2;
$theme-secondary: #f0cc6c;
$bg-grey: #f8f8f8;
$border-color: #f0f0f0;
$text-color: #333;
$text-color-light: #666;
$text-color-placeholder: #999;

.my-orders-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);
}

.page-header {
    position: relative;
    padding: 40rpx 30rpx 30rpx;
    background: linear-gradient(135deg, #8966ef, #a584f2);
    overflow: hidden;

    .header-content {
        position: relative;
        z-index: 2;

        .page-title {
            display: block;
            font-size: 48rpx;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8rpx;
        }

        .page-subtitle {
            display: block;
            font-size: 28rpx;
            color: rgba(255, 255, 255, 0.8);
        }
    }

    .header-decoration {
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        z-index: 1;

        .decoration-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);

            &.circle-1 {
                width: 120rpx;
                height: 120rpx;
                top: -30rpx;
                right: 50rpx;
                animation: float 6s ease-in-out infinite;
            }

            &.circle-2 {
                width: 80rpx;
                height: 80rpx;
                top: 60rpx;
                right: 150rpx;
                animation: float 4s ease-in-out infinite reverse;
            }
        }
    }
}

.filter-tabs {
    background: white;
    display: flex;
    padding: 0 20rpx;
    border-bottom: 1rpx solid $border-color;

    .tab-item {
        flex: 1;
        text-align: center;
        padding: 30rpx 0;
        font-size: 28rpx;
        color: $text-color-light;
        position: relative;
        transition: all 0.3s ease;

        &.active {
            color: $theme-primary;
            font-weight: 500;

            &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 60rpx;
                height: 4rpx;
                background: $theme-primary;
                border-radius: 2rpx;
            }
        }

        .tab-badge {
            position: absolute;
            top: 15rpx;
            right: 20rpx;
            background: #ff4757;
            color: white;
            font-size: 20rpx;
            padding: 2rpx 8rpx;
            border-radius: 10rpx;
            min-width: 32rpx;
            text-align: center;
            line-height: 1.2;
        }
    }
}

.orders-list {
    padding: 20rpx;
}

.order-item {
    background: white;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    border: 1rpx solid $border-color;
    transition: all 0.3s ease;

    &:active {
        transform: scale(0.98);
        box-shadow: 0 4rpx 20rpx rgba(137, 102, 239, 0.1);
    }
}

.order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 30rpx;
    border-bottom: 1rpx solid #f5f5f5;

    .order-info {
        flex: 1;

        .order-no {
            font-size: 28rpx;
            color: $text-color;
            font-weight: 500;
            margin-bottom: 8rpx;
        }

        .order-time {
            font-size: 24rpx;
            color: $text-color-placeholder;
        }
    }

    .order-status {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 500;

        &.status-pending {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
        }

        &.status-processing {
            background: rgba(137, 102, 239, 0.1);
            color: $theme-primary;
        }

        &.status-shipped {
            background: rgba(76, 217, 100, 0.1);
            color: #4cd964;
        }

        &.status-completed {
            background: rgba(76, 217, 100, 0.1);
            color: #4cd964;
        }

        &.status-refund {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        &.status-cancelled {
            background: rgba(108, 117, 125, 0.1);
            color: #6c757d;
        }
    }
}

.goods-info {
    display: flex;
    padding: 24rpx 30rpx;
    border-bottom: 1rpx solid #f5f5f5;

    .goods-image {
        margin-right: 24rpx;
    }

    .goods-detail {
        flex: 1;

        .goods-title {
            font-size: 30rpx;
            color: $text-color;
            font-weight: 500;
            margin-bottom: 12rpx;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
        }

        .goods-spec {
            font-size: 24rpx;
            color: $text-color-light;
            margin-bottom: 16rpx;
            padding: 4rpx 12rpx;
            background: #f8f9fa;
            border-radius: 8rpx;
            display: inline-block;
        }

        .goods-price-qty {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .price {
                font-size: 32rpx;
                color: #ff4757;
                font-weight: 600;
            }

            .quantity {
                font-size: 28rpx;
                color: $text-color-light;
            }
        }
    }
}

.order-note {
    padding: 20rpx 30rpx;
    border-bottom: 1rpx solid #f5f5f5;
    background: rgba(137, 102, 239, 0.02);

    .note-label {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;

        text {
            margin-left: 8rpx;
            font-size: 26rpx;
            color: $text-color-light;
        }
    }

    .note-content {
        font-size: 28rpx;
        color: $text-color;
        line-height: 1.5;
        padding: 16rpx 20rpx;
        // background: #ffffff;
        border-radius: 12rpx;
        // border-left: 4rpx solid $theme-primary;
    }
}

.logistics-info {
    padding: 20rpx 30rpx;
    border-bottom: 1rpx solid #f5f5f5;
    background: #f8f9fa;

    .logistics-label {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;

        text {
            margin-left: 8rpx;
            font-size: 26rpx;
            color: $text-color-light;
        }
    }

    .logistics-detail {
        .tracking-no {
            font-size: 26rpx;
            color: $text-color;
            margin-right: 20rpx;
        }

        .logistics-status {
            font-size: 26rpx;
            color: $theme-primary;
            font-weight: 500;
        }
    }
}

.order-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 20rpx 30rpx;
    gap: 20rpx;
    flex-wrap: wrap;

    .action-btn {
        padding: 12rpx 24rpx;
        border-radius: 20rpx;
        font-size: 26rpx;
        font-weight: 500;
        border: 1rpx solid;
        transition: all 0.3s ease;

        &.primary {
            background: $theme-primary;
            color: white;
            border-color: $theme-primary;

            &:active {
                background: darken($theme-primary, 10%);
            }
        }

        &.secondary {
            background: white;
            color: $theme-primary;
            border-color: $theme-primary;

            &:active {
                background: rgba(137, 102, 239, 0.1);
            }
        }

        &.info {
            background: white;
            color: #17a2b8;
            border-color: #17a2b8;

            &:active {
                background: rgba(23, 162, 184, 0.1);
            }
        }

        &.warning {
            background: white;
            color: #f0ad4e;
            border-color: #f0ad4e;

            &:active {
                background: rgba(240, 173, 78, 0.1);
            }
        }
    }
}

.empty-state {
    padding: 100rpx 0;
}
// 浮动动画
@keyframes float {
    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20rpx) rotate(180deg);
    }
}
</style>
