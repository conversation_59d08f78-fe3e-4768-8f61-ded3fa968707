const http = uni.$u.http;
/**
 * 获取用户产品列表
 * @param {Object} d 请求参数
 * @param {Number} d.web_user_id 需要查看人的id 没有默认查看自己
 * @param {Number} d.status 默认 0所有 1上架 2下架 如果状态等于3不可操作
 * @param {Number} d.page 分页
 * @param {Number} d.pagesize 分页数量
 * @param {access_token} d.access_token 用户token
*/
export const getUserGoodList = d => http.post('?c=Userorder&a=get_user_good_list', d);

/**
 * 获取用户产品详情
 * @param {Object} d 请求参数
 * @param {Number} d.good_id 产品id
 * @param {access_token} d.access_token 用户token
*/
export const getUserGoodDetail = d => http.post('?c=Userorder&a=get_good_info', d);

/**
 * 用户添加产品
 * @param {Object} d 请求参数
 * @param {Number} d.cover_img 封面
 * @param {Number} d.images 图片集合 JSON.stringify([图片集合])
 * @param {Number} d.title 分页
 * @param {Number} d.content 我是产品内容
 * @param {Number} d.status 状态 1发布 2下架
 * @param {Number} d.sku_json sku对象集合 price 价格 org_price 原价 image图片 sku_name 标题 [{“price”:19.99,”org_price”:100,”sku_name”:”规格001”,”image”:”http://www.google.com"},{"price":29.99,"org_price":100,"sku_name":"规格002","image":"http://www.google.com"}]
 * @param {access_token} d.access_token 用户token
*/
export const addUserGood = d => http.post('?c=Userorder&a=add_good', d);

/**
 * 用户编辑产品
 * @param {Object} d 请求参数
 * @param {Number} d.user_good_id 产品id
 * @param {Number} d.cover_img 封面
 * @param {Number} d.images 图片集合 JSON.stringify([图片集合])
 * @param {Number} d.title 分页
 * @param {Number} d.content 我是产品内容
 * @param {Number} d.status 状态 1发布 2下架
 * @param {Number} d.sku_json sku对象集合 price 价格 org_price 原价 image图片 sku_name 标题 [{“price”:19.99,”org_price”:100,”sku_name”:”规格001”,”image”:”http://www.google.com"},{"price":29.99,"org_price":100,"sku_name":"规格002","image":"http://www.google.com"}]
 * @param {access_token} d.access_token 用户token
*/
export const upUserGood = d => http.post('?c=Userorder&a=up_good', d);

/**
 * 用户创建橱窗产品订单
 * @param {Object} d 请求参数
 * @param {Number} d.tj_user_id 推荐的人id
 * @param {Number} d.good_sku_id sku_id
 * @param {Number} d.num 数量
 * @param {Number} d.user_address_id 地址id
 * @param {access_token} d.access_token 用户token
*/
export const createUserGoodOrder = d => http.post('?c=Userorder&a=create_user_good_order', d);
/**
 * 获取我的橱窗订单列表
 * @param {Object} d 请求参数
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
 * @param {Number} d.ms_status 状态 1 代付款 2 代发货 3等待收货 4 退款
 * @param {access_token} d.access_token 用户token
*/
export const getUserGoodOrderList = d => http.post('?c=Userorder&a=my_order_sku_good_list', d);
/**
 * 商户橱窗店铺订单
 * @param {Object} d 请求参数
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
 * @param {Number} d.ms_status 状态 1 代付款 2 代发货 3等待收货 4 退款
 * @param {access_token} d.access_token 用户token
*/
export const getShopGoodOrderList = d => http.post('?c=Userorder&a=my_store_order_sku_good_list', d);

/**
 * 获取橱窗订单详情
 * @param {Object} d 请求参数
 * @param {Number} d.order_id 订单id
 * @param {access_token} d.access_token 用户token
*/
export const getShopGoodOrderDetail = d => http.post('?c=Userorder&a=get_user_order_info', d);

/**
 * 检查物流单号物流信息
 * @param {Object} d 请求参数
 * @param {Number} d.order_id 订单id
 * @param {access_token} d.access_token 用户token
 * @param {Number} d.wu_no 物流单号
*/
export const checkWuNo = d => http.post('?c=Userorder&a=find_wu_no_info', d);

/**
 * 识别成功发货
 * @param {Object} d 请求参数
 * @param {Number} d.order_id 订单id
 * @param {access_token} d.access_token 用户token
 * @param {Number} d.wu_no 物流单号
*/
export const sendGood = d => http.post('?c=Userorder&a=store_order_wu', d);

/**
 * 数据统计
 * @param {Object} d 请求参数
 * @param {access_token} d.access_token 用户token
*/
export const getShopData  = d => http.post('?c=Userorder&a=data_index', d);

/**
 * 商家申请退款
 * @param {Object} d 请求参数
 * @param {Number} d.order_id 订单id
 * @param {access_token} d.access_token 用户token
*/
export const storeApplyRefund = d => http.post('?c=Userorder&a=order_refund_store', d);

/**
 * 用户申请退款
 * @param {Object} d 请求参数
 * @param {Number} d.order_id 订单id
 * @param {access_token} d.access_token 用户token
*/
export const userApplyRefund = d => http.post('?c=Userorder&a=order_refund_user', d);

/**
 * 用户确认收货
 * 
 * @param {Object} d 请求参数
 * @param {Number} d.order_id 订单id
 * @param {access_token} d.access_token 用户token
*/
export const userConfirmGood = d => http.post('?c=Userorder&a=user_confirm_wl', d);

/**
 * 产品上下架
 * 
 * @param {Object} d 请求参数
 * @param {Number} d.user_good_id 产品id
 * @param {Number} d.status 1上架 2 下架
 * @param {access_token} d.access_token 用户token
*/
export const upGoodStatus = d => http.post('?c=Userorder&a=up_good_status', d);

/**
 * 获取所有商品
 * @param {Object} d 请求参数
 * @param {access_token} d.access_token 用户token 
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
*/
export const getAllShopGoodList = d => http.post('?c=index&a=all_good_list', d);   

/**
 * 提现商户订单创建
 * @param {Object} d 请求参数
 * @param {Number} d.amount 单位元
 * @param {access_token} d.access_token 用户token
*/
export const createWithdrawShopOrder = d => http.post('?c=Order&a=create_salt_good_order', d);

export default {
    getUserGoodList,
    getUserGoodDetail,
    addUserGood,
    upUserGood,
    createUserGoodOrder,
    getUserGoodOrderList,
    getShopGoodOrderList,
    getShopGoodOrderDetail,
    checkWuNo,
    sendGood,
    getShopData,
    storeApplyRefund,
    userApplyRefund,
    userConfirmGood,
    upGoodStatus,
    getAllShopGoodList,
    createWithdrawShopOrder
};
