<template>
    <view class="page-container">
        <!-- 商品基本信息 -->
        <view class="card-container">
            <view class="card-header">
                <image src="https://friend.wdaoyun.cn/static/images/index/publish_title_content.png"></image>
                <!-- <u-icon name="info-circle" size="32" color="#8966ef"></u-icon> -->
                <text class="card-title">商品信息</text>
            </view>

            <!-- 商品标题 -->
            <view class="form-item">
                <view class="form-label">商品标题</view>
                <u-input
                    v-model="title"
                    maxlength="60"
                    placeholder="请输入商品标题"
                    placeholder-style="color: #999;"
                    border="none"
                    custom-style="background: #f8f9fa; border-radius: 12rpx; padding: 24rpx;"
                />
            </view>

            <!-- 商品内容 -->
            <view class="form-item">
                <view class="form-label">商品描述</view>
                <textarea class="content-textarea" placeholder="请输入商品描述" v-model="content" maxlength="500"></textarea>
            </view>

            <!-- 封面图片 -->
            <view class="form-item">
                <view class="form-label">封面图片</view>
                <view class="cover-upload">
                    <view v-if="!coverImg" class="upload-btn" @click="uploadCoverImg">
                        <u-icon name="camera" size="48" color="#8966ef"></u-icon>
                        <text>上传封面</text>
                    </view>
                    <image v-else class="cover-img" :src="coverImg" mode="aspectFill" @click="uploadCoverImg"></image>
                </view>
            </view>

            <!-- 商品图片 -->
            <view class="form-item">
                <view class="form-label">商品图片</view>
                <shmily-drag-image number="9" imageWidth="200" ref="dragImage" :list.sync="images" :custom="true" @addImage="addImages"></shmily-drag-image>
            </view>
        </view>

        <!-- SKU规格信息 -->
        <view class="card-container">
            <view class="card-header">
                <!-- <u-icon name="grid" size="32" color="#8966ef"></u-icon> -->
                <image src="https://friend.wdaoyun.cn/static/images/index/publish_title_goods.png"></image>
                <text class="card-title">SKU规格</text>
            </view>

            <view class="sku-list">
                <view class="sku-item" v-for="(item, index) in skuList" :key="index">
                    <view class="sku-header">
                        <view class="sku-index">
                            <u-icon name="tag" size="24" color="#8966ef"></u-icon>
                            <text>SKU {{ index + 1 }}</text>
                        </view>
                        <view class="sku-actions" v-if="skuList.length > 1">
                            <u-button type="error" size="mini" plain @click="delSkuItem(index)" custom-style="border-radius: 20rpx;">删除</u-button>
                        </view>
                    </view>

                    <view class="sku-form">
                        <view class="form-row">
                            <view class="form-label">规格名称</view>
                            <input class="form-input" type="text" v-model="item.sku_name" placeholder="请输入规格名称" maxlength="20" />
                        </view>
                        <view class="form-row">
                            <view class="form-label">销售价格</view>
                            <input class="form-input" type="digit" v-model.trim="item.price" placeholder="请输入价格(最低1元)" />
                        </view>
                        <view class="form-row">
                            <view class="form-label">原价</view>
                            <input class="form-input" type="digit" v-model.trim="item.org_price" placeholder="请输入原价" />
                        </view>
                        <view class="form-row">
                            <view class="form-label">规格图片</view>
                            <view class="sku-img-upload">
                                <view v-if="!item.image" class="upload-btn-small" @click="uploadSkuImg(index)">
                                    <u-icon name="camera" size="32" color="#8966ef"></u-icon>
                                </view>
                                <image v-else class="sku-img" :src="item.image" mode="aspectFill" @click="uploadSkuImg(index)"></image>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="add-sku-btn" @click="addSkuItem">
                    <u-icon name="plus" size="32" color="#8966ef"></u-icon>
                    <text>添加SKU</text>
                </view>
            </view>
        </view>

        <!-- 发布按钮 -->
        <view class="button-container">
            <view class="button-group">
                <!-- <view class="draft-btn" @click="saveDraft">
                    <u-icon name="bookmark" size="32" color="#8966ef"></u-icon>
                    <text>保存草稿</text>
                </view> -->
                <view class="publish-btn" @click="publishProduct" :class="{ loading: isPublishing }">
                    <!-- <u-icon v-if="!isPublishing" name="checkmark-circle" size="32" color="#fff"></u-icon> -->
                    <!-- <u-loading-icon v-if="isPublishing" color="#fff" size="32"></u-loading-icon> -->
                    <text>{{ publishType === 'edit' ? '更新商品' : '发布商品' }}</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
import shmilyDragImage from '@/components/shmily-drag-image/shmily-drag-image.vue';
import { addUserGood, upUserGood, getUserGoodDetail } from '@/utils/vmeitime-http/shop.js';
export default {
    components: {
        shmilyDragImage,
    },
    computed: {
        ...mapState(['userInfo']),
    },
    data() {
        return {
            publishType: '', // edit修改 draft草稿
            user_good_id: '', // 商品ID，编辑时使用
            title: '', // 商品标题
            content: '', // 商品内容
            coverImg: '', // 封面图片
            images: [], // 商品图片集合
            status: 1, // 状态 1发布 2下架
            skuList: [
                {
                    sku_name: '', // 规格名称
                    price: '', // 价格
                    org_price: '', // 原价
                    image: '', // 规格图片
                },
            ],
            isPublishing: false, // 是否正在发布中
        };
    },
    async onLoad(option) {
        if (option.type === 'edit' && option.id) {
            this.publishType = 'edit';
            this.user_good_id = option.id;
            this.loadProductData(option.id);
        }
        uni.$on('uAvatarCropper', async path => {
            await this.uploadFile(path, 'cover');
        });
    },
    onUnload() {
        // 清理资源
        uni.$off('uAvatarCropper');
    },
    methods: {
        // 上传封面图片
        uploadCoverImg() {
            uni.navigateTo({
                url: `./uAvatarCropper?destWidth=346&rectWidth=350&fileType=jpg`,
            });
            // uni.chooseImage({
            //     count: 1,
            //     sizeType: ['compressed'],
            //     success: res => {
            //         this.uploadFile(res.tempFilePaths[0], 'cover');
            //     },
            // });
        },

        // 添加商品图片
        addImages() {
            let num = this.images.length;
            uni.chooseImage({
                count: 9 - num,
                sizeType: ['compressed'],
                success: async res => {
                    for (let i = 0; i < res.tempFilePaths.length; i++) {
                        await this.uploadFile(res.tempFilePaths[i], 'images');
                    }
                },
            });
        },

        // 上传SKU图片
        uploadSkuImg(index) {
            uni.chooseImage({
                count: 1,
                sizeType: ['compressed'],
                success: res => {
                    this.uploadFile(res.tempFilePaths[0], 'sku', index);
                },
            });
        },
        // 上传文件
        uploadFile(path, type, index) {
            return new Promise((resolve, reject) => {
                uni.showLoading({
                    title: '上传中',
                });
                uni.uploadFile({
                    url: 'https://friend.wdaoyun.cn/upload/uploadfile.php',
                    filePath: path,
                    name: 'upload',
                    header: {
                        'Content-Type': 'multipart/form-data',
                        Authorization: '67947632acd5ac684574488b140b40400c74b695',
                    },
                    formData: {
                        access_token: uni.getStorageSync('token'),
                        type: 1,
                    },
                    success: res => {
                        uni.hideLoading();
                        if (res.statusCode == 200) {
                            const result = JSON.parse(res.data);
                            const imgUrl = result.data.img_url;

                            switch (type) {
                                case 'cover':
                                    this.coverImg = imgUrl;
                                    break;
                                case 'images':
                                    this.$nextTick(() => {
                                        this.$refs.dragImage.addImage(imgUrl);
                                    });
                                    break;
                                case 'sku':
                                    this.skuList[index].image = imgUrl;
                                    break;
                            }
                            resolve(imgUrl);
                        } else {
                            reject(new Error('上传失败'));
                        }
                    },
                    fail: err => {
                        uni.hideLoading();
                        uni.showToast({
                            title: '上传失败，请重试',
                            icon: 'none',
                        });
                        reject(err);
                    },
                });
            });
        },

        // SKU操作相关
        addSkuItem() {
            this.skuList.push({
                sku_name: '',
                price: '',
                org_price: '',
                image: '',
                sku_id: 0,
            });
        },

        delSkuItem(index) {
            if (this.skuList.length > 1) {
                this.skuList.splice(index, 1);
            } else {
                uni.showToast({
                    title: '至少保留一个SKU',
                    icon: 'none',
                });
            }
        },

        // 加载商品数据（编辑时使用）
        async loadProductData(id) {
            try {
                const res = await this.$api.getUserGoodDetail({
                    good_id: id,
                    access_token: uni.getStorageSync('token'),
                });

                if (res.status === 0 && res.data) {
                    const data = res.data.good;
                    this.title = data.title;
                    this.content = data.content;
                    this.coverImg = data.cover_img;
                    this.images = JSON.parse(data.images || '[]');
                    this.status = data.status;
                    this.skuList = data.sku_list.map(item => {
                        return {
                            sku_name: item.sku_name,
                            price: item.price,
                            org_price: item.org_price,
                            image: item.image,
                            sku_id: item.id,
                        };
                    });
                }
            } catch (e) {
                console.error('获取商品数据失败', e);
                uni.showToast({
                    title: '获取商品数据失败',
                    icon: 'none',
                });
            }
        },

        // 表单验证
        validateForm() {
            if (!this.title.trim()) {
                uni.showToast({
                    title: '请输入商品标题',
                    icon: 'none',
                });
                return false;
            }

            if (!this.content.trim()) {
                uni.showToast({
                    title: '请输入商品描述',
                    icon: 'none',
                });
                return false;
            }

            if (!this.coverImg) {
                uni.showToast({
                    title: '请上传封面图片',
                    icon: 'none',
                });
                return false;
            }

            if (!this.images || this.images.length === 0) {
                uni.showToast({
                    title: '请上传商品图片',
                    icon: 'none',
                });
                return false;
            }

            // 验证SKU
            for (let i = 0; i < this.skuList.length; i++) {
                const sku = this.skuList[i];
                if (!sku.sku_name.trim()) {
                    uni.showToast({
                        title: `请输入第${i + 1}个SKU的规格名称`,
                        icon: 'none',
                    });
                    return false;
                }

                if (!sku.price || Number(sku.price) < 1) {
                    uni.showToast({
                        title: `商品价格不能低于1元`,
                        icon: 'none',
                    });
                    return false;
                }

                if (!sku.org_price || Number(sku.org_price) <= 0) {
                    uni.showToast({
                        title: `请输入第${i + 1}个SKU的原价`,
                        icon: 'none',
                    });
                    return false;
                }

                if (!sku.image) {
                    uni.showToast({
                        title: `请上传第${i + 1}个SKU的规格图片`,
                        icon: 'none',
                    });
                    return false;
                }
            }

            return true;
        },

        // 保存草稿
        saveDraft() {
            if (!this.title.trim()) {
                uni.showToast({
                    title: '请输入商品标题后再保存草稿',
                    icon: 'none',
                });
                return;
            }

            const draftData = {
                title: this.title,
                content: this.content,
                coverImg: this.coverImg,
                images: this.images,
                skuList: this.skuList,
                status: this.status,
            };

            uni.setStorage({
                key: 'productDraft',
                data: draftData,
                success: () => {
                    uni.showToast({
                        title: '草稿保存成功',
                        icon: 'success',
                    });
                },
            });
        },

        // 发布商品
        async publishProduct() {
            if (this.isPublishing) return;

            if (!this.validateForm()) {
                return;
            }

            this.isPublishing = true;

            try {
                const params = {
                    title: this.title,
                    content: this.content,
                    cover_img: this.coverImg,
                    images: JSON.stringify(this.images),
                    status: this.status,
                    sku_json: JSON.stringify(this.skuList),
                    access_token: uni.getStorageSync('token'),
                };

                let result;
                if (this.publishType === 'edit') {
                    // 编辑商品
                    params.user_good_id = this.user_good_id;
                    result = await upUserGood(params);
                } else {
                    // 新增商品
                    result = await addUserGood(params);
                }

                if (result.status === 0) {
                    uni.showToast({
                        title: this.publishType === 'edit' ? '更新成功' : '发布成功',
                        icon: 'success',
                    });

                    // 清除草稿
                    uni.removeStorageSync('productDraft');

                    setTimeout(() => {
                        uni.navigateBack();
                    }, 1500);
                } else {
                    throw new Error(result.msg || '操作失败');
                }
            } catch (e) {
                console.error('发布失败', e);
                uni.showToast({
                    title: e.message || '发布失败，请重试',
                    icon: 'none',
                });
            } finally {
                this.isPublishing = false;
            }
        },
        // 初始化时加载草稿数据
        loadDraftData() {
            uni.getStorage({
                key: 'productDraft',
                success: res => {
                    const data = res.data;
                    this.title = data.title || '';
                    this.content = data.content || '';
                    this.coverImg = data.coverImg || '';
                    this.images = data.images || [];
                    this.skuList = data.skuList || [
                        {
                            sku_name: '',
                            price: '',
                            org_price: '',
                            image: '',
                        },
                    ];
                    this.status = data.status || 1;
                },
                fail: () => {
                    // 没有草稿数据，使用默认值
                },
            });
        },
    },
};
</script>
<style lang="scss" scoped>
// 引入项目主题色
$theme-primary: #8966ef;
$theme-primary-light: #a584f2;
$theme-secondary: #f0cc6c;
$bg-grey: #f8f8f8;
$border-color: #f0f0f0;
$text-color: #333;
$text-color-light: #666;
$text-color-placeholder: #999;

.page-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);
    padding: 20rpx;
    box-sizing: border-box;
}

.card-container {
    background: #ffffff;
    border-radius: 16rpx;
    margin-bottom: 24rpx;
    padding: 32rpx;
    box-sizing: border-box;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    border: 1rpx solid #f5f5f5;

    .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 32rpx;
        padding-bottom: 24rpx;
        border-bottom: 1rpx solid $border-color;

        .card-title {
            font-size: 32rpx;
            font-weight: 600;
            color: $text-color;
            margin-left: 16rpx;
        }
        image {
            width: 36rpx;
            height: 36rpx;
        }
    }

    .form-item {
        margin-bottom: 32rpx;

        &:last-child {
            margin-bottom: 0;
        }

        .form-label {
            font-size: 28rpx;
            font-weight: 500;
            color: $text-color;
            margin-bottom: 16rpx;
            display: flex;
            align-items: center;
        }

        .content-textarea {
            width: 100%;
            height: 200rpx;
            background: #f8f9fa;
            border-radius: 12rpx;
            padding: 24rpx;
            box-sizing: border-box;
            font-size: 28rpx;
            color: $text-color;
            border: 1rpx solid transparent;
            transition: all 0.3s ease;

            &:focus {
                background: rgba($theme-primary, 0.05);
                border-color: $theme-primary;
                outline: none;
            }
        }

        .cover-upload {
            .upload-btn {
                width: 200rpx;
                height: 200rpx;
                background: #f8f9fa;
                border: 2rpx dashed #ddd;
                border-radius: 12rpx;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;

                &:active {
                    transform: scale(0.98);
                    border-color: $theme-primary;
                    background: rgba($theme-primary, 0.05);
                }

                text {
                    font-size: 24rpx;
                    color: $text-color-placeholder;
                    margin-top: 12rpx;
                }
            }

            .cover-img {
                width: 200rpx;
                height: 200rpx;
                border-radius: 12rpx;
                border: 2rpx solid $border-color;
            }
        }
    }
}

.sku-list {
    .sku-item {
        background: #f8f9fa;
        border-radius: 16rpx;
        padding: 24rpx;
        margin-bottom: 24rpx;
        border: 1rpx solid #f0f0f0;
        transition: all 0.3s ease;

        &:hover {
            box-shadow: 0 4rpx 12rpx rgba($theme-primary, 0.1);
        }

        .sku-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24rpx;

            .sku-index {
                display: flex;
                align-items: center;
                font-size: 28rpx;
                font-weight: 500;
                color: $theme-primary;

                text {
                    margin-left: 8rpx;
                }
            }
        }

        .sku-form {
            .form-row {
                display: flex;
                align-items: center;
                margin-bottom: 20rpx;

                &:last-child {
                    margin-bottom: 0;
                }

                .form-label {
                    width: 160rpx;
                    font-size: 26rpx;
                    color: $text-color;
                    font-weight: 500;
                }

                .form-input {
                    flex: 1;
                    height: 72rpx;
                    background: #ffffff;
                    border-radius: 12rpx;
                    padding: 0 20rpx;
                    font-size: 28rpx;
                    color: $text-color;
                    border: 1rpx solid #e8e8e8;
                    transition: all 0.3s ease;

                    &:focus {
                        border-color: $theme-primary;
                        background: rgba($theme-primary, 0.02);
                        outline: none;
                    }
                }

                .sku-img-upload {
                    .upload-btn-small {
                        width: 120rpx;
                        height: 120rpx;
                        background: #ffffff;
                        border: 2rpx dashed #ddd;
                        border-radius: 12rpx;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: all 0.3s ease;

                        &:active {
                            transform: scale(0.95);
                            border-color: $theme-primary;
                            background: rgba($theme-primary, 0.05);
                        }
                    }

                    .sku-img {
                        width: 120rpx;
                        height: 120rpx;
                        border-radius: 12rpx;
                        border: 1rpx solid #e8e8e8;
                    }
                }
            }
        }
    }

    .add-sku-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 88rpx;
        background: rgba($theme-primary, 0.05);
        border: 2rpx dashed $theme-primary;
        border-radius: 16rpx;
        color: $theme-primary;
        font-size: 28rpx;
        font-weight: 500;
        transition: all 0.3s ease;

        &:active {
            transform: scale(0.98);
            background: rgba($theme-primary, 0.1);
        }

        text {
            margin-left: 12rpx;
        }
    }
}

.button-container {
    // background: #ffffff;
    padding: 32rpx;
    margin-top: 40rpx;
    margin-bottom: 40rpx;

    .button-group {
        display: flex;
        gap: 24rpx;

        .draft-btn,
        .publish-btn {
            flex: 1;
            height: 88rpx;
            border-radius: 44rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 30rpx;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;

            text {
                margin-left: 12rpx;
            }

            &:active {
                transform: scale(0.98);
            }
        }

        .draft-btn {
            background: #ffffff;
            color: #8966ef;
            border: 2rpx solid #8966ef;

            &:active {
                background: rgba(#8966ef, 0.05);
            }
        }

        .publish-btn {
            background: linear-gradient(135deg, #8966ef 0%, #a584f2 100%);
            color: #ffffff;
            border: none;
            box-shadow: 0 6rpx 20rpx rgba(137, 102, 239, 0.3);

            &.loading {
                opacity: 0.8;
                pointer-events: none;
            }

            &:active {
                box-shadow: 0 4rpx 16rpx rgba(137, 102, 239, 0.4);
            }
        }
    }
}

.page-container {
    padding-bottom: 40rpx;
}
</style>
