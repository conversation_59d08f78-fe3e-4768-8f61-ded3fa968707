<template>
    <view class="content">
        <view class="address-list">
            <radio-group>
                <view class="address-item white-box" :class="{ 'address-check': checkId == item.id }" v-for="(item, index) in addressList" :key="index">
                    <view class="ai-top" @click="changeAddress(index)">
                        <view class="ai-msg">
                            <view class="title">{{ item.name }} {{ item.phone }}</view>
                            <view class="value">{{ item.province_txt + item.city_txt + item.area_txt }} - {{ item.address }}</view>
                        </view>
                        <view class="ai-btn" @click.stop="editAddress(index)">编辑</view>
                    </view>
                    <view class="ai-bottom">
                        <view @click="setDefault(index)">
                            <u-radio-group v-model="defaultId" active-color="#8966ef">
                                <u-radio :name="item.id">
                                    <text v-if="item.is_def == 1" class="default-text">默认</text>
                                    <text v-else class="set-default-text">设为默认</text>
                                </u-radio>
                            </u-radio-group>
                        </view>
                        <view class="del-btn" @click="delAddress(index)">删除</view>
                    </view>
                </view>
            </radio-group>
        </view>
        <view class="empty-content" v-if="addressList.length === 0">
            <image class="empty-image" src="https://static.wdaoyun.com/friend/2025-07-02/ef0fac060cb6f1abfde647fe1a55776d.png" mode="aspectFit"></image>
            <view class="empty-tip">暂无地址~</view>
        </view>
        <view class="add-btn">
            <view class="btn-item" @click="addAddress">
                <text>添加新地址</text>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            lastRoute: '',
            addressList: [],
            checkId: 0,
            defaultId: 0,
        };
    },

    onLoad(option) {
        if (option.id) {
            this.checkId = option.id;
        }
        this.getAddress();
        let pages = getCurrentPages();
        let lastPages = pages[pages.length - 2];
        this.lastRoute = lastPages.route;
        uni.$on('updateAddressList', res => {
            this.getAddress(res.id);
        });
    },
    onUnload() {
        uni.$off('updateAddressList');
    },

    methods: {
        getAddress(id) {
            this.$api
                .getUserAddressList({
                    access_token: uni.getStorageSync('token'),
                })
                .then(res => {
                    // 兼容不同的数据结构
                    this.addressList = res.data?.list || res.data || [];
                    if (this.addressList.length > 0) {
                        this.addressList.forEach(item => {
                            // 兼容不同的默认地址字段名
                            if (item.isdefault == 1 || item.is_def == 1) {
                                this.defaultId = item.id;
                            }
                        });
                        if (id) {
                            if (this.checkId) {
                                this.checkId = id;
                                const addressItem = this.addressList.find(addr => addr.id == id);
                                uni.$emit('updateAddressMsg', addressItem);
                            }
                        }
                    }
                })
                .catch(err => {
                    console.error('获取地址列表失败:', err);
                    uni.showToast({
                        title: '获取地址列表失败',
                        icon: 'none',
                    });
                });
        },
        changeAddress(index) {
            if (this.lastRoute != 'pages/mine/mine') {
                uni.$emit('updateAddressMsg', this.addressList[index]);
                uni.navigateBack();
            }
        },
        addAddress() {
            uni.navigateTo({
                url: './addressEdit',
            });
        },
        editAddress(index) {
            uni.navigateTo({
                url: './addressEdit?type=2&addressMsg=' + JSON.stringify(this.addressList[index]),
            });
        },
        setDefault(index) {
            this.$api
                .editAddress({
                    address_id: this.addressList[index].id,
                    access_token: uni.getStorageSync('token'),
                    is_def: '1',
                    name: this.addressList[index].name,
                    phone: this.addressList[index].phone,
                    province_code: this.addressList[index].province_code,
                    city_code: this.addressList[index].city_code,
                    area_code: this.addressList[index].area_code,
                    address: this.addressList[index].address,
                })
                .then(res => {
                    if (res.status == 0) {
                        uni.showToast({
                            title: '设置成功',
                        });
                        setTimeout(() => {
                            this.getAddress();
                        }, 600);
                    }
                });
        },
        async delAddress(index) {
            let delId = this.addressList[index].id;
            await uni.showModal({
                title: '确定删除该地址吗',
                success: async res => {
                    if (res.confirm) {
                        this.$api
                            .delAddress({
                                address_id: this.addressList[index].id,
                                access_token: uni.getStorageSync('token'),
                            })
                            .then(res => {
                                this.addressList.splice(index, 1);
                                uni.showToast({
                                    title: '删除成功',
                                });
                                if (delId == this.checkId) {
                                    if (this.addressList.length > 0) {
                                        this.checkId = this.addressList[0].id;
                                        uni.$emit('updateAddressMsg', this.addressList[0]);
                                    } else {
                                        uni.$emit('updateAddressMsg', {});
                                    }
                                }
                            });
                    }
                },
            });
        },
        getWxAddress() {
            uni.chooseAddress({
                success: res => {
                    console.log(res);
                    // 注意：这里需要根据实际的省市区代码来设置，目前暂时使用名称
                    this.$api
                        .createAddress({
                            name: res.userName,
                            phone: res.telNumber,
                            province_code: '', // 微信接口不提供代码，需要后续优化
                            city_code: '',
                            area_code: '',
                            address: res.provinceName + res.cityName + res.countyName + res.detailInfo,
                            is_def: '0',
                            access_token: uni.getStorageSync('token'),
                        })
                        .then(res => {
                            uni.showToast({
                                title: '添加成功',
                            });
                            if (this.checkId) {
                                this.checkId = res.data.id;
                                uni.$emit('updateAddressMsg', res.data);
                            }
                            setTimeout(() => {
                                this.getAddress();
                            }, 500);
                        });
                },
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.content {
    padding: 0 25rpx;
    box-sizing: border-box;
    padding-bottom: 150rpx;
    background-color: #f7f7f7;
    min-height: 100vh;
}
.address-list {
    padding-bottom: 100rpx;
    .address-check {
        border: 1px solid #8966ef;
        box-shadow: 0 0 10rpx rgba($color: #8966ef, $alpha: 0.3);
    }
    .address-item {
        margin: 25rpx 0;
        padding: 0 20rpx;
        box-sizing: border-box;
        background-color: #ffffff;
        border-radius: 12rpx;
        border: 1px solid #f0f0f0;
        .ai-top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            padding: 20rpx 0;
            .radio-btn {
                position: absolute;
                left: 0;
                top: 50%;
                transform: translateY(-50%);
            }
            .ai-msg {
                width: 500rpx;
                .title {
                    font-size: 34rpx;
                    color: #333333;
                    font-weight: 500;
                    margin-bottom: 15rpx;
                }
                .value {
                    font-size: 28rpx;
                    color: #666666;
                }
            }
            .ai-btn {
                font-size: 34rpx;
                color: #8966ef;
                padding: 8rpx 16rpx;
                border-radius: 6rpx;
                background-color: rgba(137, 102, 239, 0.1);
            }
        }
        .ai-bottom {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top: thin solid #f0f0f0;
            height: 80rpx;
            color: #777777;

            .default-text {
                color: #8966ef;
                font-weight: 500;
            }

            .set-default-text {
                color: #999999;
            }

            .del-btn {
                color: #ff4757;
                font-size: 28rpx;
            }
        }
    }
}
.add-btn {
    width: 100%;
    text-align: center;
    line-height: 88rpx;
    position: fixed;
    bottom: 20rpx;
    left: 0;
    display: flex;
    justify-content: center;
    padding: 20rpx 30rpx;
    box-sizing: border-box;
    border-top: thin solid #f0f0f0;
    background: #ffffff;
    padding-bottom: 0;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
    .btn-item {
        width: 48%;
        height: 88rpx;
        border-radius: 50rpx;
        font-size: 28rpx;
        color: #ffffff;
        background: linear-gradient(135deg, #8966ef, #a584f2);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4rpx 12rpx rgba(137, 102, 239, 0.3);
        image {
            width: 28rpx;
            height: 28rpx;
            margin-right: 10rpx;
        }
    }
    .get-wx-btn {
        color: #8966ef;
        border: 2rpx solid #8966ef;
        background: #ffffff;
        box-sizing: border-box;
        box-shadow: 0 4rpx 12rpx rgba(137, 102, 239, 0.15);
    }
    .iconfont {
        font-size: 28rpx;
        margin-right: 10rpx;
        font-weight: 500;
    }
}
.empty-content {
    // margin: 100rpx 30rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .empty-image {
        width: 400rpx;
        height: 314rpx;
        margin-bottom: 40rpx;
    }

    .empty-text {
        color: #999;
        font-size: 32rpx;
        margin-bottom: 16rpx;
        text-align: center;
    }

    .empty-tip {
        color: #8966ef;
        font-size: 28rpx;
        text-align: center;
    }
}
</style>
