<template>
	<view class="moments-container">
		<view class="moments-list">
			<MomentItem 
				v-for="item in momentsList" 
				:key="item.id" 
				:moment="item"
				@like="handleLike"
				@comment="handleComment"
				@detail="handleDetail"
			/>
		</view>
		
		<!-- 底部加载更多 -->
		<view class="moments-footer">
			<text class="load-more">没有更多内容了</text>
		</view>
		
		<!-- 悬浮发布按钮 -->
		<view class="publish-btn" @click="goPublish">
			<u-icon name="plus" color="#FFFFFF" size="28"></u-icon>
		</view>
	</view>
</template>

<script>
// 导入MomentItem组件
import MomentItem from '@/components/moments/MomentItem.vue';

export default {
	components: {
		MomentItem
	},
	data() {
		return {
			momentsList: []
		}
	},
	onLoad() {
		// 加载模拟数据
		this.loadMockData();
	},
	methods: {
		loadMockData() {
			// 模拟动态数据
			this.momentsList = [
				{
					id: '1',
					userName: '张三',
					userAvatar: '/static/images/we.png',
					createTime: '2023-06-15 10:30',
					content: '今天天气真好，出去走走放松一下心情！#心情 #阳光',
					images: [
						'http://static.wdaoyun.com/wdy/source/2025-05-26/图片9.jpg',
						'http://static.wdaoyun.com/wdy/source/2025-05-26/图片9.jpg'
					],
					location: '广州市天河区',
					isLiked: false,
					likeCount: 12,
					commentCount: 3,
					likes: [
						{ userId: '101', userName: '李四' },
						{ userId: '102', userName: '王五' }
					],
					comments: [
						{ id: '201', userId: '101', userName: '李四', content: '天气确实很好，羡慕啊！', createTime: '2023-06-15 10:35' },
						{ id: '202', userId: '102', userName: '王五', content: '照片拍得真不错！在哪里拍的？', createTime: '2023-06-15 10:40' },
						{ id: '203', userId: '103', userName: '赵六', content: '今天我也出去玩了，偶遇好心情~', createTime: '2023-06-15 11:05' }
					]
				},
				{
					id: '2',
					userName: '李四',
					userAvatar: '/static/images/we.png',
					createTime: '2023-06-14 18:45',
					content: '分享一个我最近做的小项目，花了两周时间，终于完成了！',
					images: [
						'http://static.wdaoyun.com/wdy/source/2025-05-26/图片9.jpg'
					],
					location: '',
					isLiked: true,
					likeCount: 25,
					commentCount: 5,
					likes: [
						{ userId: '100', userName: '张三' },
						{ userId: '102', userName: '王五' },
						{ userId: '103', userName: '赵六' }
					],
					comments: [
						{ id: '204', userId: '100', userName: '张三', content: '做得太棒了！', createTime: '2023-06-14 18:50' },
						{ id: '205', userId: '102', userName: '王五', content: '厉害，用了什么技术栈？', createTime: '2023-06-14 19:05' }
					]
				},
				{
					id: '3',
					userName: '王五',
					userAvatar: '/static/images/we.png',
					createTime: '2023-06-13 09:20',
					content: '新买的智能家居设备，安装好了，非常好用！推荐大家也试试~',
					images: [
						'http://static.wdaoyun.com/wdy/source/2025-05-26/图片9.jpg',
						'http://static.wdaoyun.com/wdy/source/2025-05-26/图片9.jpg',
						'http://static.wdaoyun.com/wdy/source/2025-05-26/图片9.jpg'
					],
					location: '深圳市南山区',
					isLiked: false,
					likeCount: 18,
					commentCount: 4,
					likes: [
						{ userId: '100', userName: '张三' },
						{ userId: '101', userName: '李四' }
					],
					comments: [
						{ id: '206', userId: '100', userName: '张三', content: '看起来不错，哪个品牌的？', createTime: '2023-06-13 09:30' },
						{ id: '207', userId: '101', userName: '李四', content: '我也想买一套，价格如何？', createTime: '2023-06-13 09:45' }
					]
				}
			];
		},
		
		// 点赞事件处理
		handleLike(momentId) {
			const index = this.momentsList.findIndex(item => item.id === momentId);
			if (index !== -1) {
				const moment = this.momentsList[index];
				moment.isLiked = !moment.isLiked;
				moment.likeCount = moment.isLiked ? moment.likeCount + 1 : moment.likeCount - 1;
				
				// 如果点赞，添加到点赞列表
				if (moment.isLiked) {
					moment.likes.push({ userId: '999', userName: '我' });
				} else {
					// 如果取消点赞，从点赞列表中移除
					const likeIndex = moment.likes.findIndex(like => like.userId === '999');
					if (likeIndex !== -1) {
						moment.likes.splice(likeIndex, 1);
					}
				}
				
				// 更新数据
				this.$set(this.momentsList, index, moment);
			}
		},
		
		// 评论事件处理
		handleComment(momentId) {
			uni.showToast({
				title: '评论功能开发中...',
				icon: 'none'
			});
		},
		
		// 查看详情事件处理
		handleDetail(momentId) {
			uni.showToast({
				title: '详情页开发中...',
				icon: 'none'
			});
		},
		
		// 跳转到发布页面
		goPublish() {
		
			// 实际跳转代码，待发布页面创建后取消注释
			uni.navigateTo({
			    url: '/views/moments/publish'
			});
		}
	},
	// 下拉刷新
	onPullDownRefresh() {
		// 模拟刷新
		setTimeout(() => {
			this.loadMockData();
			uni.stopPullDownRefresh();
			uni.showToast({
				title: '刷新成功',
				icon: 'success'
			});
		}, 1000);
	}
}
</script>

<style lang="scss" scoped>
.moments-container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding: 20rpx;
	position: relative;
	
	.moments-header {
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		
		.moments-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
			text-align: center;
			display: block;
		}
	}
	
	.moments-list {
		padding-bottom: 30rpx;
	}
	
	.moments-footer {
		text-align: center;
		padding: 30rpx 0;
		
		.load-more {
			font-size: 26rpx;
			color: #999;
		}
	}
	
	.publish-btn {
		position: fixed;
		right: 40rpx;
		bottom: 100rpx;
		width: 100rpx;
		height: 100rpx;
		background: linear-gradient(135deg, #0c3949, #1a5c6d);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 4rpx 20rpx rgba(12, 57, 73, 0.4);
		z-index: 99;
		transition: all 0.3s;
		
		&:active {
			transform: scale(0.95);
			box-shadow: 0 2rpx 10rpx rgba(12, 57, 73, 0.3);
		}
	}
}
</style>