<template>
    <u-popup mode="bottom" v-model="popShow">
        <view class="select-address-box">
            <view class="header-box">
                <view></view>
                <view class="title-box">选择所在地区</view>
                <view class="close-icon" @tap="close">
                    <!-- <view class="iconfont icon-a-Shutdown-01">x</view> -->
                    <u-icon name="close" color="#333333" size="40"></u-icon>
                </view>
            </view>
            <view class="main-box">
                <view class="address-info">
                    <view class="address-item" @tap="setSelectStatus(0)">
                        <view class="strip-item">
                            <view
                                :class="{
                                    'show-line': address.province,
                                    active: address.province,
                                }"
                            ></view>
                        </view>
                        <view
                            class="name-box"
                            :class="{
                                active: selectStatus == 0,
                            }"
                        >
                            <text v-if="address.province">
                                {{ address.province.name }}
                            </text>
                            <text v-else>请选择所在省份</text>
                        </view>
                        <view class="icon-box">
                            <u-icon class="uicon" name="arrow-right" color="#7D7F82"></u-icon>
                        </view>
                    </view>
                    <view v-if="address.province" class="address-item" @tap="setSelectStatus(1)">
                        <view class="strip-item">
                            <view
                                :class="{
                                    'show-line': address.city,
                                    active: address.city,
                                }"
                            ></view>
                        </view>
                        <view
                            class="name-box"
                            :class="{
                                active: selectStatus == 1,
                            }"
                        >
                            <text v-if="address.city">{{ address.city.name }}</text>
                            <text v-else>请选择所在城市</text>
                        </view>
                        <view class="icon-box">
                            <u-icon class="uicon" name="arrow-right" color="#7D7F82"></u-icon>
                        </view>
                    </view>
                    <view v-if="address.city" class="address-item" @tap="setSelectStatus(2)">
                        <view class="strip-item last-strip-item">
                            <view
                                :class="{
                                    'show-line': showStreet,
                                    active: address.area,
                                }"
                            ></view>
                        </view>
                        <view
                            class="name-box"
                            :class="{
                                active: selectStatus == 2,
                            }"
                        >
                            <text v-if="address.area">{{ address.area.name }}</text>
                            <text v-else>请选择所在区/县</text>
                        </view>
                        <view class="icon-box">
                            <u-icon class="uicon" name="arrow-right" color="#7D7F82"></u-icon>
                        </view>
                    </view>
                    <view v-if="showStreet" class="address-item" @tap="setSelectStatus(3)">
                        <view class="strip-item last-strip-item">
                            <view
                                :class="{
                                    active: address.street,
                                }"
                            ></view>
                        </view>
                        <view
                            class="name-box"
                            :class="{
                                active: selectStatus == 3,
                            }"
                        >
                            <text v-if="address.street">
                                {{ address.street.name }}
                            </text>
                            <text v-else>请选择所在乡/镇/街道办</text>
                        </view>
                        <view class="icon-box">
                            <image src="/static/images/you.png"></image>
                        </view>
                    </view>
                </view>
                <view class="address-select-box">
                    <scroll-view class="content-box" :style="{ height: scrollHeight }" scroll-y="true">
                        <view v-if="selectStatus === 0">
                            <view class="select-tip">请选择省份</view>
                            <view
                                class="select-item"
                                :class="{
                                    active: activeProvinvial.name === i.name,
                                }"
                                v-for="i in provinvial"
                                :key="i.code"
                                @tap="changeProvinvial(i)"
                            >
                                {{ i.name }}
                            </view>
                        </view>
                        <view v-else-if="selectStatus === 1">
                            <view class="select-tip">请选择城市</view>
                            <view
                                class="select-item"
                                :class="{
                                    active: activeCity.name === i.name,
                                }"
                                v-for="i in citys"
                                :key="i.code"
                                @tap="changeCity(i)"
                            >
                                {{ i.name }}
                            </view>
                        </view>
                        <view v-else-if="selectStatus === 2">
                            <view class="select-tip">请选择区/县</view>
                            <view
                                class="select-item"
                                :class="{
                                    active: activeArea.name === i.name,
                                }"
                                v-for="i in areas"
                                :key="i.code"
                                @tap="changeArea(i)"
                            >
                                {{ i.name }}
                            </view>
                        </view>
                        <view v-else>
                            <view class="select-tip">请选择乡/镇</view>
                            <view
                                class="select-item"
                                :class="{
                                    active: activeStreet.name === i.name,
                                }"
                                v-for="i in streets"
                                :key="i.code"
                                @tap="changeStreet(i)"
                            >
                                {{ i.name }}
                            </view>
                        </view>
                    </scroll-view>
                </view>
            </view>
        </view>
    </u-popup>
</template>

<script>
export default {
    emits: ['change'],
    props: {
        address: {
            type: Object,
            require: true,
            default: () => ({
                province: '',
                city: '',
                area: '',
                street: '',
            }),
        },
    },
    data() {
        return {
            popShow: false,
            //全部数据
            provinvial: [], // 省
            cityData: [],
            areaData: [],
            streetsData: [],
            // 筛选后的数据
            citys: [], // 城市
            areas: [], // 区/县
            streets: [], // 镇
            // 选择的省、市、区具体名称
            activeProvinvial: { name: '', code: '' }, // 选择中的省份
            activeCity: {
                name: '',
            }, // 选中的城市
            activeArea: { name: '', code: '' }, // 选中的区县
            activeStreet: { name: '', code: '' }, // 选中的乡镇
            //  0 1 2 3 当前正在选择 省 市 区 镇
            selectStatus: 0,
        };
    },
    computed: {
        // 高度计算
        scrollHeight() {
            let height = 920;

            if (this.address.province) {
                height -= 80;
            }
            if (this.address.city) {
                height -= 80;
            }
            if (this.address.area && 'street' in this.address) {
                height -= 80;
            }
            return height + 'rpx';
        },
        // 是否展示4级
        showStreet() {
            return Boolean(this.selectStatus === 3 || (this.activeArea.name && 'street' in this.address));
        },
    },
    watch: {
        address: {
            handler(newHandler) {
                if (newHandler.province && !this.activeProvinvial.code) {
                    this.setActiveProvinvial();
                }
                if (newHandler.city && !this.activeCity.code) {
                    this.setActiveCity();
                }
                if (newHandler.area && !this.activeArea.code) {
                    this.setActiveArea();
                }
                if (newHandler.street && !this.activeStreet.code) {
                    this.setActiveStreet();
                }
            },
            deep: true,
        },
    },
    mounted() {
        this.getProvinvial();
    },
    methods: {
        // 更新正在选择内容的标记
        setSelectStatus(newSelectStatus) {
            this.selectStatus = newSelectStatus;
        },
        // 关闭 popup
        close() {
            this.popShow = false;
        },
        // 打开  popup
        open() {
            this.popShow = true;
        },
        // 选择省
        changeProvinvial(i) {
            if (i.name !== this.address.province) {
                this.activeProvinvial = i;
                this.activeCity = {};
                this.activeArea = {};
                this.activeStreet = {};
                const newAddress = {
                    province: { name: i.name, code: i.code },
                    city: '',
                    area: '',
                };
                if ('street' in this.address) {
                    newAddress.street = '';
                }
                this.$emit('change', newAddress);
            }
            this.getCity();
            this.selectStatus = 1;
        },
        // 选择市
        changeCity(i) {
            if (i.name !== this.address.city) {
                this.activeCity = i;
                this.activeArea = {};
                this.activeStreet = {};
                const newAddress = {
                    province: this.address.province,
                    city: { name: i.name, code: i.code },
                    area: '',
                };
                if ('street' in this.address) {
                    newAddress.street = '';
                }
                this.$emit('change', newAddress);
            }
            this.getArea();
            this.selectStatus = 2;
        },
        // 选择区县
        changeArea(i) {
            if (i.name !== this.address.area) {
                this.activeArea = i;
                this.activeStreet = {};
                const newAddress = {
                    province: this.address.province,
                    city: this.address.city,
                    area: { name: i.name, code: i.code },
                };
                if ('street' in this.address) {
                    newAddress.street = '';
                }
                this.$emit('change', newAddress);
            }

            if ('street' in this.address) {
                this.selectStatus = 3;
                this.getStreets();
            } else {
                this.close();
            }
        },
        // 选择乡镇
        changeStreet(i) {
            if (i.name !== this.address.street) {
                this.activeStreet = i;
                this.$emit('change', {
                    ...this.address,
                    street: { name: i.name, code: i.code },
                });
            }
            this.close();
        },
        // 省份数据获取
        async getProvinvial() {
            // const res = await this.$u.get(
            //     'https://yzk.wdaoyun.cn/app/api?c=Region&a=get_region_list',
            //     {},
            //     {
            //         pcode: 0,
            //     }
            // );
            // console.log(res)
            const res = await this.$api.getRegionList({
                pcode: 0,
            });
            this.provinvial = res.data.list;
            this.setActiveProvinvial();
        },
        setActiveProvinvial() {
            let provinceIndex;
            if (this.address.province && !this.activeProvinvial.code) {
                if (this.address.province.code) {
                    provinceIndex = this.provinvial.findIndex(i => i.code == this.address.province.code);
                } else {
                    provinceIndex = this.provinvial.findIndex(i => i.name == this.address.province.name);
                }
                if (provinceIndex !== -1) {
                    this.activeProvinvial = this.provinvial[provinceIndex];
                }
                if (!this.address.province.code) {
                    this.address.province.code = this.activeProvinvial.code;
                    this.$emit('change', {
                        ...this.address,
                    });
                }
                this.getCity();
            }
        },
        // 城市数据获取
        async getCity() {
            let res = await this.$api.getRegionList({
                pcode: this.activeProvinvial.code,
            });
            this.cityData = res.data.list;
            this.setActiveCity();
        },
        setActiveCity() {
            if (this.cityData && this.cityData.length === 0) {
                return;
            }
            this.citys = this.cityData;
            if (this.address.city && !this.activeCity.code) {
                let cityIndex;
                if (this.address.city.code) {
                    cityIndex = this.cityData.findIndex(i => i.code == this.address.city.code);
                } else {
                    cityIndex = this.cityData.findIndex(i => i.name == this.address.city.name);
                }
                if (cityIndex !== -1) {
                    this.activeCity = this.cityData[cityIndex];
                }
                if (!this.address.city.code) {
                    this.address.city.code = this.activeCity.code;
                    this.$emit('change', {
                        ...this.address,
                    });
                }

                this.getArea();
            }
        },
        // 区县数据
        async getArea() {
            let res = await this.$api.getRegionList({
                pcode: this.activeCity.code,
            });
            this.areaData = res.data.list;
            this.setActiveArea();
        },
        setActiveArea() {
            if (this.areaData.length === 0) {
                return;
            }
            this.areas = this.areaData;

            if (this.address.area && !this.activeArea.code) {
                let areaIndex;
                if (this.address.area.code) {
                    areaIndex = this.areaData.findIndex(i => i.code == this.address.area.code);
                } else {
                    areaIndex = this.areaData.findIndex(i => i.name == this.address.area.name);
                }
                // const areaIndex = this.areaData.findIndex(i => i.code == this.address.area.code);
                if (areaIndex !== -1) {
                    this.activeArea = this.areaData[areaIndex];
                }
                if (!this.address.area.code) {
                    this.address.area.code = this.activeArea.code;
                    this.$emit('change', {
                        ...this.address,
                    });
                }

                this.getStreets();
            }
        },
        // 镇 社区
        async getStreets() {
            let res = await this.$api.getRegionList({
                pcode: this.activeArea.code,
            });
            this.streetsData = res.data.list;
            this.setActiveStreet();
        },
        setActiveStreet() {
            if (this.streetsData.length === 0) {
                return;
            }
            this.streets = this.streetsData;
            if (this.address.street && !this.activeStreet.code) {
                let streetIndex;
                if (this.address.street.code) {
                    streetIndex = this.streetsData.findIndex(i => i.code == this.address.street.code);
                } else {
                    streetIndex = this.streetsData.findIndex(i => i.name == this.address.street.name);
                }
                // const streetIndex = this.streetsData.findIndex(i => i.code == this.address.street.code);
                if (streetIndex !== -1) {
                    this.activeStreet = this.streetsData[streetIndex];
                }

                if (!this.address.street.code) {
                    this.address.street.code = this.activeStreet.code;
                    this.$emit('change', {
                        ...this.address,
                    });
                }
                this.$emit('change', {
                    ...this.address,
                });
            }
        },
    },
};
</script>

<style lang="scss">
.select-address-box {
    height: 1300rpx;
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
    background-color: #fff;
    padding: 30rpx 0;

    .header-box {
        display: flex;
        justify-content: space-between;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 40rpx;
        padding: 0 20rpx;

        > .title-box {
            font-size: 36rpx;
        }

        .iconfont {
            color: #72899b;
        }
    }

    .main-box {
        .address-info {
            padding: 0 20rpx;

            > .address-item {
                display: flex;
                align-items: center;
                height: 80rpx;

                .strip-item {
                    color: #333;
                    margin-right: 48rpx;
                    position: relative;

                    > view {
                        width: 12rpx;
                        height: 12rpx;
                        border-radius: 50%;
                        border: 2rpx solid #086df7;
                        transition: all 0.25s;
                        box-sizing: border-box;
                    }

                    .show-line {
                        &::after {
                            content: '';
                            position: absolute;
                            top: 100%;
                            left: 50%;
                            width: 2rpx;
                            height: 69rpx;
                            background-color: #086df7;
                        }

                        /* #ifndef H5*/
                        &::after {
                            transform: translateX(-50%);
                        }
                        /* #endif */

                        /* #ifdef VUE2 */
                        &::after {
                            transform: translateX(-50%);
                        }
                        /* #endif */
                    }

                    .active {
                        background: #086df7;
                    }
                }

                .name-box {
                    font-size: 28rpx;
                    flex: 1;
                }

                .active {
                    color: #086df7;
                }

                .icon-box {
                    .iconfont {
                        font-size: 24rpx;
                        color: #72899b;
                    }
                    image {
                        width: 14rpx;
                        height: 24rpx;
                    }
                }
            }
        }

        .address-select-box {
            padding: 20rpx 20rpx;
            border-top: 2rpx solid #d4d4d4;

            .content-box {
                height: 600rpx;

                .select-tip {
                    font-size: 32rpx;
                    color: #333;
                    font-weight: bold;
                    padding-bottom: 20rpx;
                }

                .select-item {
                    padding: 20rpx 20rpx;
                    font-size: 28rpx;
                    color: #333;
                }

                .active {
                    color: #086df7;
                }
            }
        }
    }
}
</style>
