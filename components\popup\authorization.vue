<template>
    <view>
        <u-popup class="pop-auth" :custom-style="{ background: 'transparent' }" v-model="showPopup" mode="bottom" :safe-area-inset-bottom="safe" :mask-close-able="false" border-radius="20">
            <view class="login-module">
                <view class="login-logo">
                    <image class="login-logo-img" src="http://static.wdaoyun.com/wdy/source/2025-04-21/dinggu_logo.png" mode="aspectFit"></image>
                </view>
                <view class="login-panel">
                    <view class="panel-text">
                        <view class="panel-text1">欢迎登录体育社交～</view>
                        <!-- <view class="panel-text2">登录后可享受更好的体验</view> -->
                    </view>
                    <view class="form-privacy">
                        <view class="form-check">
                            <u-checkbox class="check-wrap" active-color="#0c3949" v-model="agreement">
                                <text>我已阅读并同意</text>
                            </u-checkbox>
                            <button id="agree-btn" type="default" open-type="agreePrivacyAuthorization" class="privacy-btn" @agreeprivacyauthorization="handleAgree"></button>
                        </view>
                        <text class="form-privacy-text" @click.stop="openPrivacyContract">《体育社交隐私保护》</text>
                    </view>
                    <view class="btn-group">
                        <button class="cancel-btn" @click.stop="hide">暂不登录</button>

                        <button class="confirm" @click="actionLogin">授权登录</button>
                        <!-- <button v-else :loading="loading" class="confirm" open-type="getUserInfo" @getuserinfo="actionLogin" withCredentials="true" lang="zh_CN">
                        <text class="iconfont">&#xe65c;</text>
                        微信授权登录
                    </button> -->
                    </view>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex';
export default {
    props: {
        safe: {
            type: Boolean,
            default: false,
        },
    },
    computed: mapState(['hasLogin', 'userInfo', 'shareData', 'systemInfo','appType']),
    data() {
        return {
            loading: false,
            showPopup: false,
            agreement: false,
            privacyResolves: new Set(),
            canIUseGetUserProfile: false, //适配授权信息新旧版本
        };
    },
    mounted() {
        this.handleTouchInput();
        if (wx.onNeedPrivacyAuthorization) {
            wx.onNeedPrivacyAuthorization(resolve => {
                this.privacyHandler(resolve);
            });
        } else {
            uni.showModal({
                title: '提示',
                content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。',
                confirmText: '我知道了',
                showCancel: false,
            });
        }
    },
    methods: {
        hide() {
            this.showPopup = false;
        },
        show() {
            this.showPopup = true;
            // this.getPrivacySetting();
            if (wx.getUserProfile) {
                this.canIUseGetUserProfile = true;
            }
        },
        getPrivacySetting() {
            if (wx.getPrivacySetting) {
                wx.getPrivacySetting({
                    success: res => {
                        console.log(res); // 返回结果为: res = { needAuthorization: true/false, privacyContractName: '《xxx隐私保护指引》' }
                        if (res.needAuthorization) {
                            // 需要弹出隐私协议
                        } else {
                            // 用户已经同意过隐私协议，所以不需要再弹出隐私协议，也能调用隐私接口
                        }
                    },
                    fail: () => {},
                });
            }
        },

        actionLogin: async function (res) {
            if (!this.agreement) {
                uni.showToast({
                    title: '请先阅读并同意用户协议',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            uni.showLoading({
                title: 'Loading...',
                mask: true,
            });
            setTimeout(() => {
                uni.hideLoading();
            }, 8000);
            const resUser = await this.getUserInfo();
            const resAuth = await this.wxLogin();
            console.log('resAuth--', resAuth);
            console.log('resUser--', resUser);
            const resOpen = resAuth && (await this.getOpenid(resAuth));
            const resLogin = resUser && resOpen && (await this.reqLogin(resUser, resOpen));
        },
        // 适配新旧版本获取个人信息
        getUserInfo: async function () {
            // 调整后
            if (wx.getUserProfile) {
                return new Promise((resolve, reject) => {
                    this.loading = true;
                    wx.getUserProfile({
                        lang: 'zh_CN',
                        desc: '用于完善会员资料',
                        success: res => {
                            resolve(res);
                        },
                        fail: res => {
                            console.log('GetUserProfile--fail');
                            uni.hideLoading();
                            this.$emit('loginSuccess', false);
                        },
                        complete: res => {
                            console.log('GetUserProfile--complete', res);
                            this.loading = false;
                            this.showPopup = false;
                        },
                    });
                });
            }
            // 调整前
            else {
                return new Promise((resolve, reject) => {
                    this.loading = true;
                    uni.getUserInfo({
                        provider: 'weixin',
                        lang: 'zh_CN',
                        success: res => {
                            resolve(res);
                        },
                        fail: res => {
                            console.log('getUserInfo--fail');
                            uni.hideLoading();
                            this.$emit('loginSuccess', false);
                        },
                        complete: res => {
                            console.log('getUserInfo--complete', res);
                            this.loading = false;
                            this.showPopup = false;
                        },
                    });
                });
            }
        },
        wxLogin: async function () {
            return new Promise((resolve, reject) => {
                uni.login({
                    provider: 'weixin',
                    success: res => {
                        resolve(res.code);
                    },
                    fail: res => {
                        uni.showToast({
                            title: '获取code失败,请重新授权尝试获取！',
                            icon: 'none',
                        });
                    },
                });
            });
        },
        getOpenid: async function (code) {
            return new Promise((resolve, reject) => {
                this.$api
                    .codeLogin({
                        code: code,
                        m_fromuid: '',
                        app_type: this.appType,
                    })
                    .then(res => {
                        resolve(res.data);
                        uni.setStorage({
                            key: 'token',
                            data: res.data.access_token,
                        });
                        this.showPopup = false;
                        uni.hideLoading();
                        uni.setStorageSync('loginTime', new Date().getTime());
                        this.VAgetUser().then(res => {
                            this.$emit('loginSuccess', true);
                        });
                        console.log(res, 'code');
                    })
                    .catch(err => {
                        uni.showToast({
                            title: err.message,
                            icon: 'none',
                            mask: true,
                        });
                        uni.hideLoading();
                        console.log('request fail', err);
                    });
            });
        },

        async reqLogin(user, arg) {
            uni.setStorage({
                key: 'token',
                data: arg.access_token,
            });
            console.log(this.$util.compareVersion(this.systemInfo.SDKVersion, '2.27.0') <= 0);
            if (this.$util.compareVersion(this.systemInfo.SDKVersion, '2.27.0') <= 0) {
                const info = user.userInfo;
                await this.$api.updateUser({
                    nickname: info.nickName ? info.nickName : '',
                    headimgurl: info.avatarUrl ? info.avatarUrl : '',
                    access_token: arg.access_token,
                });
            }
            uni.setStorageSync('loginTime', new Date().getTime());
            this.VAgetUser().then(res => {
                this.$emit('loginSuccess', true);
            });
            uni.hideLoading();
        },
        // 打开隐私链接
        openPrivacyContract() {
            wx.openPrivacyContract({
                success: res => {
                    console.log('openPrivacyContract success');
                },
                fail: res => {
                    console.error('openPrivacyContract fail', res);
                },
            });
        },
        privacyHandler(resolve) {
            this.privacyResolves.add(resolve);
        },
        handleAgree(e) {
            console.log('handleAgree');
            // this.disPopUp();
            // 这里演示了同时调用多个wx隐私接口时要如何处理：让隐私弹窗保持单例，点击一次同意按钮即可让所有pending中的wx隐私接口继续执行 （看page/index/index中的 wx.getClipboardData 和 wx.startCompass）
            this.privacyResolves.forEach(resolve => {
                resolve({
                    event: 'agree',
                    buttonId: 'agree-btn',
                });
            });
            this.privacyResolves.clear();
            this.agreement = !this.agreement;
        },
        handleTouchInput() {
            if (wx.getPrivacySetting) {
                wx.getPrivacySetting({
                    success: res => {
                        console.log(res); // 返回结果为: res = { needAuthorization: true/false, privacyContractName: '《xxx隐私保护指引》' }
                    },
                    fail: err => {
                        console.log(err);
                    },
                });
                return;
            }
        },
        ...mapMutations(['setOpenid', 'updateUserInfo', 'setHasLogin']),
        ...mapActions(['VAgetUser']),
    },
};
</script>

<style lang="scss" scoped>
.login-module {
    background: #ffffff;
    // border-radius: 20rpx 20rpx 0 0;
    // padding-bottom: 0;
    // padding-bottom: constant(safe-area-inset-bottom);
    // padding-bottom: env(safe-area-inset-bottom);
}
.pop-auth {
    ::v-deep {
        .u-drawer-bottom {
            background-color: #333333 !important;
        }
    }
}
.login-panel {
    padding: 0 30rpx;
    box-sizing: border-box;
    border-radius: 12rpx;
    overflow: hidden;
}
.login-logo .login-logo-img {
    width: 130rpx;
    height: 130rpx;
    margin: 0rpx auto;
    margin-top: 50rpx;
    display: block;
    border-radius: 50%;
}
.panel-text {
    margin: 35rpx 0 25rpx 0;
    text-align: center;
    line-height: 2rem;
    .panel-text1 {
        font-size: 48rpx;
        color: #333333;
        font-weight: 500;
    }
    .panel-text2 {
        font-size: 28rpx;
        color: #666666;
    }
}
.btn-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 40rpx 20rpx;
}
.confirm {
    background: #0c3949;
    color: #ffffff;
    font-size: 34rpx;
    border-radius: 16rpx;
    // overflow: hidden;
    width: 45%;
    display: flex;
    align-items: center;
    justify-content: center;
    .iconfont {
        font-size: 34rpx;
        margin-right: 8rpx;
    }
}

// .rule-view {
//     font-size: 28rpx;
//     line-height: 80rpx;
//     color: #999999;
//     span {
//         color: #ff8d34;
//     }
// }

.cancel-btn {
    width: 45%;
    color: #666666;
    text-align: center;
    border-radius: 16rpx;
    // overflow: hidden;
    width: 45%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 34rpx;
    // padding: 35rpx 0;
}
.form-privacy {
    display: flex;
    align-items: center;
    // justify-content: center;
    margin: 16rpx 0rpx 0;
    &-text {
        color: $custom-color;
    }
}
.form-check {
    // width: 690rpx;
    position: relative;
    .privacy-btn {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        border: none;
        background: transparent;
        z-index: 9;
    }
    .check-wrap {
        // position: relative;
        ::v-deep .u-checkbox__label {
            margin-right: 0;
            font-size: 28rpx;
        }
        &-blue {
            color: $custom-color;
            cursor: pointer;
            z-index: 99;
        }
    }
}
</style>
