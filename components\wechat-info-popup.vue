<template>
    <view class="wechat-popup-overlay" v-if="show" @click="handleOverlayClick">
        <view class="wechat-popup-container" @click.stop>
            <!-- 关闭按钮 -->
            <view class="close-btn" @click="handleClose">
                <u-icon name="close" size="24" color="#fff"></u-icon>
            </view>
            
            <!-- 用户头像和昵称 -->
            <view class="user-header">
                <view class="avatar-container">
                    <u-avatar :src="userInfo.headimgurl" size="120" mode="aspectFill"></u-avatar>
                    <view class="vip-badge" v-if="isVip">
                        <u-icon name="crown-fill" size="16" color="#8a6500"></u-icon>
                        <text>VIP</text>
                    </view>
                </view>
                <view class="user-name">{{ userInfo.nickname || '微信用户' }}</view>
            </view>

            <!-- 微信号区域 -->
            <view class="wechat-section">
                <view class="section-title">
                    <u-icon name="weixin-fill" size="20" color="#8966ef"></u-icon>
                    <text>微信号</text>
                </view>
                <view class="wechat-content" v-if="userInfo.weixin">
                    <view class="wechat-id">{{ decodedWechat }}</view>
                    <view class="copy-btn" @click="copyWechat">
                        <u-icon name="copy" size="18" color="#8966ef"></u-icon>
                        <text>复制</text>
                    </view>
                </view>
                <view class="no-wechat" v-else>
                    <text>该用户未设置微信号</text>
                </view>
            </view>

            <!-- 身高体重信息 -->
            <view class="body-info-section">
                <view class="section-title">
                    <u-icon name="account-fill" size="20" color="#8966ef"></u-icon>
                    <text>基本信息</text>
                </view>
                <view class="body-info-grid">
                    <view class="info-card">
                        <view class="info-icon">
                            <u-icon name="arrow-up" size="20" color="#f0cc6c"></u-icon>
                        </view>
                        <view class="info-content">
                            <text class="info-label">身高</text>
                            <text class="info-value">{{ userInfo.sf_cm ? userInfo.sf_cm + 'cm' : '未设置' }}</text>
                        </view>
                    </view>
                    <view class="info-card">
                        <view class="info-icon">
                            <u-icon name="star-fill" size="20" color="#f0cc6c"></u-icon>
                        </view>
                        <view class="info-content">
                            <text class="info-label">体重</text>
                            <text class="info-value">{{ userInfo.tz_kg ? userInfo.tz_kg + 'kg' : '未设置' }}</text>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 底部按钮 -->
            <view class="footer-actions">
                <button class="action-btn close-action" @click="handleClose">
                    <text>关闭</text>
                </button>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'WechatInfoPopup',
    props: {
        value: {
            type: Boolean,
            default: false
        },
        userInfo: {
            type: Object,
            default: () => ({})
        },
        needDecodeWechat: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        show() {
            return this.value;
        },
        isVip() {
            // 这里可以根据实际的VIP判断逻辑来实现
            return this.userInfo?.member_time && this.userInfo?.member_time !== '0';
        },
        // 获取解码后的微信号
        decodedWechat() {
            if (!this.userInfo?.weixin) return '';
            if (this.needDecodeWechat) {
                return this.$util.decodeBase64(this.userInfo.weixin);
            }
            return this.userInfo.weixin;
        }
    },
    methods: {
        handleClose() {
            this.$emit('input', false);
            this.$emit('close');
        },
        handleOverlayClick() {
            this.handleClose();
        },
        copyWechat() {
            if (!this.userInfo.weixin) {
                uni.showToast({
                    title: '该用户未设置微信号',
                    icon: 'none'
                });
                return;
            }

            uni.setClipboardData({
                data: this.decodedWechat,
                success: () => {
                    uni.showToast({
                        title: '微信号已复制',
                        icon: 'success'
                    });
                },
                fail: () => {
                    uni.showToast({
                        title: '复制失败，请重试',
                        icon: 'none'
                    });
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.wechat-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(10rpx);
}

.wechat-popup-container {
    width: 600rpx;
    max-height: 80vh;
    background: linear-gradient(135deg, #8966ef 0%, #a584f2 50%, #f0cc6c 100%);
    border-radius: 32rpx;
    position: relative;
    overflow: hidden;
    box-shadow: 0 20rpx 60rpx rgba(137, 102, 239, 0.3);
}

.close-btn {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    width: 48rpx;
    height: 48rpx;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    backdrop-filter: blur(10rpx);
}

.user-header {
    padding: 60rpx 40rpx 40rpx;
    text-align: center;
    
    .avatar-container {
        position: relative;
        display: inline-block;
        margin-bottom: 20rpx;
        
        .vip-badge {
            position: absolute;
            bottom: -8rpx;
            left: 50%;
            transform: translateX(-50%);
            background: linear-gradient(135deg, #f0cc6c, #edc353);
            color: #8a6500;
            font-size: 20rpx;
            padding: 6rpx 12rpx;
            border-radius: 20rpx;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 4rpx;
        }
    }
    
    .user-name {
        font-size: 32rpx;
        font-weight: 600;
        color: #fff;
        text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
    }
}

.wechat-section, .body-info-section {
    margin: 0 30rpx 30rpx;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    padding: 30rpx;
    backdrop-filter: blur(10rpx);
    
    .section-title {
        display: flex;
        align-items: center;
        gap: 8rpx;
        margin-bottom: 20rpx;
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
    }
}

.wechat-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9ff;
    border: 2rpx solid #e8ecff;
    border-radius: 16rpx;
    padding: 20rpx;
    
    .wechat-id {
        font-size: 30rpx;
        font-weight: 600;
        color: #333;
        letter-spacing: 1rpx;
    }
    
    .copy-btn {
        display: flex;
        align-items: center;
        gap: 6rpx;
        background: linear-gradient(135deg, #8966ef, #a584f2);
        color: #fff;
        padding: 12rpx 20rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 500;
        box-shadow: 0 4rpx 12rpx rgba(137, 102, 239, 0.3);
    }
}

.no-wechat {
    text-align: center;
    color: #999;
    font-size: 26rpx;
    padding: 20rpx;
}

.body-info-grid {
    display: flex;
    gap: 20rpx;
    
    .info-card {
        flex: 1;
        background: #f8f9ff;
        border: 2rpx solid #e8ecff;
        border-radius: 16rpx;
        padding: 20rpx;
        display: flex;
        align-items: center;
        gap: 12rpx;
        
        .info-icon {
            width: 40rpx;
            height: 40rpx;
            background: linear-gradient(135deg, #f0cc6c, #edc353);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .info-content {
            flex: 1;
            
            .info-label {
                display: block;
                font-size: 22rpx;
                color: #999;
                margin-bottom: 4rpx;
            }
            
            .info-value {
                display: block;
                font-size: 26rpx;
                font-weight: 600;
                color: #333;
            }
        }
    }
}

.footer-actions {
    padding: 0 30rpx 30rpx;
    
    .action-btn {
        width: 100%;
        height: 80rpx;
        background: rgba(255, 255, 255, 0.9);
        color: #8966ef;
        border: none;
        border-radius: 20rpx;
        font-size: 28rpx;
        font-weight: 600;
        backdrop-filter: blur(10rpx);
        box-shadow: 0 4rpx 12rpx rgba(255, 255, 255, 0.2);
        
        &:active {
            background: rgba(255, 255, 255, 0.8);
        }
    }
}
</style>
