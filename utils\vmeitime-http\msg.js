const http = uni.$u.http;
/**
 * 获取消息列表
 * @param {Object} d 请求参数
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
 * @param {access_token} d.access_token 用户token
*/
export const getMsgList = d => http.post('?c=Msg&a=get_msg_list', d);
/**
 * 获取关注消息记录
 * @param {Object} d 请求参数
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
 * @param {access_token} d.access_token 用户token
*/
export const getFollowMsgList = d => http.post('?c=Msg&a=follow_user_list', d);
/**
 * 获取消息记录详情
 * @param {Object} d 请求参数
 * @param {Number} d.msg_session_id 跟谁聊天传谁
 * @param {Number} d.pagesize 每页条数
 * @param {last_id} d.last_id 默认为0 往上翻传第一个id
 * @param {access_token} d.access_token 用户token
*/
export const getMsgDetail = d => http.post('?c=Msg&a=get_msg_user_list', d);
/**
 * 发送消息
 * @param {Object} d 请求参数
 * @param {Number} d.to_uid 给谁发
 * @param {Number} d.msg 消息内容
 * @param {Number} d.message_type 1文本 2图片 3视频 4 语音
 * @param {Number} d.file_size 如果类型2 3 4 传递文件大小
 * @param {last_id} d.duration 语音时长 类型4必穿
 * @param {access_token} d.access_token 用户token
*/
export const sendMsg = d => http.post('?c=Msg&a=send_msg', d);  

/**
 * 获取评论消息
 * @param {Object} d 请求参数
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
 * @param {access_token} d.access_token 用户token
 */
export const getCommentMsg = d => http.post('?c=Msg&a=comment_list', d);

/**
 * 获取点赞收藏消息
 * @param {Object} d 请求参数
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
 * @param {access_token} d.access_token 用户token
 */
export const getLikeMsg = d => http.post('?c=Msg&a=like_msg_list', d);

/**
 * 设置自动回复机器人
 * @param {Object} d 请求参数
 * @param {Number} d.name 机器人名称
 * @param {Number} d.sex 性别-1男 2女
 * @param {String} d.option_json 机器人自定义配置 JSON.stringify([“我的星座是白羊座”])
 * @param {Number} d.interval 秒 多少秒后自定回复
 * @param {Number} d.status 1开启 2关闭
 * @param {access_token} d.access_token 用户token
 */
export const setAutoReplyRobot = d => http.post('?c=Msg&a=set_robot', d);

/**
 * 获取自动回复机器人
 * @param {Object} d 请求参数
 * @param {access_token} d.access_token 用户token
 */
export const getAutoReplyRobot = d => http.post('?c=Msg&a=get_robot', d);

/**
 * 获取未读消息列表
 * @param {Object} d 请求参数
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
 * @param {access_token} d.access_token 用户token
*/
export const getUnreadMsgList = d => http.post('?c=Msg&a=msg_index', d);

/**
 * 初始化会话
 * @param {Object} d 请求参数
 * @param {Number} d.to_uid 给谁发
 * @param {access_token} d.access_token 用户token
*/
export const initSession = d => http.post('?c=Msg&a=msg_session_id', d);



export default {
    getMsgList,
    getFollowMsgList,
    getMsgDetail,
    sendMsg,
    getCommentMsg,
    getLikeMsg,
    setAutoReplyRobot,
    getAutoReplyRobot,
    getUnreadMsgList,
    initSession
};
