import App from './App';

// #ifndef VUE3
import Vue from 'vue';
import './uni.promisify.adaptor';
// 引入uview-ui 状态库
import uView from 'uview-ui';
Vue.use(uView);

// 引入vuex 状态库
import store from './store';
Vue.prototype.$store = store;

// 工具类
import util from 'utils/utils.js';
Vue.prototype.$util = util;
Vue.prototype.$apiHost = 'https://gaoshi.wdaoyun.cn';
Vue.prototype.$familyImg = 'http://static.wdaoyun.com/wdy/source/2023-10-18/share.png';
Vue.config.productionTip = false;
App.mpType = 'app';
const app = new Vue({
    store,
    ...App,
});
// api工具类
import httpInterceptor from './utils/vmeitime-http/interface.js';
Vue.use(httpInterceptor, app);
import api from './utils/vmeitime-http/index';
Vue.prototype.$api = api;
app.$mount();
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue';
export function createApp() {
    const app = createSSRApp(App);
    return {
        app,
    };
}
// #endif
