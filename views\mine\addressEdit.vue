<template>
    <view class="page-container">
        <!-- 页面标题 -->
        <view class="page-header">
            <view class="page-title">{{ type === 1 ? '添加新地址' : '编辑地址' }}</view>
        </view>

        <!-- 地址信息卡片 -->
        <view class="address-card">
            <view class="card-header">
                <u-icon name="map" color="#8966ef" size="32"></u-icon>
                <text class="card-title">地址信息</text>
            </view>

            <u-form :model="addressMsg" ref="uForm" class="address-form">
                <view class="form-item">
                    <view class="form-label">
                        <u-icon name="account" color="#666" size="28"></u-icon>
                        <text>收货人</text>
                    </view>
                    <view class="detail-input-box">
                        <u-input v-model="addressMsg.name" placeholder="请输入收货人姓名" maxlength="8" :clearable="false" class="form-input detail-input" />
                    </view>
                </view>

                <view class="form-item">
                    <view class="form-label">
                        <u-icon name="phone" color="#666" size="28"></u-icon>
                        <text>联系电话</text>
                    </view>
                    <view class="detail-input-box">
                        <u-input v-model="addressMsg.phone" placeholder="请输入手机号码" maxlength="11" type="number" :clearable="false" class="form-input detail-input" />
                    </view>
                </view>

                <view class="form-item form-cell" @click="openAddressPop">
                    <view class="form-label">
                        <u-icon name="map" color="#666" size="28"></u-icon>
                        <text>所在地区</text>
                    </view>
                    <view class="region-selector">
                        <view v-if="!selectedAddress" class="placeholder-text">请选择地区</view>
                        <view v-else class="selected-text">{{ selectedAddress }}</view>
                        <u-icon name="arrow-right" color="#c6cad1" size="28"></u-icon>
                    </view>
                </view>

                <view class="form-item">
                    <view class="form-label">
                        <u-icon name="home" color="#666" size="28"></u-icon>
                        <text>详细地址</text>
                    </view>
                    <view class="detail-input-box">
                        <u-input v-model="addressMsg.address" placeholder="请输入街道门牌信息" :clearable="false" class="form-input detail-input" />
                        <view class="location-btn" @click.stop="openMap">
                            <u-icon name="map" color="#8966ef" size="32"></u-icon>
                        </view>
                    </view>
                </view>
            </u-form>
        </view>

        <!-- 保存按钮 -->
        <view class="save-button" :class="{ disabled: saveDisable }" @click="saveAddress">
            <text>{{ type === 1 ? '保存地址' : '更新地址' }}</text>
        </view>

        <!-- 地址选择弹窗 -->
        <address-pop ref="addressPop" :address="regionAddress" @change="onAddressChange"></address-pop>
    </view>
</template>

<script>
import { mapState } from 'vuex';
import addressPop from '@/components/addressPop.vue';

export default {
    components: {
        addressPop,
    },
    computed: {
        ...mapState(['userInfo']),
        selectedAddress() {
            if (this.regionAddress.province && this.regionAddress.city && this.regionAddress.area) {
                return this.regionAddress.province.name + this.regionAddress.city.name + this.regionAddress.area.name;
            }
            return '';
        },
    },
    data() {
        return {
            type: 1, // 1添加 2编辑
            addressMsg: {
                name: '',
                phone: '',
                province_code: '', // 省份代码
                city_code: '', // 城市代码
                area_code: '', // 区域代码
                address: '', // 详细地址
                is_def: '0', // 是否默认地址
                address_id: '', // 编辑时的地址ID
            },
            regionAddress: {
                province: '',
                city: '',
                area: '',
            },
            saveDisable: true,
        };
    },
    watch: {
        addressMsg: {
            handler(val) {
                if (val.name != '' && val.phone.length == 11 && val.province_code != '' && val.city_code != '' && val.area_code != '' && val.address != '') {
                    this.saveDisable = false;
                } else {
                    this.saveDisable = true;
                }
            },
            deep: true,
        },
    },
    onLoad(option) {
        if (option.type == 2) {
            this.type = 2;
            const addressData = JSON.parse(option.addressMsg);
			console.log(addressData);
            // 兼容旧数据格式，转换为新格式
            this.addressMsg = {
                name: addressData.uname || addressData.name || '',
                phone: addressData.mobile || addressData.phone || '',
                province_code: addressData.province_code || '',
                city_code: addressData.city_code || '',
                area_code: addressData.area_code || '',
                address: addressData.address || '',
                is_def: addressData.is_def || '0',
                address_id: addressData.id || addressData.address_id || '',
            };

            // 如果有地区信息，设置regionAddress用于显示
            if (addressData.province_txt || addressData.area_txt || addressData.city_txt) {
                this.regionAddress = {
                    province: { name: addressData.province_txt || '', code: addressData.province_code || '' },
                    city: { name: addressData.city_txt || '', code: addressData.city_code || '' },
                    area: { name: addressData.area_txt || '', code: addressData.area_code || '' },
                };
            }
        } else {
            this.addressMsg.phone = this.userInfo.mobile;
        }
    },
    methods: {
        // 打开地址选择弹窗
        openAddressPop() {
            this.$refs.addressPop.open();
        },
        // 地址选择回调
        onAddressChange(address) {
            this.regionAddress = address;
            this.addressMsg.province_code = address.province ? address.province.code : '';
            this.addressMsg.city_code = address.city ? address.city.code : '';
            this.addressMsg.area_code = address.area ? address.area.code : '';
        },
        saveAddress() {
            if (
                this.addressMsg.name == '' ||
                this.addressMsg.phone.length != 11 ||
                this.addressMsg.province_code == '' ||
                this.addressMsg.city_code == '' ||
                this.addressMsg.area_code == '' ||
                this.addressMsg.address == ''
            ) {
                uni.showToast({
                    title: '请完善地址信息',
                    icon: 'none',
                });
            } else {
                if (this.type == 1) {
                    this.addAddress();
                } else if (this.type == 2) {
                    this.editAddress();
                }
            }
        },
        addAddress() {
            this.$api
                .createAddress({
                    name: this.addressMsg.name,
                    phone: this.addressMsg.phone,
                    province_code: this.addressMsg.province_code,
                    city_code: this.addressMsg.city_code,
                    area_code: this.addressMsg.area_code,
                    address: this.addressMsg.address,
                    is_def: this.addressMsg.is_def,
                    access_token: uni.getStorageSync('token'),
                })
                .then(res => {
                    uni.showToast({
                        title: '添加成功',
                        icon: 'success',
                    });
                    uni.$emit('updateAddressList', res.data);
                    uni.navigateBack();
                })
                .catch(() => {
                    uni.showToast({
                        title: '添加失败',
                        icon: 'none',
                    });
                });
        },
        editAddress() {
            this.$api
                .editAddress({
                    address_id: this.addressMsg.address_id,
                    name: this.addressMsg.name,
                    phone: this.addressMsg.phone,
                    province_code: this.addressMsg.province_code,
                    city_code: this.addressMsg.city_code,
                    area_code: this.addressMsg.area_code,
                    address: this.addressMsg.address,
                    is_def: this.addressMsg.is_def,
                    access_token: uni.getStorageSync('token'),
                })
                .then(res => {
                    uni.showToast({
                        title: '修改成功',
                        icon: 'success',
                    });
                    uni.$emit('updateAddressList', res.data);
                    uni.navigateBack();
                })
                .catch(() => {
                    uni.showToast({
                        title: '修改失败',
                        icon: 'none',
                    });
                });
        },
        openMap() {
            var that = this;
            uni.chooseLocation({
                success: res => {
                    this.addressMsg.address = res.address;
                },
                fail: () => {
                    wx.getSetting({
                        success(res1) {
                            if (!res1.authSetting['scope.userLocation']) {
                                wx.authorize({
                                    scope: 'scope.userLocation',
                                    success() {
                                        that.openSetting();
                                    },
                                    fail() {
                                        that.openSetting();
                                    },
                                });
                            }
                        },
                    });
                },
            });
        },
        openSetting() {
            uni.showModal({
                title: '温馨提示',
                content: '您之前已拒绝授权地理位置，请前往开启后再使用',
                showCancel: false,
                success: res => {
                    if (res.confirm) {
                        wx.openSetting();
                    }
                },
            });
        },
    },
};
</script>

<style lang="scss" scoped>
// 引入项目主题色变量
$theme-primary: #8966ef;
$theme-primary-light: #a584f2;
$theme-secondary: #f0cc6c;
$border-color: #f0f0f0;
$text-color: #333;
$text-gray: #666;
$text-light: #999;
$bg-gray: #f7f8f7;

.page-container {
    min-height: 100vh;
    background-color: $bg-gray;
    padding: 20rpx;
    box-sizing: border-box;
}

.page-header {
    margin-bottom: 30rpx;

    .page-title {
        font-size: 36rpx;
        font-weight: 600;
        color: $text-color;
        text-align: center;
        padding: 20rpx 0;
    }
}

.address-card {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 40rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid $border-color;

    .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 30rpx;
        padding-bottom: 20rpx;
        border-bottom: 1rpx solid #f5f5f5;

        .card-title {
            font-size: 32rpx;
            font-weight: 600;
            color: $text-color;
            margin-left: 12rpx;
        }
    }
}

.address-form {
    .form-item {
        margin-bottom: 30rpx;

        &:last-child {
            margin-bottom: 0;
        }

        .form-label {
            display: flex;
            align-items: center;
            margin-bottom: 16rpx;

            text {
                font-size: 28rpx;
                color: $text-gray;
                margin-left: 12rpx;
                font-weight: 500;
            }
        }

        .form-input {
            background-color: #f8f9fa;
            border-radius: 12rpx;
            padding: 20rpx 24rpx;
            font-size: 28rpx;

            &:focus {
                border-color: $theme-primary;
                background-color: rgba(137, 102, 239, 0.05);
            }
        }
    }

    .form-cell {
        cursor: pointer;

        .region-selector {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: #f8f9fa;
            border: 1rpx solid $border-color;
            border-radius: 12rpx;
            padding: 20rpx 24rpx;
            min-height: 80rpx;

            .placeholder-text {
                color: $text-light;
                font-size: 28rpx;
            }

            .selected-text {
                color: $text-color;
                font-size: 28rpx;
                flex: 1;
            }
        }

        &:active .region-selector {
            background-color: rgba(137, 102, 239, 0.05);
            border-color: $theme-primary;
        }
    }

    .detail-input-box {
        position: relative;
        display: flex;
        align-items: center;

        .detail-input {
            flex: 1;
            padding-right: 80rpx;
        }

        .location-btn {
            position: absolute;
            right: 20rpx;
            top: 50%;
            transform: translateY(-50%);
            width: 60rpx;
            height: 60rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: rgba(137, 102, 239, 0.1);
            border-radius: 50%;

            &:active {
                background-color: rgba(137, 102, 239, 0.2);
                transform: translateY(-50%) scale(0.95);
            }
        }
    }
}

.save-button {
    width: 100%;
    height: 100rpx;
    background: linear-gradient(135deg, $theme-primary, $theme-primary-light);
    border-radius: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 40rpx 0;
    box-shadow: 0 6rpx 20rpx rgba(137, 102, 239, 0.3);
    transition: all 0.3s ease;

    text {
        font-size: 32rpx;
        color: #ffffff;
        font-weight: 600;
    }

    &:active {
        transform: translateY(2rpx);
        box-shadow: 0 4rpx 15rpx rgba(137, 102, 239, 0.4);
    }

    &.disabled {
        background: linear-gradient(135deg, #d6d7d8, #e0e1e2);
        box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

        text {
            color: #999;
        }

        &:active {
            transform: none;
        }
    }
}
</style>
