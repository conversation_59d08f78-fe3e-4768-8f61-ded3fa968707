<template>
    <view class="order-detail-page">
        <!-- 页面标题 -->
        <view class="page-header">
            <view class="header-content">
                <text class="page-title">订单详情</text>
                <text class="page-subtitle">{{ isShopOwner ? '管理您的店铺订单' : '查看订单信息' }}</text>
            </view>
            <view class="header-decoration">
                <view class="decoration-circle circle-1"></view>
                <view class="decoration-circle circle-2"></view>
            </view>
        </view>

        <!-- 加载状态 -->
        <view class="loading-container" v-if="loading">
            <u-loading mode="circle" size="60"></u-loading>
            <text class="loading-text">加载中...</text>
        </view>

        <!-- 错误状态 -->
        <view class="error-container" v-else-if="!orderInfo.id">
            <u-icon name="close-circle" size="120" color="#ccc"></u-icon>
            <text class="error-text">订单信息加载失败</text>
            <view class="retry-btn" @click="loadOrderDetail">重新加载</view>
        </view>

        <!-- 订单内容 -->
        <template v-else>
            <!-- 订单状态卡片 -->
            <view class="status-card">
                <view class="status-icon">
                    <u-icon :name="getStatusIcon(orderInfo.ms_status, orderInfo.cancel_time)" size="60" :color="getStatusColor(orderInfo.ms_status, orderInfo.cancel_time)"></u-icon>
                </view>
                <view class="status-content">
                    <text class="status-text">{{ getStatusText(orderInfo.ms_status, orderInfo.cancel_time) }}</text>
                    <text class="status-desc">{{ getStatusDesc(orderInfo.ms_status, orderInfo.cancel_time) }}</text>
                </view>
            </view>

            <!-- 商品信息 -->
            <view class="goods-card">
                <view class="card-header">
                    <view class="card-header-l">
                        <u-icon name="shopping-bag" size="32" color="#8966ef"></u-icon>
                        <text class="card-title">商品信息</text>
                    </view>
                </view>
                <view class="goods-content">
                    <view class="goods-image">
                        <u-image width="160rpx" height="160rpx" :src="orderInfo.sku.image" mode="aspectFill" border-radius="16rpx"></u-image>
                    </view>
                    <view class="goods-detail">
                        <view class="goods-title">{{ orderInfo.good.title || '商品已删除' }}</view>
                        <view class="goods-spec" v-if="orderInfo.sku.sku_name">规格：{{ orderInfo.sku.sku_name }}</view>
                        <view class="goods-price-info">
                            <view class="price-row">
                                <text class="label">单价：</text>
                                <text class="price">¥{{ orderInfo.sku.price }}</text>
                            </view>
                            <view class="price-row">
                                <text class="label">数量：</text>
                                <text class="quantity">x {{ orderInfo.num }}</text>
                            </view>
                            <view class="price-row total">
                                <text class="label">总计：</text>
                                <text class="total-price">¥{{ orderInfo.amount }}</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 订单信息 -->
            <view class="order-info-card">
                <view class="card-header">
                    <view class="card-header-l">
                        <u-icon name="file-text" size="32" color="#8966ef"></u-icon>
                        <text class="card-title">订单信息</text>
                    </view>
                </view>
                <view class="info-list">
                    <view class="info-item">
                        <text class="info-label">订单号</text>
                        <view class="info-value-with-copy">
                            <text class="info-value">{{ orderInfo.order_no }}</text>
                            <view class="copy-btn" @click="copyOrderNo">
                                <u-icon name="copy" size="24" color="#8966ef"></u-icon>
                            </view>
                        </view>
                    </view>
                    <view class="info-item">
                        <text class="info-label">下单时间</text>
                        <text class="info-value">{{ formatTime(orderInfo.addtime) }}</text>
                    </view>
                    <view class="info-item" v-if="orderInfo.pay_time">
                        <text class="info-label">付款时间</text>
                        <text class="info-value">{{ formatTime(orderInfo.pay_time) }}</text>
                    </view>
                    <view class="info-item">
                        <text class="info-label">支付方式</text>
                        <text class="info-value">{{ getPayTypeText(orderInfo.pay_type) }}</text>
                    </view>
                    <view class="info-item">
                        <text class="info-label">订单状态</text>
                        <text class="info-value status" :class="[getStatusClass(orderInfo.ms_status, orderInfo.cancel_time)]">
                            {{ getStatusText(orderInfo.ms_status, orderInfo.cancel_time) }}
                        </text>
                    </view>
                    <view class="info-item" v-if="orderInfo.buy_note">
                        <text class="info-label">订单备注</text>
                        <view class="info-value note-value">{{ orderInfo.buy_note }}</view>
                    </view>
                </view>
            </view>

            <!-- 收货地址信息 -->
            <view class="address-card" v-if="orderInfo.fh_address">
                <view class="card-header">
                    <view class="card-header-l">
                        <u-icon name="map" size="32" color="#8966ef"></u-icon>
                        <text class="card-title">收货地址</text>
                    </view>
                </view>
                <view class="address-content">
                    <view class="contact-info">
                        <text class="contact-name">{{ orderInfo.data_json.fh_name || '收货人' }}</text>
                        <text class="contact-phone">{{ orderInfo.fh_phone }}</text>
                    </view>
                    <view class="address-text">{{ orderInfo.fh_address }}</view>
                </view>
            </view>

            <!-- 物流信息 -->
            <view class="logistics-card" v-if="orderInfo.wu_no">
                <view class="card-header">
                    <view class="card-header-l">
                        <u-icon name="car" size="32" color="#8966ef"></u-icon>
                        <text class="card-title">物流信息</text>
                    </view>
                    <view class="card-header-r" v-if="orderInfo.wu_wc_time" @click="viewLogistics">
                        <view class="card-header-r-t">查看物流</view>
                    </view>
                </view>
                <view class="logistics-content">
                    <view class="logistics-item">
                        <text class="logistics-label">快递公司</text>
                        <text class="logistics-value">{{ orderInfo.wu_name || '未知快递' }}</text>
                    </view>
                    <view class="logistics-item">
                        <text class="logistics-label">快递单号</text>
                        <text class="logistics-value">{{ orderInfo.wu_no }}</text>
                    </view>
                    <view class="logistics-item" v-if="orderInfo.wu_fh_time">
                        <text class="logistics-label">发货时间</text>
                        <text class="logistics-value">{{ formatTime(orderInfo.wu_fh_time) }}</text>
                    </view>
                    <view class="logistics-item" v-if="orderInfo.wu_wc_time">
                        <text class="logistics-label">完成时间</text>
                        <text class="logistics-value">{{ formatTime(orderInfo.wu_wc_time) }}</text>
                    </view>
                </view>
            </view>

            <!-- 操作按钮 -->
            <view class="action-buttons">
                <!-- 用户操作 -->
                <template v-if="!isShopOwner">
                    <view class="action-btn secondary" v-if="orderInfo.web_user_id != userInfo.uid" @click="contactSeller">联系卖家</view>
                    <view class="action-btn primary" v-if="Number(orderInfo.ms_status) === 1 && !orderInfo.cancel_time" @click="payOrder">立即付款</view>
                    <view class="action-btn primary" v-if="Number(orderInfo.ms_status) === 3" @click="confirmReceipt">确认收货</view>
                    <!-- <view class="action-btn info" v-if="orderInfo.wu_no" @click="viewLogistics">查看物流</view> -->
                    <view class="action-btn warning" v-if="canRefund(orderInfo.ms_status, orderInfo.cancel_time)" @click="requestRefund">申请退款</view>
                    <!-- 发货后需要退款时提示联系商家 -->
                    <view class="action-btn secondary" v-if="needContactSellerForRefund(orderInfo.ms_status, orderInfo.cancel_time)" @click="contactSellerForRefund">联系商家退款</view>
                </template>

                <!-- 商户操作 -->
                <template v-else>
                    <!-- <view class="action-btn secondary" @click="contactBuyer">联系买家</view> -->
                    <view class="action-btn primary" v-if="Number(orderInfo.ms_status) === 2" @click="shipOrder">发货</view>
                    <!-- <view class="action-btn info" v-if="orderInfo.wu_no" @click="viewLogistics">查看物流</view> -->
                    <view class="action-btn warning" v-if="canRefund(orderInfo.ms_status, orderInfo.cancel_time) && Number(orderInfo.pay_amount) <= 0" @click="requestRefund">申请退款</view>
                </template>
            </view>
        </template>
    </view>
</template>

<script>
import { mapState } from 'vuex';
import { getShopGoodOrderDetail, userApplyRefund, storeApplyRefund, userConfirmGood } from '@/utils/vmeitime-http/shop.js';

export default {
    data() {
        return {
            orderId: '',
            orderInfo: {},
            loading: false,
            isShopOwner: false, // 是否为店铺主人
        };
    },
    computed: {
        ...mapState(['userInfo']),
    },
    onLoad(options) {
        this.orderId = options.orderId;
        // 根据来源页面判断是否为店铺主人
        this.isShopOwner = options.type === 'shop' || getCurrentPages().some(page => page.route.includes('shopOrders'));

        if (!this.orderId) {
            uni.showToast({
                title: '订单ID不能为空',
                icon: 'none',
            });
            setTimeout(() => {
                uni.navigateBack();
            }, 1500);
            return;
        }

        this.loadOrderDetail();
    },

    onPullDownRefresh() {
        this.loadOrderDetail();
        uni.stopPullDownRefresh();
    },
    methods: {
        // 加载订单详情
        async loadOrderDetail() {
            if (!this.orderId) {
                uni.showToast({
                    title: '订单ID不能为空',
                    icon: 'none',
                });
                return;
            }

            this.loading = true;
            uni.showLoading({
                title: '加载中...',
            });

            try {
                const res = await getShopGoodOrderDetail({
                    access_token: uni.getStorageSync('token'),
                    order_id: this.orderId,
                });

                uni.hideLoading();

                this.orderInfo = res.data.info || {};
                console.log(this.orderInfo, 200000);
            } catch (error) {
                uni.hideLoading();
                console.error('加载订单详情失败:', error);
                uni.showToast({
                    title: '网络错误，请重试',
                    icon: 'none',
                });
            } finally {
                this.loading = false;
            }
        },

        // 获取状态图标
        getStatusIcon(status, cancelTime) {
            console.log(status, 200000000);
            // 如果是待付款状态且存在取消时间，则显示为已取消图标
            if (Number(status) === 1 && cancelTime) {
                return 'close-circle';
            }

            const iconMap = {
                0: 'clock',
                1: 'clock',
                2: 'shopping-bag',
                3: 'car',
                5: 'checkmark-circle',
                4: 'checkmark-circle',
            };
            return iconMap[status] || 'help-circle';
        },

        // 获取状态颜色
        getStatusColor(status, cancelTime) {
            // 如果是待付款状态且存在取消时间，则显示为已取消颜色
            if (Number(status) === 1 && cancelTime) {
                return '#6c757d';
            }

            const colorMap = {
                0: '#ffc107',
                1: '#ffc107',
                2: '#8966ef',
                3: '#4cd964',
                5: '#4cd964',
                4: '#dc3545',
            };
            return colorMap[status] || '#999';
        },

        // 获取状态文本
        getStatusText(status, cancelTime) {
            console.log(status);
            // 如果是待付款状态且存在取消时间，则显示为已取消
            if (Number(status) === 1 && cancelTime) {
                return '已取消';
            }

            const textMap = {
                0: '待付款',
                1: '待付款',
                2: '待发货',
                3: '待收货',
                5: '已完成',
                4: '退款完成',
            };
            return textMap[status] || '未知状态';
        },

        // 获取状态描述
        getStatusDesc(status, cancelTime) {
            // 如果是待付款状态且存在取消时间，则显示为已取消描述
            if (Number(status) === 1 && cancelTime) {
                return '订单已取消';
            }

            const descMap = {
                0: '请尽快完成付款',
                1: '请尽快完成付款',
                2: '商家正在准备发货',
                3: '商品正在配送中',
                5: '订单已完成',
                4: '退款处理中',
            };
            return descMap[status] || '';
        },

        // 获取状态样式类
        getStatusClass(status, cancelTime) {
            // 如果是待付款状态且存在取消时间，则显示为已取消状态
            if (Number(status) === 1 && cancelTime) {
                return 'status-cancelled';
            }

            const classMap = {
                0: 'status-pending',
                1: 'status-pending',
                2: 'status-processing',
                3: 'status-shipped',
                5: 'status-completed',
                4: 'status-refund',
            };
            return classMap[status] || 'status-unknown';
        },

        // 获取支付方式文本
        getPayTypeText(payType) {
            const typeMap = {
                weixin: '微信支付',
                alipay: '支付宝',
                balance: '余额支付',
            };
            return typeMap[payType] || '未知支付方式';
        },

        // 格式化时间显示
        formatTime(timeStr) {
            if (!timeStr) return '--';
            try {
                const date = new Date(timeStr);
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const hours = String(date.getHours()).padStart(2, '0');
                const minutes = String(date.getMinutes()).padStart(2, '0');
                return `${year}-${month}-${day} ${hours}:${minutes}`;
            } catch (error) {
                return timeStr;
            }
        },

        // 格式化金额显示
        formatAmount(amount) {
            console.log(amount, 2000);
            if (!amount && Number(amount) !== 0) return '0.00';
            return Number(amount).toFixed(2);
        },

        // 判断是否可以申请退款
        canRefund(status, cancelTime) {
            status = Number(status);
            // 如果是已取消的订单，不能申请退款
            if (status === 1 && cancelTime) {
                return false;
            }

            if (this.isShopOwner) {
                // 商家除了待付款(0,1)和已退款(4)状态，都可以申请退款
                return ![0, 1, 4].includes(status);
            } else {
                // 用户只能在待发货状态申请退款，发货后不可申请退款
                return [2].includes(status);
            }
        },

        // 判断是否需要联系商家退款（发货后的状态）
        needContactSellerForRefund(status, cancelTime) {
            status = Number(status);
            // 如果是已取消的订单，不需要联系商家退款
            if (status === 1 && cancelTime) {
                return false;
            }
            // 用户在待收货、已完成状态需要联系商家退款
            return [3, 5].includes(status);
        },

        // 复制订单号
        copyOrderNo() {
            if (!this.orderInfo.order_no) {
                uni.showToast({
                    title: '订单号为空',
                    icon: 'none',
                });
                return;
            }

            uni.setClipboardData({
                data: this.orderInfo.order_no,
                success: () => {
                    uni.showToast({
                        title: '订单号已复制',
                        icon: 'success',
                    });
                },
                fail: () => {
                    uni.showToast({
                        title: '复制失败',
                        icon: 'none',
                    });
                },
            });
        },

        // 联系卖家
        contactSeller(order) {
            uni.showModal({
                title: '联系卖家',
                content: '是否要联系卖家？',
                success: async res => {
                    if (res.confirm) {
                        const infoRes = await this.$api.getOtherUserInfo({
                            access_token: uni.getStorageSync('token'),
                            to_user_id: this.orderInfo.web_user_id,
                        });
                        const info = infoRes.data;
                        // 这里可以跳转到聊天页面或显示卖家联系方式
                        const res = await this.$api.initSession({
                            access_token: uni.getStorageSync('token'),
                            to_uid: this.orderInfo.web_user_id,
                        });
                        const sessionId = res.data.msg_session_id;
                        // 导航到聊天页面
                        uni.navigateTo({
                            url: `/views/moments/chat?sessionId=${sessionId}&userId=${info.uid}&username=${encodeURIComponent(info.nickname)}&avatar=${encodeURIComponent(info.headimgurl || '')}`,
                        });
                    }
                },
            });
        },

        // 联系商家退款
        contactSellerForRefund() {
            uni.showModal({
                title: '联系商家退款',
                content: '商品已发货，如需退款请联系商家处理。是否要联系商家？',
                confirmText: '联系商家',
                cancelText: '取消',
                success: res => {
                    if (res.confirm) {
                        // 这里可以跳转到聊天页面或显示卖家联系方式
                        uni.showToast({
                            title: '功能开发中',
                            icon: 'none',
                        });
                    }
                },
            });
        },

        // 联系买家
        contactBuyer() {
            uni.showModal({
                title: '联系买家',
                content: `买家联系方式：${this.orderInfo.fh_phone}`,
                showCancel: false,
            });
        },

        // 立即付款
        payOrder() {
            if (!this.userInfo.openid) {
                uni.showToast({
                    title: '请先登录',
                    icon: 'none',
                });
                return;
            }

            uni.showModal({
                title: '确认付款',
                content: `确认支付 ¥${this.orderInfo.amount}？`,
                success: async res => {
                    if (res.confirm) {
                        uni.showLoading({
                            title: '支付中...',
                        });

                        try {
                            const payRes = await this.$api.createMiniPay({
                                openid: this.userInfo.openid,
                                orderid: this.orderInfo.order_no,
                                access_token: uni.getStorageSync('token'),
                            });

                            if (payRes.status === 0) {
                                await this.requestPayment(payRes.data);
                                uni.hideLoading();
                                uni.showToast({
                                    title: '支付成功',
                                    icon: 'success',
                                });
                                // 延迟刷新订单详情
                                setTimeout(() => {
                                    this.loadOrderDetail();
                                }, 1000);
                            } else {
                                uni.hideLoading();
                                uni.showToast({
                                    title: payRes.msg || '支付失败',
                                    icon: 'none',
                                });
                            }
                        } catch (error) {
                            uni.hideLoading();
                            console.error('支付失败:', error);
                            uni.showToast({
                                title: '支付失败，请重试',
                                icon: 'none',
                            });
                        }
                    }
                },
            });
        },

        // 调用微信支付
        async requestPayment(paymentData) {
            return new Promise((resolve, reject) => {
                uni.requestPayment({
                    provider: 'wxpay',
                    timeStamp: paymentData.timeStamp,
                    nonceStr: paymentData.nonceStr,
                    package: paymentData.package,
                    signType: paymentData.signType,
                    paySign: paymentData.paySign,
                    success: resolve,
                    fail: reject,
                });
            });
        },

        // 确认收货
        confirmReceipt() {
            uni.showModal({
                title: '确认收货',
                content: '确认已收到商品？收货后订单将完成，无法撤销。',
                success: async res => {
                    if (res.confirm) {
                        await this.handleConfirmReceipt();
                    }
                },
            });
        },

        // 处理确认收货
        async handleConfirmReceipt() {
            uni.showLoading({ title: '确认中...' });
            try {
                const res = await userConfirmGood({
                    access_token: uni.getStorageSync('token'),
                    order_id: this.orderId,
                });

                uni.hideLoading();
                if (res.status === 0) {
                    uni.showToast({
                        title: '确认收货成功',
                        icon: 'success',
                    });
                    this.loadOrderDetail();
                } else {
                    uni.showToast({
                        title: res.msg || '确认收货失败',
                        icon: 'none',
                    });
                }
            } catch (error) {
                uni.hideLoading();
                uni.showToast({
                    title: '网络错误，请重试',
                    icon: 'none',
                });
            }
        },

        // 发货
        shipOrder() {
            uni.navigateTo({
                url: `/views/shop/shipOrder?orderId=${this.orderId}`,
            });
        },

        // 查看物流
        viewLogistics() {
            uni.navigateTo({
                url: `/views/shop/logistics?orderId=${this.orderId}&trackingNo=${this.orderInfo.wu_no}`,
            });
        },

        // 申请退款
        requestRefund() {
            uni.showModal({
                title: '申请退款',
                content: `确认要对订单 ${this.orderInfo.order_no} 申请退款吗？\n退款金额：¥${this.orderInfo.amount}`,
                success: async res => {
                    if (res.confirm) {
                        await this.handleRefund();
                    }
                },
            });
        },

        // 处理退款申请
        async handleRefund() {
            uni.showLoading({ title: '申请中...' });
            try {
                const refundApi = this.isShopOwner ? storeApplyRefund : userApplyRefund;
                const res = await refundApi({
                    access_token: uni.getStorageSync('token'),
                    order_id: this.orderId,
                });

                uni.hideLoading();
                if (res.status === 0) {
                    uni.showToast({
                        title: '退款申请成功',
                        icon: 'success',
                    });
                    this.loadOrderDetail();
                } else {
                    uni.showToast({
                        title: res.msg || '申请失败',
                        icon: 'none',
                    });
                }
            } catch (error) {
                uni.hideLoading();
                uni.showToast({
                    title: '网络错误，请重试',
                    icon: 'none',
                });
            }
        },
    },
};
</script>

<style lang="scss" scoped>
// 引入项目主题色
$theme-primary: #8966ef;
$theme-primary-light: #a584f2;
$theme-secondary: #f0cc6c;
$bg-grey: #f8f8f8;
$border-color: #f0f0f0;
$text-color: #333;
$text-color-light: #666;
$text-color-placeholder: #999;

.order-detail-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);
    padding-bottom: 220rpx;
}

// 页面头部
.page-header {
    position: relative;
    padding: 40rpx 30rpx 30rpx;
    background: linear-gradient(135deg, #8966ef, #a584f2);
    overflow: hidden;

    .header-content {
        position: relative;
        z-index: 2;

        .page-title {
            display: block;
            font-size: 48rpx;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8rpx;
        }

        .page-subtitle {
            display: block;
            font-size: 28rpx;
            color: rgba(255, 255, 255, 0.8);
        }
    }

    .header-decoration {
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        z-index: 1;

        .decoration-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);

            &.circle-1 {
                width: 120rpx;
                height: 120rpx;
                top: -30rpx;
                right: 50rpx;
                animation: float 6s ease-in-out infinite;
            }

            &.circle-2 {
                width: 80rpx;
                height: 80rpx;
                top: 60rpx;
                right: 150rpx;
                animation: float 4s ease-in-out infinite reverse;
            }
        }
    }
}

// 状态卡片
.status-card {
    margin: 20rpx;
    background: white;
    border-radius: 16rpx;
    padding: 40rpx 30rpx;
    display: flex;
    align-items: center;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    border: 1rpx solid $border-color;

    .status-icon {
        margin-right: 24rpx;
    }

    .status-content {
        flex: 1;

        .status-text {
            display: block;
            font-size: 36rpx;
            font-weight: 600;
            color: $text-color;
            margin-bottom: 8rpx;
        }

        .status-desc {
            display: block;
            font-size: 26rpx;
            color: $text-color-light;
        }
    }
}

// 通用卡片样式
.goods-card,
.order-info-card,
.address-card,
.logistics-card {
    margin: 20rpx;
    background: white;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    border: 1rpx solid $border-color;

    .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24rpx 30rpx;
        border-bottom: 1rpx solid #f5f5f5;
        background: #fafafa;

        .card-title {
            margin-left: 12rpx;
            font-size: 32rpx;
            font-weight: 600;
            color: $text-color;
        }
        .card-header-l {
            display: flex;
            align-items: center;
        }
        .card-header-r {
            display: flex;
            align-items: center;
            color: $theme-primary;
        }
    }
}

// 商品信息卡片
.goods-content {
    padding: 30rpx;
    display: flex;

    .goods-image {
        margin-right: 24rpx;
    }

    .goods-detail {
        flex: 1;

        .goods-title {
            font-size: 32rpx;
            color: $text-color;
            font-weight: 500;
            margin-bottom: 16rpx;
            line-height: 1.4;
        }

        .goods-spec {
            font-size: 26rpx;
            color: $text-color-light;
            margin-bottom: 20rpx;
            padding: 6rpx 16rpx;
            background: #f8f9fa;
            border-radius: 12rpx;
            display: inline-block;
        }

        .goods-price-info {
            .price-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12rpx;

                .label {
                    font-size: 28rpx;
                    color: $text-color-light;
                }

                .price,
                .quantity {
                    font-size: 28rpx;
                    color: $text-color;
                }

                &.total {
                    padding-top: 12rpx;
                    border-top: 1rpx solid #f0f0f0;
                    margin-bottom: 0;

                    .label {
                        font-weight: 600;
                        color: $text-color;
                    }

                    .total-price {
                        font-size: 32rpx;
                        font-weight: 600;
                        color: #ff4757;
                    }
                }
            }
        }
    }
}

// 订单信息列表
.info-list {
    padding: 0 30rpx 20rpx;

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20rpx 0;
        border-bottom: 1rpx solid #f5f5f5;

        &:last-child {
            border-bottom: none;
        }

        .info-label {
            font-size: 28rpx;
            color: $text-color-light;
        }

        .info-value {
            font-size: 28rpx;
            color: $text-color;
            text-align: right;
            flex: 1;
            margin-left: 20rpx;

            &.status {
                font-weight: 500;

                &.status-pending {
                    color: #ffc107;
                }

                &.status-processing {
                    color: $theme-primary;
                }

                &.status-shipped {
                    color: #4cd964;
                }

                &.status-completed {
                    color: #4cd964;
                }

                &.status-refund {
                    color: #dc3545;
                }

                &.status-cancelled {
                    color: #6c757d;
                }
            }

            &.note-value {
                text-align: left;
                background: rgba(137, 102, 239, 0.05);
                padding: 16rpx 20rpx;
                border-radius: 12rpx;
                // border-left: 4rpx solid $theme-primary;
                line-height: 1.5;
                margin-top: 8rpx;
            }
        }

        .info-value-with-copy {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            flex: 1;
            margin-left: 20rpx;

            .info-value {
                margin: 0;
                margin-right: 12rpx;
            }

            .copy-btn {
                padding: 8rpx;
                border-radius: 8rpx;
                transition: all 0.3s ease;

                &:active {
                    background: rgba(137, 102, 239, 0.1);
                    transform: scale(0.95);
                }
            }
        }
    }
}

// 收货地址内容
.address-content {
    padding: 30rpx;

    .contact-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;

        .contact-name {
            font-size: 32rpx;
            font-weight: 600;
            color: $text-color;
        }

        .contact-phone {
            font-size: 28rpx;
            color: $theme-primary;
            font-weight: 500;
        }
    }

    .address-text {
        font-size: 28rpx;
        color: $text-color-light;
        line-height: 1.5;
        padding: 20rpx;
        background: #f8f9fa;
        border-radius: 12rpx;
        // border-left: 4rpx solid $theme-primary;
    }
}

// 物流信息内容
.logistics-content {
    padding: 30rpx;

    .logistics-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16rpx 0;
        border-bottom: 1rpx solid #f5f5f5;

        &:last-child {
            border-bottom: none;
        }

        .logistics-label {
            font-size: 28rpx;
            color: $text-color-light;
        }

        .logistics-value {
            font-size: 28rpx;
            color: $text-color;
            text-align: right;
            flex: 1;
            margin-left: 20rpx;
        }
    }
}

// 操作按钮
.action-buttons {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    padding: 20rpx 30rpx;
    padding-bottom: calc(env(safe-area-inset-bottom));
    border-top: 1rpx solid $border-color;
    display: flex;
    gap: 20rpx;
    box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.04);

    .action-btn {
        flex: 1;
        padding: 24rpx 0;
        border-radius: 24rpx;
        font-size: 28rpx;
        font-weight: 500;
        text-align: center;
        border: 1rpx solid;
        transition: all 0.3s ease;

        &.primary {
            background: $theme-primary;
            color: white;
            border-color: $theme-primary;

            &:active {
                background: darken($theme-primary, 10%);
                transform: scale(0.98);
            }
        }

        &.secondary {
            background: white;
            color: $theme-primary;
            border-color: $theme-primary;

            &:active {
                background: rgba(137, 102, 239, 0.1);
                transform: scale(0.98);
            }
        }

        &.info {
            background: white;
            color: #17a2b8;
            border-color: #17a2b8;

            &:active {
                background: rgba(23, 162, 184, 0.1);
                transform: scale(0.98);
            }
        }

        &.warning {
            background: white;
            color: #f0ad4e;
            border-color: #f0ad4e;

            &:active {
                background: rgba(240, 173, 78, 0.1);
                transform: scale(0.98);
            }
        }
    }
}

// 动画效果
@keyframes float {
    0%,
    100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10rpx);
    }
}

// 加载状态
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;

    .loading-text {
        margin-top: 20rpx;
        font-size: 28rpx;
        color: $text-color-light;
    }
}

// 错误状态
.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;

    .error-text {
        margin: 20rpx 0 40rpx;
        font-size: 28rpx;
        color: $text-color-light;
    }

    .retry-btn {
        padding: 16rpx 40rpx;
        background: $theme-primary;
        color: white;
        border-radius: 24rpx;
        font-size: 28rpx;
        font-weight: 500;
        transition: all 0.3s ease;

        &:active {
            background: darken($theme-primary, 10%);
            transform: scale(0.98);
        }
    }
}

// 响应式调整
@media screen and (max-width: 750rpx) {
    .goods-content {
        flex-direction: column;

        .goods-image {
            margin-right: 0;
            margin-bottom: 20rpx;
            align-self: center;
        }
    }

    .action-buttons {
        flex-direction: column;
        gap: 16rpx;

        .action-btn {
            flex: none;
        }
    }
}
</style>
