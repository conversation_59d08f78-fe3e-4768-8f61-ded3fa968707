<template>
    <view class="likes-list-container">
        <view class="empty-state" v-if="likesList.length === 0">
            <view class="empty-content">
                <image class="empty-image" src="https://static.wdaoyun.com/friend/2025-07-02/ef0fac060cb6f1abfde647fe1a55776d.png" mode="aspectFit"></image>
                <view class="empty-tip">暂无点赞收藏记录~</view>
            </view>
        </view>

        <view class="like-item" v-for="(item, index) in likesList" :key="index" @tap="viewContent(item)">
            <view class="avatar-wrapper">
                <u-avatar :src="getUserAvatar(item)" size="108"></u-avatar>
            </view>
            <view class="like-content">
                <view class="top-info">
                    <text class="username">{{ getUserName(item) }}</text>
                </view>
                <view class="like-description">
                    <text class="description" v-if="item.type === '2'">赞了你的{{ getLikeType(item) }} {{ formatLikeTime(item.addtime) }}</text>
                    <text class="description" v-else>收藏了你的{{ getLikeType(item) }} {{ formatLikeTime(item.addtime) }}</text>
                </view>
            </view>
            <view class="action-area">
                <view class="work-image" v-if="getWorkImage(item)">
                    <image :src="getWorkImage(item)" mode="aspectFill" class="content-image"></image>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
export default {
    data() {
        return {
            page: 1,
            pagesize: 10,
            total: 0,
            likesList: [],
            currentUid: '', // 当前用户ID
        };
    },
    computed: {
        ...mapState(['userInfo']),
    },
    onLoad() {
        // 获取当前用户ID
        this.currentUid = this.userInfo.uid || '';
        this.getLikesList();
    },
    onPullDownRefresh() {
        this.page = 1;
        this.getLikesList();
        uni.stopPullDownRefresh();
    },
    onReachBottom() {
        if (this.likesList.length < this.total) {
            this.page++;
            this.getLikesList();
        }
    },
    methods: {
        async getLikesList() {
            try {
                const res = await this.$api.getLikeMsg({
                    page: this.page,
                    pagesize: this.pagesize,
                    access_token: uni.getStorageSync('token'),
                });

                if (res && res.data && res.data.list) {
                    if (this.page === 1) {
                        this.likesList = res.data.list;
                    } else {
                        this.likesList = [...this.likesList, ...res.data.list];
                    }
                    this.total = parseInt(res.data.total || 0);
                }
            } catch (error) {
                console.error('获取点赞收藏列表失败', error);
                uni.showToast({
                    title: '获取点赞收藏列表失败',
                    icon: 'none',
                });
            }
        },
        // 获取用户头像
        getUserAvatar(item) {
            return item.to_user && item.to_user.headimgurl ? item.to_user.headimgurl : '/static/images/we.png';
        },
        // 获取用户名称
        getUserName(item) {
            return item.to_user && item.to_user.nickname ? item.to_user.nickname : '微信用户';
        },
        // 获取作品图片
        getWorkImage(item) {
            return item.like && item.like.work && item.like.work.img ? item.like.work.img : '';
        },
        // 获取点赞/收藏的内容类型
        getLikeType(item) {
            if (item.like && item.like.class_name) {
                const types = {
                    work: '作品',
                    dynamic: '动态',
                    article: '文章',
                    comment: '评论',
                };
                return types[item.like.class_name] || '内容';
            }
            return '内容';
        },
        // 格式化时间
        formatLikeTime(timestamp) {
            if (!timestamp) return '';

            // 处理时间戳或日期字符串
            let likeDate;
            if (typeof timestamp === 'string') {
                likeDate = new Date(timestamp.replace(/-/g, '/'));
            } else {
                likeDate = new Date(timestamp);
            }

            const now = new Date();
            const diff = now - likeDate;

            // 小于1分钟
            if (diff < 60 * 1000) {
                return '刚刚';
            }

            // 小于1小时
            if (diff < 60 * 60 * 1000) {
                return Math.floor(diff / (60 * 1000)) + '分钟前';
            }

            // 小于24小时
            if (diff < 24 * 60 * 60 * 1000) {
                return Math.floor(diff / (60 * 60 * 1000)) + '小时前';
            }

            // 小于7天
            if (diff < 7 * 24 * 60 * 60 * 1000) {
                return Math.floor(diff / (24 * 60 * 60 * 1000)) + '天前';
            }

            // 格式化为年月日
            const year = likeDate.getFullYear();
            const month = (likeDate.getMonth() + 1).toString().padStart(2, '0');
            const day = likeDate.getDate().toString().padStart(2, '0');

            return `${year}-${month}-${day}`;
        },
        // 查看被点赞/收藏的内容
        viewContent(item) {
            if (!item.like || !item.like.class_name || !item.like.class_id) {
                uni.showToast({
                    title: '内容信息不完整',
                    icon: 'none',
                });
                return;
            }

            const classType = item.like.class_name;
            const contentId = item.like.class_id;

            // 根据内容类型跳转到不同页面
            switch (classType) {
                case 'work':
                    uni.navigateTo({
                        url: `/views/moments/detail?id=${contentId}&type=work`,
                    });
                    break;
                case 'dynamic':
                    uni.navigateTo({
                        url: `/views/moments/detail?id=${contentId}&type=dynamic`,
                    });
                    break;
                case 'article':
                    uni.navigateTo({
                        url: `/views/article/detail?id=${contentId}`,
                    });
                    break;
                case 'comment':
                    // 查找评论所属的内容并跳转
                    uni.navigateTo({
                        url: `/views/moments/detail?id=${contentId}&showComment=1`,
                    });
                    break;
                default:
                    uni.showToast({
                        title: '暂不支持查看此类内容',
                        icon: 'none',
                    });
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.likes-list-container {
    min-height: 100vh;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);
    padding-bottom: 30rpx;

    .no-data {
        height: 300rpx;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .like-item {
        display: flex;
        padding: 24rpx;
        align-items: center;
        background-color: #ffffff;
        border-bottom: 1px solid #f2f2f2;
        margin: 20rpx 20rpx 0;
        .avatar-wrapper {
            position: relative;
            margin-right: 24rpx;
        }

        .like-content {
            flex: 1;
            overflow: hidden;

            .top-info {
                display: flex;
                justify-content: space-between;
                margin-bottom: 12rpx;

                .username {
                    font-size: 32rpx;
                    color: #333333;
                    font-weight: 400;
                }
            }

            .like-description {
                .description {
                    font-size: 26rpx;
                    color: #666666;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                }
            }
        }

        .action-area {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            justify-content: center;

            .work-image {
                width: 120rpx;
                height: 120rpx;
                border-radius: 8rpx;
                overflow: hidden;
                margin-left: 16rpx;
                box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);

                .content-image {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
        }
    }
}

.empty-state {
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    text-align: center;

    .empty-content {
        // margin: 100rpx 30rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .empty-image {
            width: 400rpx;
            height: 314rpx;
            margin-bottom: 40rpx;
        }

        .empty-text {
            color: #999;
            font-size: 32rpx;
            margin-bottom: 16rpx;
            text-align: center;
        }

        .empty-tip {
            color: #8966ef;
            font-size: 28rpx;
            text-align: center;
        }
    }
}
</style>
