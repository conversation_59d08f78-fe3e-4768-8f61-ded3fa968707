<template>
    <view class="ship-order-page">
        <!-- 页面标题 -->
        <view class="page-header">
            <view class="header-title">订单发货</view>
            <view class="header-subtitle">填写物流信息完成发货</view>
        </view>

        <!-- 订单信息卡片 -->
        <view class="order-info-card">
            <view class="card-title">
                <u-icon name="file-text" size="32" color="#8966ef"></u-icon>
                <text>订单信息</text>
            </view>
            <view class="order-summary">
                <view class="order-no">订单号：{{ orderInfo.order_no }}</view>
                <view class="goods-info">
                    <view class="goods-image">
                        <u-image width="80rpx" height="80rpx" :src="orderInfo.sku.image" mode="aspectFill" border-radius="8rpx"></u-image>
                    </view>
                    <view class="goods-detail">
                        <view class="goods-title">{{ orderInfo.good.title || '商品已删除' }}</view>
                        <view class="goods-spec" v-if="orderInfo.sku.sku_name">{{ orderInfo.sku.sku_name }}</view>
                        <view class="goods-price">¥{{ orderInfo.amount }} × {{ orderInfo.num }}</view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 收货地址信息 -->
        <view class="address-card">
            <view class="card-title">
                <u-icon name="map" size="32" color="#8966ef"></u-icon>
                <text>收货地址</text>
            </view>
            <view class="address-info">
                <view class="contact-info">
                    <text class="name">{{ orderInfo.data_json.fh_name }}</text>
                    <text class="phone">{{ orderInfo.fh_phone }}</text>
                </view>
                <view class="address-text">{{ formatAddress(orderInfo.fh_address) }}</view>
            </view>
        </view>

        <!-- 物流信息表单 -->
        <view class="logistics-form">
            <view class="card-title">
                <u-icon name="car" size="32" color="#8966ef"></u-icon>
                <text>物流信息</text>
            </view>

            <view class="form-item">
                <view class="form-label">物流公司</view>
                <view class="form-input">
                    <u-input v-model="logisticsForm.company" placeholder="请输入物流公司名称" :border="false"></u-input>
                </view>
            </view>

            <view class="form-item">
                <view class="form-label">快递单号</view>
                <view class="form-input">
                    <u-input v-model="logisticsForm.trackingNo" placeholder="请输入快递单号" :border="false"></u-input>
                </view>
            </view>

            <!-- <view class="form-item">
                <view class="form-label">发货备注</view>
                <view class="form-textarea">
                    <u-input v-model="logisticsForm.remark" type="textarea" placeholder="选填，可备注发货说明" :border="false" height="120" maxlength="200"></u-input>
                </view>
            </view> -->
        </view>

        <!-- 温馨提示 -->
        <view class="tips-card">
            <view class="tips-title">
                <u-icon name="info-circle" size="28" color="#f0ad4e"></u-icon>
                <text>温馨提示</text>
            </view>
            <view class="tips-content">
                <text>• 请确保物流信息准确无误</text>
                <text>• 发货后买家将收到物流通知</text>
                <text>• 建议选择有保障的快递公司</text>
                <text>• 贵重物品建议购买运费险</text>
            </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-buttons">
            <view class="btn-row">
                <!-- <view class="action-btn secondary" @click="checkLogistics">验证物流</view> -->
                <view class="action-btn primary" @click="confirmShip">确认发货</view>
            </view>
        </view>
    </view>
</template>

<script>
import { getShopGoodOrderDetail, checkWuNo, sendGood } from '@/utils/vmeitime-http/shop.js';

export default {
    data() {
        return {
            orderId: '',
            orderInfo: {},
            loading: false,

            // 物流表单数据
            logisticsForm: {
                company: '',
                trackingNo: '',
                remark: '',
            },
        };
    },
    onLoad(options) {
        this.orderId = options.orderId;
        if (this.orderId) {
            this.loadOrderDetail();
        }
    },
    methods: {
        // 加载订单详情
        async loadOrderDetail() {
            if (this.loading) return;

            this.loading = true;
            uni.showLoading({ title: '加载中...' });

            try {
                const res = await getShopGoodOrderDetail({
                    access_token: uni.getStorageSync('token'),
                    order_id: this.orderId,
                });

                this.orderInfo = res.data.info || {};
            } catch (error) {
                console.error('加载订单详情失败:', error);
                uni.showToast({
                    title: '网络错误，请重试',
                    icon: 'none',
                });
            } finally {
                this.loading = false;
                uni.hideLoading();
            }
        },

        // 获取收货人姓名
        getReceiverName(address) {
            // 根据实际地址格式解析收货人姓名
            return '收货人'; // 临时返回
        },

        // 格式化地址
        formatAddress(address) {
            if (typeof address === 'string') {
                return address;
            }
            return address || '';
        },

        // 验证物流信息
        async checkLogistics() {
            if (!this.logisticsForm.trackingNo.trim()) {
                uni.showToast({
                    title: '请输入快递单号',
                    icon: 'none',
                });
                return;
            }

            uni.showLoading({ title: '验证中...' });

            try {
                const res = await checkWuNo({
                    access_token: uni.getStorageSync('token'),
                    order_id: this.orderId,
                    wu_no: this.logisticsForm.trackingNo,
                });

                if (res.status === 0) {
                    uni.showToast({
                        title: '物流信息验证成功',
                        icon: 'success',
                    });
                } else {
                    uni.showToast({
                        title: res.msg || '验证失败',
                        icon: 'none',
                    });
                }
            } catch (error) {
                console.error('验证物流信息失败:', error);
                uni.showToast({
                    title: '验证失败，请重试',
                    icon: 'none',
                });
            } finally {
                uni.hideLoading();
            }
        },

        // 确认发货
        async confirmShip() {
            // 表单验证
            if (!this.logisticsForm.company.trim()) {
                uni.showToast({
                    title: '请输入物流公司',
                    icon: 'none',
                });
                return;
            }

            if (!this.logisticsForm.trackingNo.trim()) {
                uni.showToast({
                    title: '请输入快递单号',
                    icon: 'none',
                });
                return;
            }

            uni.showModal({
                title: '确认发货',
                content: '确认已发货并填写了正确的物流信息？',
                success: async res => {
                    if (res.confirm) {
                        await this.submitShipInfo();
                    }
                },
            });
        },

        // 提交发货信息
        async submitShipInfo() {
            uni.showLoading({ title: '发货中...' });

            try {
                const check = await checkWuNo({
                    access_token: uni.getStorageSync('token'),
                    order_id: this.orderId,
                    wu_no: this.logisticsForm.trackingNo,
                });
                await sendGood({
                    access_token: uni.getStorageSync('token'),
                    order_id: this.orderId,
                    wu_no: this.logisticsForm.trackingNo,
                });

                uni.showToast({
                    title: '发货成功',
                    icon: 'success',
                });

                setTimeout(() => {
                    uni.navigateBack();
                }, 1500);
            } catch (error) {
                console.error('发货失败:', error);
                uni.showToast({
                    title: error.msg,
                    icon: 'none',
                });
            } finally {
                uni.hideLoading();
            }
        },
    },
};
</script>

<style lang="scss" scoped>
// 引入项目主题色
$theme-primary: #8966ef;
$theme-primary-light: #a584f2;
$theme-secondary: #f0cc6c;
$bg-grey: #f8f8f8;
$border-color: #f0f0f0;
$text-color: #333;
$text-color-light: #666;
$text-color-placeholder: #999;

.ship-order-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);
    padding-bottom: 120rpx;
}

.page-header {
    background: linear-gradient(135deg, $theme-primary, $theme-primary-light);
    padding: 40rpx 30rpx 30rpx;
    color: white;

    .header-title {
        font-size: 36rpx;
        font-weight: 600;
        margin-bottom: 8rpx;
    }

    .header-subtitle {
        font-size: 26rpx;
        opacity: 0.9;
    }
}

.order-info-card,
.address-card,
.logistics-form,
.tips-card {
    background: white;
    border-radius: 16rpx;
    padding: 30rpx;
    margin: 20rpx;
    border: 1rpx solid $border-color;
}

.card-title {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid #f5f5f5;

    text {
        margin-left: 12rpx;
        font-size: 30rpx;
        font-weight: 600;
        color: $text-color;
    }
}

.order-summary {
    .order-no {
        font-size: 28rpx;
        color: $text-color-light;
        margin-bottom: 20rpx;
    }

    .goods-info {
        display: flex;
        align-items: center;

        .goods-image {
            margin-right: 20rpx;
        }

        .goods-detail {
            flex: 1;

            .goods-title {
                font-size: 28rpx;
                color: $text-color;
                font-weight: 500;
                margin-bottom: 8rpx;
                line-height: 1.4;
            }

            .goods-spec {
                font-size: 24rpx;
                color: $text-color-light;
                margin-bottom: 8rpx;
            }

            .goods-price {
                font-size: 26rpx;
                color: #ff4757;
                font-weight: 500;
            }
        }
    }
}

.address-info {
    .contact-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12rpx;

        .name {
            font-size: 28rpx;
            color: $text-color;
            font-weight: 500;
        }

        .phone {
            font-size: 28rpx;
            color: $text-color-light;
        }
    }

    .address-text {
        font-size: 26rpx;
        color: $text-color-light;
        line-height: 1.5;
    }
}

.form-item {
    margin-bottom: 30rpx;

    &:last-child {
        margin-bottom: 0;
    }

    .form-label {
        font-size: 28rpx;
        color: $text-color;
        font-weight: 500;
        margin-bottom: 16rpx;
    }

    .form-input,
    .form-textarea {
        background: #f8f9fa;
        border-radius: 12rpx;
        padding: 20rpx;
        border: 1rpx solid #e9ecef;
        transition: all 0.3s ease;

        &:focus-within {
            border-color: $theme-primary;
            background: white;
        }
    }
}

.tips-card {
    .tips-title {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;

        text {
            margin-left: 8rpx;
            font-size: 28rpx;
            font-weight: 500;
            color: #f0ad4e;
        }
    }

    .tips-content {
        text {
            display: block;
            font-size: 26rpx;
            color: $text-color-light;
            line-height: 1.6;
            margin-bottom: 8rpx;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

.action-buttons {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    padding: 20rpx 30rpx;
    border-top: 1rpx solid $border-color;
    box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.1);

    .btn-row {
        display: flex;
        gap: 20rpx;

        .action-btn {
            flex: 1;
            padding: 20rpx 0;
            border-radius: 24rpx;
            font-size: 30rpx;
            font-weight: 500;
            text-align: center;
            border: 1rpx solid;
            transition: all 0.3s ease;

            &.primary {
                background: $theme-primary;
                color: white;
                border-color: $theme-primary;

                &:active {
                    background: darken($theme-primary, 10%);
                }
            }

            &.secondary {
                background: white;
                color: $theme-primary;
                border-color: $theme-primary;

                &:active {
                    background: rgba(137, 102, 239, 0.1);
                }
            }
        }
    }
}
</style>
