<template>
    <u-popup v-model="show" mode="bottom" border-radius="14" :safe-area-inset-bottom="true" :mask-close-able="true" :z-index="1000">
        <view class="gift-chat-popup">
            <!-- 顶部区域 -->
            <view class="popup-header">
                <text class="popup-title">给对方送个小礼物，马上开聊</text>
                <view class="close-btn" @click="closePopup">
                    <text class="close-text">先不聊了</text>
                </view>
            </view>

            <!-- 礼物选项区域 -->
            <view class="gift-options">
                <view class="gift-option" :class="{ selected: selectedGift === 'member' }" @click="selectGift('member')">
                    <view class="gift-icon">
                        <image src="/static/images/hy.png" mode="widthFix"></image>
                    </view>
                    <view class="gift-info">
                        <text class="gift-name">会员免费聊</text>
                        <!-- <text class="gift-desc">开通会员即可畅聊</text> -->
                    </view>
                    <view class="selected-mark" v-if="selectedGift === 'member'">
                        <u-icon name="checkmark" color="#ffffff" size="24"></u-icon>
                    </view>
                </view>

                <view class="gift-option" :class="{ selected: selectedGift === 'flower10' }" @click="selectGift('flower10')">
                    <view class="gift-icon">
                        <image src="/static/images/hua.png" mode="widthFix"></image>
                        <text class="flower-count">x10</text>
                    </view>
                    <view class="gift-info">
                        <text class="gift-name">10朵鲜花</text>
                        <text class="gift-price">$10</text>
                    </view>
                    <view class="selected-mark" v-if="selectedGift === 'flower10'">
                        <u-icon name="checkmark" color="#ffffff" size="24"></u-icon>
                    </view>
                </view>

                <view class="gift-option" :class="{ selected: selectedGift === 'flower20' }" @click="selectGift('flower20')">
                    <view class="gift-icon">
                        <image src="/static/images/hua.png" mode="widthFix"></image>
                        <text class="flower-count">x20</text>
                    </view>
                    <view class="gift-info">
                        <text class="gift-name">20朵鲜花</text>
                        <text class="gift-price">$20</text>
                    </view>
                    <view class="selected-mark" v-if="selectedGift === 'flower20'">
                        <u-icon name="checkmark" color="#ffffff" size="24"></u-icon>
                    </view>
                </view>

                <view class="gift-option" :class="{ selected: selectedGift === 'flower30' }" @click="selectGift('flower30')">
                    <view class="gift-icon">
                        <image src="/static/images/hua.png" mode="widthFix"></image>
                        <text class="flower-count">x30</text>
                    </view>
                    <view class="gift-info">
                        <text class="gift-name">20朵鲜花</text>
                        <text class="gift-price">$30</text>
                    </view>
                    <view class="selected-mark" v-if="selectedGift === 'flower30'">
                        <u-icon name="checkmark" color="#ffffff" size="24"></u-icon>
                    </view>
                </view>
            </view>

            <!-- 底部按钮区域 -->
            <view class="action-buttons">
                <button class="action-button member-btn" @click="selectedGift === 'member' ? onClickMember() : onClickSend()">
                    <text class="button-text">{{ selectedGift == 'member' ? '开通会员' : sendButtonText }}</text>
                </button>
            </view>
        </view>
    </u-popup>
</template>

<script>
export default {
    name: 'gift-chat-popup',
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        // 作品ID，用于打赏
        workId: {
            type: [String, Number],
            default: null,
        },
        // 礼物商品ID配置
        giftGoodIds: {
            type: Object,
            default: () => ({
                flower5: 1,
                flower10: 2,
                flower20: 3,
            }),
        },
    },
    data() {
        return {
            show: false,
            selectedGift: 'member',
        };
    },
    computed: {
        sendButtonText() {
            switch (this.selectedGift) {
                case 'member':
                    return '立即开通';
                case 'flower10':
                    return '$10赠送';
                case 'flower20':
                    return '$20赠送';
                case 'flower30':
                    return '$30赠送';
                default:
                    return '$10赠送';
            }
        },
    },
    watch: {
        value: {
            handler(val) {
                this.show = val;
            },
            immediate: true,
        },
        show(val) {
            this.$emit('input', val);
        },
    },
    methods: {
        closePopup() {
            this.show = false;
            this.$emit('input', false);
            this.$emit('close');
        },
        selectGift(giftType) {
            this.selectedGift = giftType;
        },
        onClickMember() {
            // 开通会员按钮点击处理
            this.$emit('open-member');
            this.closePopup();
        },
        async onClickSend() {
            // 赠送礼物按钮点击处理
            const giftPrice = this.getGiftPrice();

            if (giftPrice === 0) {
                // 会员免费聊天
                this.$emit('open-member');
                this.closePopup();
                return;
            }

            // 检查余额是否足够
            const hasEnoughBalance = await this.checkBalance(giftPrice);

            if (!hasEnoughBalance) {
                // 余额不足，引导充值
                this.showRechargeConfirm(giftPrice);
                return;
            }

            // 余额足够，显示确认弹窗
            this.showGiftConfirm(giftPrice);
        },

        // 检查用户余额
        async checkBalance(requiredAmount) {
            try {
                const res = await this.$api.getUserWallet({
                    access_token: uni.getStorageSync('token'),
                });

                if (res.status === 0 && res.data && res.data.balance) {
                    const currentBalance = parseFloat(res.data.balance.balance || 0);
                    return currentBalance >= requiredAmount;
                }
                return false;
            } catch (error) {
                console.error('获取余额失败', error);
                return false;
            }
        },

        // 显示充值确认弹窗
        showRechargeConfirm(requiredAmount) {
            uni.showModal({
                title: '余额不足',
                content: `当前余额不足，需要¥${requiredAmount}，是否前往充值？`,
                confirmText: '去充值',
                cancelText: '取消',
                success: (res) => {
                    if (res.confirm) {
                        this.closePopup();
                        uni.navigateTo({
                            url: '/views/wallet/recharge'
                        });
                    }
                }
            });
        },

        // 显示礼物确认弹窗
        showGiftConfirm(giftPrice) {
            const giftName = this.getGiftName();
            uni.showModal({
                title: '确认打赏',
                content: `确定要赠送${giftName}吗？将消耗¥${giftPrice}`,
                confirmText: '确认赠送',
                cancelText: '取消',
                success: (res) => {
                    if (res.confirm) {
                        this.sendGift();
                    }
                }
            });
        },

        // 发送礼物
        async sendGift() {
            const giftInfo = {
                type: this.selectedGift,
                price: this.getGiftPrice(),
                good_id: this.getGiftGoodId(),
                num: this.getGiftNum()
            };

            uni.showLoading({
                title: '赠送中...'
            });

            try {
                // 调用打赏接口
                const res = await this.$api.rewardAuthor({
                    good_id: giftInfo.good_id,
                    num: giftInfo.num,
                    work_id: this.workId, // 需要从父组件传入
                    access_token: uni.getStorageSync('token')
                });

                uni.hideLoading();

                if (res.status === 0) {
                    uni.showToast({
                        title: '赠送成功',
                        icon: 'success'
                    });

                    this.$emit('send-gift-success', giftInfo);
                    this.closePopup();
                } else {
                    uni.showToast({
                        title: res.msg || '赠送失败',
                        icon: 'none'
                    });
                }
            } catch (error) {
                console.error('赠送失败', error);
                uni.hideLoading();
                uni.showToast({
                    title: '网络异常，请稍后重试',
                    icon: 'none'
                });
            }
        },
        getGiftPrice() {
            switch (this.selectedGift) {
                case 'member':
                    return 0; // 会员免费
                case 'flower10':
                    return 10;
                case 'flower20':
                    return 20;
                case 'flower30':
                    return 30;
                default:
                    return 10;
            }
        },

        // 获取礼物名称
        getGiftName() {
            switch (this.selectedGift) {
                case 'member':
                    return '会员免费聊';
                case 'flower10':
                    return '10朵鲜花';
                case 'flower20':
                    return '20朵鲜花';
                case 'flower30':
                    return '30朵鲜花';
                default:
                    return '10朵鲜花';
            }
        },

        // 获取礼物商品ID（需要根据实际商品配置）
        getGiftGoodId() {
            // 这里需要根据实际的商品ID配置
            // 可以从接口获取或者在组件初始化时传入
            switch (this.selectedGift) {
                case 'flower10':
                    return this.giftGoodIds?.flower10 || 1;
                case 'flower20':
                    return this.giftGoodIds?.flower20 || 2;
                case 'flower30':
                    return this.giftGoodIds?.flower30 || 3;
                default:
                    return this.giftGoodIds?.flower10 || 1;
            }
        },

        // 获取礼物数量
        getGiftNum() {
            switch (this.selectedGift) {
                case 'flower10':
                    return 10;
                case 'flower20':
                    return 20;
                case 'flower30':
                    return 30;
                default:
                    return 10;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
/* 定义变量 */
$primary-gradient: linear-gradient(135deg, #4a90e2, #5e62b0, #dc2e9d);
$pink-gradient: linear-gradient(135deg, #ff6b9b, #dc2e9d);
$gold-color: #ffd700;
$primary-blue: #4a90e2;
$primary-purple: #5e62b0;
$primary-pink: #dc2e9d;
$text-light: #ffffff;
$text-dark: #333333;
$text-gray: #888888;

.gift-chat-popup {
    position: relative;
    background-color: #fff;
    width: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 14rpx 14rpx 0 0;
    animation: slide-up 0.3s ease-out;
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
    padding-bottom: env(safe-area-inset-bottom);
}

@keyframes slide-up {
    from {
        transform: translateY(100%);
    }
    to {
        transform: translateY(0);
    }
}

.popup-header {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 30rpx;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.popup-title {
    font-size: 32rpx;
    font-weight: bold;
    color: $text-dark;
    flex: 1;
}

.close-btn {
    padding: 10rpx 20rpx;
    border-radius: 30rpx;
    background-color: rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;

    &:active {
        background-color: rgba(0, 0, 0, 0.1);
    }
}

.close-text {
    font-size: 26rpx;
    color: $text-gray;
}

.gift-options {
    padding: 20rpx 20rpx 30rpx;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10rpx;
}

.gift-option {
    position: relative;
    border-radius: 16rpx;
    padding: 24rpx 20rpx;
    background: linear-gradient(to bottom, rgba(74, 144, 226, 0.05), rgba(94, 98, 176, 0.02));
    border: 1rpx solid rgba(74, 144, 226, 0.1);
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: all 0.3s ease;

    &:active {
        transform: scale(0.98);
    }

    &.selected {
        background: linear-gradient(to bottom, rgba(74, 144, 226, 0.1), rgba(94, 98, 176, 0.05));
        border: 2rpx solid $primary-pink;
        box-shadow: 0 4rpx 12rpx rgba(220, 46, 157, 0.15);
    }
}

.gift-icon {
    position: relative;
    margin-bottom: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60rpx;
    image {
        width: 100%;
        height: 100%;
    }
}

.flower-count {
    position: absolute;
    bottom: -10rpx;
    right: -15rpx;
    font-size: 24rpx;
    font-weight: bold;
    color: $primary-pink;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 20rpx;
    padding: 2rpx 10rpx;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.gift-info {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.gift-name {
    font-size: 26rpx;
    font-weight: bold;
    color: $text-dark;
    margin-bottom: 4rpx;
}

.gift-desc {
    font-size: 22rpx;
    color: $text-gray;
}

.gift-price {
    font-size: 26rpx;
    font-weight: bold;
    color: $primary-pink;
}

.selected-mark {
    position: absolute;
    top: 10rpx;
    right: 10rpx;
    width: 36rpx;
    height: 36rpx;
    border-radius: 50%;
    background: $primary-pink;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2rpx 6rpx rgba(220, 46, 157, 0.3);
}

.action-buttons {
    padding: 20rpx 30rpx 30rpx;
    display: flex;
    gap: 20rpx;
}

.action-button {
    flex: 1;
    height: 90rpx;
    border: none;
    border-radius: 45rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 6rpx 12rpx rgba(0, 0, 0, 0.15);
    overflow: hidden;

    &:before {
        content: '';
        position: absolute;
        top: -10rpx;
        left: -10rpx;
        right: -10rpx;
        bottom: -10rpx;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transform: translateX(-100%);
        transition: transform 0s;
        pointer-events: none;
    }

    &:active {
        transform: translateY(3rpx);
        box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);

        &:before {
            transform: translateX(100%);
            transition: transform 0.6s ease;
        }
    }

    &::after {
        border: none;
    }
}

.member-btn {
    background: $primary-gradient;
    color: $text-light;
}

.gift-btn {
    background: $pink-gradient;
    color: $text-light;
}

.button-text {
    font-size: 32rpx;
    font-weight: bold;
    text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
}
</style>
