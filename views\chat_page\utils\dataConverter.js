/**
 * 将API返回的消息数据转换为组件所需的格式
 * @param {Object} message API返回的消息对象
 * @param {String} myid 当前用户ID
 * @return {Object} 转换后的消息对象
 */
export const convertMessageFromApi = (message, myid) => {
  // 默认为文本消息类型
  let type = 'text';
  switch (message.message_type) {
    case '1': type = 'text'; break;
    case '2': type = 'image'; break;
    case '3': type = 'video'; break;
    case '4': type = 'audio'; break;
    default: type = 'text';
  }

  // 构建消息对象
  const convertedMessage = {
    groupId: message.to_uid, // 在私聊场景下，groupId用来存储会话ID
    senderData: {
      name: message.user?.nickname || '用户',
      avatar: message.user?.headimgurl || message.user?.avatar || message.user?.avatarUrl ,
      member_id: message.uid
    },
    senderId: message.uid,
    messageId: message.id,
    timestamp: new Date(message.addtime.replace(/-/g, '/')).getTime(), // 转换时间字符串为时间戳
    type: type,
    payload: {
      text: message.content
    },
    recalled: false,
    status: 'success',
    isHide: 0
  };

  // 根据消息类型补充额外的payload信息
  if (type === 'image') {
    convertedMessage.payload = {
      contentType: 'image/jpg',
      name: 'image.jpg',
      size: message.file_size || 0,
      url: message.content,
      thumbnail: message.content
    };
  } else if (type === 'video') {
    convertedMessage.payload = {
      video: {
        name: 'video.mp4',
        url: message.content,
        contentType: 'video/mp4',
        size: message.file_size || 0,
        duration: message.duration || 0
      },
      thumbnail: {
        url: message.thumbnail || message.content
      }
    };
  } else if (type === 'audio') {
    convertedMessage.payload = {
      contentType: 'audio/mp3',
      name: 'audio.mp3',
      size: message.file_size || 0,
      url: message.content,
      duration: message.duration || 0
    };
    convertedMessage.pause = 4; // 初始化语音状态
  }

  return convertedMessage;
};

/**
 * 批量转换API返回的消息列表
 * @param {Array} messages API返回的消息列表
 * @param {String} myid 当前用户ID
 * @return {Array} 转换后的消息列表
 */
export const convertMessagesFromApi = (messages, myid) => {
  if (!Array.isArray(messages)) return [];
  return messages.map(msg => convertMessageFromApi(msg, myid));
};

/**
 * 构建要发送给API的消息对象
 * @param {Object} params 消息参数
 * @return {Object} 发送给API的消息对象
 */
export const buildApiMessageParams = (params) => {
  const { to_uid, content, type = 'text', file_size = 0, duration = 0 } = params;
  
  // 消息类型映射
  const messageTypeMap = {
    'text': '1',
    'image': '2',
    'video': '3',
    'audio': '4'
  };
  
  return {
    to_uid,
    msg: content,
    message_type: messageTypeMap[type] || '1',
    file_size,
    duration
  };
};

export default {
  convertMessageFromApi,
  convertMessagesFromApi,
  buildApiMessageParams
}; 