<template>
    <view class="comments-list-container">
        <view class="empty-state" v-if="commentsList.length === 0">
            <view class="empty-content">
                <image class="empty-image" src="https://static.wdaoyun.com/friend/2025-07-02/ef0fac060cb6f1abfde647fe1a55776d.png" mode="aspectFit"></image>
                <view class="empty-tip">暂无评论消息~</view>
            </view>
        </view>

        <view class="comment-item" v-for="(item, index) in commentsList" :key="index" @tap="viewContent(item)">
            <view class="avatar-wrapper">
                <u-avatar :src="getUserAvatar(item)" size="108"></u-avatar>
            </view>
            <view class="comment-content">
                <view class="top-info">
                    <text class="username">{{ getUserName(item) }}</text>
                </view>
                <view class="comment-description">
                    <text class="description">评论了你的{{ getCommentType(item) }} {{ formatCommentTime(item.addtime) }}</text>
                </view>
                <view class="comment-info">
                    {{ getCommentContent(item) }}
                </view>
            </view>
            <view class="action-area">
                <view class="work-image" v-if="getWorkImage(item)">
                    <image :src="getWorkImage(item)" mode="aspectFill" class="content-image"></image>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
export default {
    data() {
        return {
            page: 1,
            pagesize: 10,
            total: 0,
            commentsList: [],
            currentUid: '', // 当前用户ID
        };
    },
    computed: {
        ...mapState(['userInfo']),
    },
    onLoad() {
        // 获取当前用户ID
        this.currentUid = this.userInfo.uid || '';
        this.getCommentsList();
    },
    onPullDownRefresh() {
        this.page = 1;
        this.getCommentsList();
        uni.stopPullDownRefresh();
    },
    onReachBottom() {
        if (this.commentsList.length < this.total) {
            this.page++;
            this.getCommentsList();
        }
    },
    methods: {
        async getCommentsList() {
            try {
                const res = await this.$api.getCommentMsg({
                    page: this.page,
                    pagesize: this.pagesize,
                    access_token: uni.getStorageSync('token'),
                });

                if (res && res.data && res.data.list) {
                    if (this.page === 1) {
                        this.commentsList = res.data.list;
                    } else {
                        this.commentsList = [...this.commentsList, ...res.data.list];
                    }
                    this.total = parseInt(res.data.total || 0);
                }
            } catch (error) {
                console.error('获取评论列表失败', error);
                uni.showToast({
                    title: '获取评论列表失败',
                    icon: 'none',
                });
            }
        },
        // 获取用户头像
        getUserAvatar(item) {
            return item.to_user && item.to_user.headimgurl ? item.to_user.headimgurl : '/static/images/we.png';
        },
        // 获取用户名称
        getUserName(item) {
            return item.to_user && item.to_user.nickname ? item.to_user.nickname : '微信用户';
        },
        // 获取评论的内容类型
        getCommentType(item) {
            if (item.comment && item.comment.class_name) {
                const types = {
                    work: '作品',
                    dynamic: '动态',
                    article: '文章',
                };
                return types[item.comment.class_name] || '内容';
            }
            return '内容';
        },
        // 获取作品图片
        getWorkImage(item) {
            return item.comment && item.comment.work && item.comment.work.img ? item.comment.work.img : '';
        },
        // 获取评论内容
        getCommentContent(item) {
            return item.comment && item.comment.content ? (item.comment.content.length > 15 ? item.comment.content.substring(0, 15) + '...' : item.comment.content) : '';
        },
        // 格式化时间
        formatCommentTime(timestamp) {
            if (!timestamp) return '';

            // 处理时间戳或日期字符串
            let commentDate;
            if (typeof timestamp === 'string') {
                commentDate = new Date(timestamp.replace(/-/g, '/'));
            } else {
                commentDate = new Date(timestamp);
            }

            const now = new Date();
            const diff = now - commentDate;

            // 小于1分钟
            if (diff < 60 * 1000) {
                return '刚刚';
            }

            // 小于1小时
            if (diff < 60 * 60 * 1000) {
                return Math.floor(diff / (60 * 1000)) + '分钟前';
            }

            // 小于24小时
            if (diff < 24 * 60 * 60 * 1000) {
                return Math.floor(diff / (60 * 60 * 1000)) + '小时前';
            }

            // 小于7天
            if (diff < 7 * 24 * 60 * 60 * 1000) {
                return Math.floor(diff / (24 * 60 * 60 * 1000)) + '天前';
            }

            // 格式化为年月日
            const year = commentDate.getFullYear();
            const month = (commentDate.getMonth() + 1).toString().padStart(2, '0');
            const day = commentDate.getDate().toString().padStart(2, '0');

            return `${year}-${month}-${day}`;
        },
        // 查看被评论的内容
        viewContent(item) {
            if (!item.comment || !item.comment.class_name || !item.comment.class_id) {
                uni.showToast({
                    title: '内容信息不完整',
                    icon: 'none',
                });
                return;
            }

            const classType = item.comment.class_name;
            const contentId = item.comment.class_id;

            // 根据内容类型跳转到不同页面
            switch (classType) {
                case 'work':
                    uni.navigateTo({
                        url: `/views/moments/detail?id=${contentId}&type=work&showComment=1`,
                    });
                    break;
                case 'dynamic':
                    uni.navigateTo({
                        url: `/views/moments/detail?id=${contentId}&type=dynamic&showComment=1`,
                    });
                    break;
                case 'article':
                    uni.navigateTo({
                        url: `/views/article/detail?id=${contentId}&showComment=1`,
                    });
                    break;
                default:
                    uni.showToast({
                        title: '暂不支持查看此类内容',
                        icon: 'none',
                    });
            }
        },
        // 跳转到聊天页面
        gotoChat(item) {
            const chatTarget = item.user;

            if (!chatTarget) {
                uni.showToast({
                    title: '聊天对象信息不完整',
                    icon: 'none',
                });
                return;
            }

            // 导航到聊天详情页
            uni.navigateTo({
                url: `/views/chat_page/index?userId=${chatTarget.uid}&username=${encodeURIComponent(chatTarget.nickname)}&avatar=${encodeURIComponent(chatTarget.headimgurl)}`,
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.comments-list-container {
    min-height: 100vh;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);

    .no-data {
        height: 300rpx;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .comment-item {
        display: flex;
        padding: 24rpx;
        align-items: center;
        background-color: #ffffff;
        border-bottom: 1px solid #f2f2f2;

        .avatar-wrapper {
            position: relative;
            margin-right: 24rpx;
        }

        .comment-content {
            flex: 1;
            overflow: hidden;

            .top-info {
                display: flex;
                justify-content: space-between;
                margin-bottom: 12rpx;

                .username {
                    font-size: 32rpx;
                    color: #333333;
                    font-weight: 400;
                }
            }

            .comment-description {
                .description {
                    font-size: 26rpx;
                    color: #666666;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                }
            }
            .comment-info {
                margin-top: 10rpx;
                font-size: 28rpx;
                color: #333333;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
            }
        }

        .action-area {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            justify-content: center;

            .work-image {
                width: 120rpx;
                height: 120rpx;
                border-radius: 8rpx;
                overflow: hidden;
                margin-left: 16rpx;
                box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);

                .content-image {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
            }
        }
    }
}
.empty-state {
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    text-align: center;

    .empty-content {
        // margin: 100rpx 30rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .empty-image {
            width: 400rpx;
            height: 314rpx;
            margin-bottom: 40rpx;
        }

        .empty-text {
            color: #999;
            font-size: 32rpx;
            margin-bottom: 16rpx;
            text-align: center;
        }

        .empty-tip {
            color: #8966ef;
            font-size: 28rpx;
            text-align: center;
        }
    }
}
</style>
