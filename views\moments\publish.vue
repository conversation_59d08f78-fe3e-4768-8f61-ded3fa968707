<template>
    <view class="content">
        <!-- 顶部装饰 -->
        <view class="header-decoration"></view>

        <view class="form-container">
            <!-- 标题输入区域 -->
            <view class="input-section">
                <view class="section-header">
                    <view class="section-icon">
                        <u-icon name="edit-pen" size="30" color="#8966ef"></u-icon>
                    </view>
                    <text class="section-title">标题</text>
                </view>
                <view class="title-input-box">
                    <u-input v-model="title" placeholder="请输入标题..." maxlength="30" />
                    <view class="word-count">{{ title.length }}/30</view>
                </view>
            </view>

            <!-- 文字输入区域 -->
            <view class="input-section">
                <view class="section-header">
                    <view class="section-icon">
                        <u-icon name="chat" size="30" color="#8966ef"></u-icon>
                    </view>
                    <text class="section-title">内容</text>
                </view>
                <view class="textarea-box">
                    <u-input type="textarea" v-model="content" placeholder="分享这一刻的想法..." maxlength="500" auto-height />
                    <view class="word-count">{{ content.length }}/500</view>
                </view>
            </view>

            <!-- 图片选择区域 -->
            <view class="input-section">
                <view class="section-header">
                    <view class="section-icon">
                        <u-icon name="photo" size="30" color="#8966ef"></u-icon>
                    </view>
                    <text class="section-title">图片</text>
                    <text class="section-subtitle">最多可选择9张</text>
                </view>
                <view class="image-picker">
                    <shmily-drag-image number="9" imageWidth="200" ref="dragImage" :list.sync="imageList" :custom="true" @addImage="chooseImage"></shmily-drag-image>
                </view>
            </view>

            <!-- 分类选择 -->
            <view class="input-section">
                <view class="section-header">
                    <view class="section-icon">
                        <u-icon name="list" size="30" color="#8966ef"></u-icon>
                    </view>
                    <text class="section-title">分类</text>
                </view>
                <view class="category-picker" @click="showCategoryPicker">
                    <view class="category-content">
                        <text v-if="selectedCategory" class="category-text">{{ selectedCategory.name }}</text>
                        <text v-else class="category-placeholder">请选择分类</text>
                    </view>
                    <view class="arrow">
                        <u-icon name="arrow-right" size="16" color="#8966ef"></u-icon>
                    </view>
                </view>
            </view>

            <!-- 位置选择 -->
            <view class="input-section">
                <view class="section-header">
                    <view class="section-icon">
                        <u-icon name="map" size="30" color="#8966ef"></u-icon>
                    </view>
                    <text class="section-title">位置</text>
                </view>
                <view class="location-picker" @click="chooseLocation">
                    <view class="location-content">
                        <text v-if="location" class="location-text">{{ location }}</text>
                        <text v-else class="location-placeholder">请选择位置信息（必填）</text>
                    </view>
                    <view class="arrow">
                        <u-icon name="arrow-right" size="16" color="#8966ef"></u-icon>
                    </view>
                </view>
            </view>
        </view>

        <!-- 发布按钮 -->
        <view class="publish-btn-container">
            <view class="publish-btn" :class="{ disabled: !canPublish }" @click="publish">
                <text class="publish-text">{{ isPublishing ? '发布中...' : '发布动态' }}</text>
                <!-- <view v-if="!isPublishing" class="publish-icon">
                    <u-icon name="arrow-up" size="16" color="#ffffff"></u-icon>
                </view> -->
            </view>
        </view>

        <!-- 加载中 -->
        <u-loading-page :loading="isPublishing"></u-loading-page>

        <!-- 分类选择弹窗 -->
        <u-picker v-model="categoryPickerShow" mode="selector" :range="categoryList" range-key="name" @confirm="onCategoryConfirm" @cancel="categoryPickerShow = false"></u-picker>
    </view>
</template>

<script>
import shmilyDragImage from '@/components/shmily-drag-image/shmily-drag-image.vue';

export default {
    components: {
        shmilyDragImage,
    },
    data() {
        return {
            content: '',
            imageList: [],
            location: '',
            latitude: '',
            longitude: '',
            address:'',
            title: '',
            isPublishing: false,
            categoryList: [], // 分类列表
            selectedCategory: null, // 选中的分类
            categoryPickerShow: false, // 分类选择器显示状态
        };
    },
    computed: {
        canPublish() {
            return (this.content.trim().length > 0 || this.imageList.length > 0) &&
                   this.title.trim().length > 0 &&
                   this.selectedCategory &&
                   this.location.trim().length > 0 &&
                   this.latitude &&
                   this.longitude &&
                   this.address.trim().length > 0;
        },
    },
    async onLoad() {
        await this.getCategoryList();
    },
    methods: {
        // 获取分类列表
        async getCategoryList() {
            try {
                const res = await this.$api.getHomeCategory({
                    access_token: uni.getStorageSync('token'),
                });
                this.categoryList = res.data.work_type_list || [];
            } catch (error) {
                console.error('获取分类列表失败:', error);
                uni.showToast({
                    title: '获取分类失败',
                    icon: 'none',
                });
            }
        },

        // 显示分类选择器
        showCategoryPicker() {
            if (this.categoryList.length === 0) {
                uni.showToast({
                    title: '分类数据加载中，请稍后',
                    icon: 'none',
                });
                return;
            }
            this.categoryPickerShow = true;
        },

        // 分类选择确认
        onCategoryConfirm(e) {
            const index = e[0];
            this.selectedCategory = this.categoryList[index];
            console.log(this.selectedCategory);
            this.categoryPickerShow = false;
        },
        // 选择图片
        async chooseImage() {
            uni.chooseImage({
                count: 9 - this.imageList.length,
                sizeType: ['compressed'],
                sourceType: ['album', 'camera'],
                success: async res => {
                    let num = res.tempFilePaths.length;
                    for (let i = 0; i < num; i++) {
                        await this.uploadFile(res.tempFilePaths[i], 3);
                    }
                },
            });
        },

        // 删除图片
        deleteImage(index) {
            this.imageList.splice(index, 1);
        },

        async uploadFile(path, type) {
            let uploadFileUrl = 'https://friend.wdaoyun.cn/upload/uploadfile.php';
            uni.showLoading({
                title: '上传中',
                // mask: true
            });
            uni.uploadFile({
                url: uploadFileUrl,
                filePath: path,
                name: 'upload',
                header: {
                    'Content-Type': 'multipart/form-data',
                    Authorization: '67947632acd5ac684574488b140b40400c74b695',
                },
                formData: {
                    access_token: uni.getStorageSync('token'),
                },
                success: res => {
                    if (res.statusCode == 200) {
                        this.$nextTick(() => {
                            this.$refs.dragImage.addImage(JSON.parse(res.data).data.img_url);
                        });
                        uni.hideLoading();
                    }
                },
                complete(res) {
                    uni.hideLoading();
                },
            });
        },

        // 选择位置
        chooseLocation() {
            uni.chooseLocation({
                success: res => {
                    console.log(res);
                    this.location = res.name;
                    this.longitude = res.longitude;
                    this.latitude = res.latitude;
                    this.address = res.address
                },
            });
        },

        // 发布动态
        async publish() {
            if (!this.canPublish) {
                return;
            }

            // 额外验证位置信息
            if (!this.location || !this.latitude || !this.longitude || !this.address) {
                uni.showToast({
                    title: '请选择位置信息',
                    icon: 'none',
                });
                return;
            }

            this.isPublishing = true;

            try {
                await this.$api.addWork({
                    access_token: uni.getStorageSync('token'),
                    title: this.title,
                    img: this.imageList[0],
                    files: JSON.stringify(this.imageList),
                    latitude: this.latitude,
                    longitude: this.longitude,
                    content: this.content,
                    work_type_id: this.selectedCategory.id, // 添加分类ID
                    address:this.address,
                });

                uni.showToast({
                    title: '发布成功',
                    icon: 'success',
                });
                uni.$emit('refreshPublish');
                setTimeout(() => {
                    uni.navigateBack();
                }, 1000);
            } catch (e) {
                console.error('发布失败', e);
                uni.showToast({
                    title: '发布失败，请重试',
                    icon: 'none',
                });
            } finally {
                this.isPublishing = false;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.content {
    min-height: 100vh;
   background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);
    padding-bottom: 140rpx;
    position: relative;
}

.header-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 120rpx;
    background: linear-gradient(135deg, #8966ef 0%, #a478f0 100%);
    border-radius: 0 0 40rpx 40rpx;
    z-index: 0;
}

.form-container {
    background-color: transparent;
    padding: 80rpx 30rpx 30rpx;
    position: relative;
    z-index: 1;
}

.input-section {
    background: #ffffff;
    border-radius: 20rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 8rpx 30rpx rgba(137, 102, 239, 0.08);
    border: 1rpx solid rgba(137, 102, 239, 0.1);
    transition: all 0.3s ease;

    &:hover {
        box-shadow: 0 12rpx 40rpx rgba(137, 102, 239, 0.12);
        transform: translateY(-2rpx);
    }
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .section-icon {
        width: 40rpx;
        height: 40rpx;
        background: linear-gradient(135deg, rgba(137, 102, 239, 0.1) 0%, rgba(240, 204, 108, 0.1) 100%);
        border-radius: 10rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15rpx;
    }

    .section-title {
        font-size: 30rpx;
        font-weight: 600;
        color: #333;
        flex: 1;
    }

    .section-subtitle {
        font-size: 24rpx;
        color: #999;
    }
}

.required-mark {
    color: #ff4757;
    font-weight: bold;
    margin-left: 5rpx;
}

/* 标题输入区域样式 */
.title-input-box {
    position: relative;

    input {
        width: 100%;
        height: 80rpx;
        font-size: 32rpx;
        color: #333;
        font-weight: 500;
        padding: 20rpx;
        background: #f8f9ff;
        border-radius: 12rpx;
        border: 2rpx solid transparent;
        transition: all 0.3s ease;

        &:focus {
            border-color: #8966ef;
            background: #ffffff;
            box-shadow: 0 0 0 6rpx rgba(137, 102, 239, 0.1);
        }
    }

    .word-count {
        position: absolute;
        bottom: -30rpx;
        right: 0;
        font-size: 24rpx;
        color: #999;
        background: #ffffff;
        padding: 5rpx 10rpx;
        border-radius: 10rpx;
    }
}

.textarea-box {
    position: relative;

    textarea {
        width: 100%;
        min-height: 200rpx;
        font-size: 28rpx;
        color: #333;
        line-height: 1.6;
        padding: 20rpx;
        background: #f8f9ff;
        border-radius: 12rpx;
        border: 2rpx solid transparent;
        transition: all 0.3s ease;

        &:focus {
            border-color: #8966ef;
            background: #ffffff;
            box-shadow: 0 0 0 6rpx rgba(137, 102, 239, 0.1);
        }
    }

    .word-count {
        position: absolute;
        bottom: -30rpx;
        right: 0;
        font-size: 24rpx;
        color: #999;
        background: #ffffff;
        padding: 5rpx 10rpx;
        border-radius: 10rpx;
    }
}

.image-picker {
    .image-list {
        display: flex;
        flex-wrap: wrap;

        .image-item {
            position: relative;
            width: 220rpx;
            height: 220rpx;
            margin-right: 15rpx;
            margin-bottom: 15rpx;
            border-radius: 16rpx;
            overflow: hidden;
            box-shadow: 0 4rpx 20rpx rgba(137, 102, 239, 0.15);

            &:nth-child(3n) {
                margin-right: 0;
            }

            image {
                width: 100%;
                height: 100%;
                transition: transform 0.3s ease;
            }

            &:hover image {
                transform: scale(1.05);
            }

            .delete-btn {
                position: absolute;
                top: -8rpx;
                right: -8rpx;
                width: 36rpx;
                height: 36rpx;
                background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
            }
        }

        .add-image {
            width: 220rpx;
            height: 220rpx;
            background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
            border: 2rpx dashed #8966ef;
            border-radius: 16rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15rpx;
            margin-bottom: 15rpx;
            transition: all 0.3s ease;

            &:nth-child(3n) {
                margin-right: 0;
            }

            &:hover {
                background: linear-gradient(135deg, #8966ef 0%, #a478f0 100%);
                border-color: transparent;
                transform: translateY(-4rpx);
                box-shadow: 0 8rpx 25rpx rgba(137, 102, 239, 0.3);
            }
        }
    }
}

.category-picker,
.location-picker {
    display: flex;
    align-items: center;
    padding: 25rpx;
    background: #f8f9ff;
    border-radius: 12rpx;
    border: 2rpx solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
        border-color: #8966ef;
        background: #ffffff;
        box-shadow: 0 0 0 6rpx rgba(137, 102, 239, 0.1);
    }

    .category-content,
    .location-content {
        flex: 1;

        .category-text,
        .location-text {
            font-size: 28rpx;
            color: #333;
            font-weight: 500;
        }

        .category-placeholder,
        .location-placeholder {
            font-size: 28rpx;
            color: #999;
        }
    }

    .arrow {
        margin-left: 15rpx;
        transition: transform 0.3s ease;
    }

    &:hover .arrow {
        transform: translateX(5rpx);
    }
}

.publish-btn-container {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 25rpx 30rpx;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.95) 20%, #ffffff 100%);
    backdrop-filter: blur(20rpx);
    z-index: 100;

    .publish-btn {
        width: 100%;
        height: 90rpx;
        background: linear-gradient(135deg, #8966ef 0%, #a478f0 100%);
        border-radius: 45rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8rpx 30rpx rgba(137, 102, 239, 0.4);
        transition: all 0.3s ease;
        cursor: pointer;

        &:hover {
            transform: translateY(-2rpx);
            box-shadow: 0 12rpx 40rpx rgba(137, 102, 239, 0.5);
        }

        &:active {
            transform: translateY(0);
        }

        &.disabled {
            background: linear-gradient(135deg, #e0e0e0 0%, #d0d0d0 100%);
            box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
            cursor: not-allowed;

            &:hover {
                transform: none;
                box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
            }

            .publish-text {
                color: #999 !important;
            }
        }

        .publish-text {
            font-size: 32rpx;
            font-weight: 600;
            color: #ffffff;
            margin-right: 10rpx;
        }

        .publish-icon {
            width: 32rpx;
            height: 32rpx;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}
</style>
