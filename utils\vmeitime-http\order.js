const http = uni.$u.http;
/**
 * 获取产品列表
 * @param {Object} d 请求参数
 * @param {Number} d.type 类型必填 1金币 2会员费
 * @param {access_token} d.access_token 用户token
*/
export const getOrderGoodList = d => http.post('?c=index&a=get_good_list', d);

/**
 * 获取订单列表
 * @param {Object} d 请求参数
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
 * @param {Number} d.class_name 类型 非必穿 goods 产品 其他暂定
 * @param {access_token} d.access_token 用户token
*/
export const getOrderList = d => http.post('?c=Order&a=get_order_list', d);
/**
 * 充值余额
 * @param {Object} d 请求参数
 * @param {Number} d.good_id 产品id
 * @param {Number} d.num 数量
 * @param {access_token} d.access_token 用户token
*/
export const createBalanceOrder = d => http.post('?c=Order&a=create_balance_order', d);
/**
 * 开通会员
 * @param {Object} d 请求参数
 * @param {Number} d.good_id 产品id
 * @param {Number} d.num 数量
 * @param {access_token} d.access_token 用户token
*/
export const createMemberOrder = d => http.post('?c=Order&a=create_member_order', d);

/**
 * 小程序支付
 * @param {Number} d.openid 当前账号的openid
 * @param {Number} d.orderid 订单号
 * @param {access_token} d.access_token 用户token
*/
export const createMiniPay = d => http.post('?c=Wxpay&a=listPayApplet', d);

/**
 * 打赏作者
 * @param {Object} d 请求参数
 * @param {Number} d.good_id 礼物商品ID（对应礼物类型）
 * @param {Number} d.num 打赏数量（礼物数量）
 * @param {Number} d.work_id 作品id
 * @param {String} d.access_token 用户token
 * @returns {Promise} 返回打赏结果
*/
export const rewardAuthor = d => http.post('?c=Order&a=create_balance_work_order', d);

/**
 * 创建提现订单
 * @param {Object} d 请求参数
 * @param {Number} d.amount 提现金额
 * @param {String} d.access_token 用户token
 * @returns {Promise} 返回提现结果
*/
export const createWithdrawOrder = d => http.post('?c=Order&a=create_reward_order', d);

/**
 * 提现申请
 * @param {Object} d 请求参数
 * @param {Number} d.orderid 提现订单号
 * @param {Number} d.openid 用户openid
 * @param {String} d.access_token 用户token
 * @returns {Promise} 返回提现结果
*/
export const withdrawApply = d => http.post('?c=Wxpay&a=transfers_pay', d);

/**
 * 查询支付状态
 * @param {Object} d 请求参数
 * @param {String} d.access_token 用户token
 * @param {Number} d.orderid 提现订单号
 */
export const checkPayStatus = d => http.post('?c=Wxpay&a=query_order_no_pay_v3', d);

/**
 * 获取提现记录
 * @param {Object} d 请求参数
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
 * @param {String} d.access_token 用户token
 * @returns {Promise} 返回提现记录列表
*/
export const getWithdrawRecord = d => http.post('?c=Order&a=get_withdraw_record', d);

/**
 * 撤销提现
 * @param {String} d.access_token 用户token
 * @param {Number} d.orderid 提现订单号
 */
export const cancelWithdraw = d => http.post('?c=Order&a=transfer_for_order_no_cancel_v3', d);

/**
 * 获取交易统计数据
 * @param {Object} d 请求参数
 * @param {String} d.access_token 用户token
 * @returns {Promise} 返回交易统计数据
 */
export const getTradeStats = d => http.post('?c=Order&a=get_trade_stats', d);

/**
 * 获取交易列表
 * @param {Object} d 请求参数
 * @param {Number} d.type 类型 1:我发布的 2:我卖出的 3:我买到的
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
 * @param {String} d.access_token 用户token
 * @returns {Promise} 返回交易列表数据
 */
export const getTradeList = d => http.post('?c=Order&a=get_trade_list', d);
export default {
    getOrderGoodList,
    getOrderList,
    createBalanceOrder,
    createMemberOrder,
    createMiniPay,
    rewardAuthor,
    withdrawApply,
    getWithdrawRecord,
    createWithdrawOrder,
    checkPayStatus,
    withdrawApply,
    cancelWithdraw,
    getTradeStats,
    getTradeList
};
