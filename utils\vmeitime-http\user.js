const http = uni.$u.http;
// 根据code获取open_id
export const getOpenId = d => http.post('?c=user&a=updateUserOpenIdLb', d);
export const codeLogin = d => http.post('?c=user&a=codeLogin', d);
/**
 * 手机号登录
 * @param {Object} d 请求参数
 * @param {String} d.access_token 用户token
 * @param {Number} d.login_type 1 手机号
 * @param {String} d.login_name 登录账号
 * @param {String} d.password 密码
*/
export const mobileLogin = d => http.post('?c=user&a=account_password_login', d);

export const login = d => http.post('?c=user&a=setuser', d);
/**
 * 发送验证码
 * @param {Object} d 请求参数
 * @param {String} d.access_token 用户token
 * @param {Number} d.mobile 手机号
 */
export const sendCode = d => http.post('?a=sendCode&c=User', d);
/**
 * 更新/绑定手机号
 * @param {Object} d 请求参数   
 * @param {String} d.access_token 用户token
 * @param {Number} d.mobile 手机号
 * @param {String} d.code 验证码
 * @param {String} d.code_id 类型 验证码Id
 */
export const updatePhone = d => http.post('?c=user&a=up_phone', d);
/**
 * 设置密码
 * @param {String} d.access_token 用户token
 * @param {Number} d.password 密码
 * @param {String} d.old_password 旧密码没有为空
*/
export const setPassword = d => http.post('?c=user&a=set_password', d);

// 手机号注册登录
export const register = d => http.post('?c=User&a=register_login_wx', d);
// 获取用户手机
export const getphone = d => http.post('?c=user&a=getPhoneLb', d);
// 获取用户信息
export const getUserInfo = d => http.post('?c=user&a=getuser', d);
// 修改头像昵称电话
export const updateUser = d => http.post('?c=user&a=up_user_info', d);

// 获取用户设备
export const getUserDevice = d => http.post('?c=user&a=getUserJiatingDevice', d);
// 获取地区列表
export const getRegionList = d => http.post('?c=Region&a=get_region_list', d);
/**
 * 获取用户金币余额
 * @param {Object} d 请求参数
 * @param {String} d.access_token 用户token
 */
export const getUserBalance = d => http.post('?c=user&a=balance', d);
/**
 * 获取用户作品列表
 * @param {Object} d 请求参数
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
 * @param {String} d.access_token 用户token
 * @param {String} d.uid 用户id,不传就是当前用户
 */
export const getUserWorkList = d => http.post('?c=user&a=my_work', d);

/**
 * 发布作品
 * @param {Object} d 请求参数
 * @param {String} d.title 作品标题
 * @param {String} d.content 作品内容
 * @param {String} d.access_token 用户token
 * @param {String} d.img 图片列表
 * @param {Array} d.files 文件列表
 * @param {String} d.latitude 纬度
 * @param {String} d.longitude 经度
 * @param {Number} d.work_type_id 从分类那里获取的
 */
export const addWork = d => http.post('?c=user&a=add_work', d);
/**
 * 获取用户首页数据
 * @param {Object} d 请求参数
 * @param {String} d.access_token 用户token
 */
export const getUserIndex = d => http.post('?c=user&a=my_index', d);

/**
 * 创建标签
 * @param {Object} d 请求参数
 * @param {String} d.access_token 用户token
 * @param {String} d.name 标签名称
 */
export const createTag = d => http.post('?c=user&a=create_tag_name', d);

/**
 * 获取用户余额
 * @param {Object} d 请求参数
 * @param {String} d.access_token 用户token
*/
export const getUserWallet = d => http.post('?c=user&a=my_wallet', d);

/**
 * 获取用户余额记录
 * @param {Object} d 请求参数
 * @param {String} d.access_token 用户token
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
 * @param {Number} d.type 1余额 2打赏
*/
export const getUserWalletList = d => http.post('?c=user&a=my_wallet_balance_list', d);

/**
 * 获取用户地址
 * @param {Object} d 请求参数
 * @param {String} d.access_token 用户token
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
*/
export const getUserAddressList = d => http.post('?c=user&a=get_address_list', d);

/**
 * 创建地址
 * @param {Object} d 请求参数
 * @param {String} d.access_token 用户token
 * @param {String} d.name 收货人姓名
 * @param {String} d.phone 收货人手机号
 * @param {String} d.province_code 省份
 * @param {String} d.city_code 城市
 * @param {String} d.area_code 区域
 * @param {String} d.address 详细地址
 * @param {String} d.is_def 是否默认地址 1是 0否
*/
export const createAddress = d => http.post('?c=user&a=add_user_address', d);

/**
 * 编辑地址
 * @param {Object} d 请求参数
 * @param {String} d.access_token 用户token
 * @param {String} d.name 收货人姓名
 * @param {String} d.phone 收货人手机号
 * @param {String} d.province_code 省份
 * @param {String} d.city_code 城市
 * @param {String} d.area_code 区域
 * @param {String} d.address 详细地址
 * @param {String} d.is_def 是否默认地址 1是 0否
 * @param {String} d.address_id 地址id
*/
export const editAddress = d => http.post('?c=user&a=up_user_address', d);

/**
 * 删除地址
 * @param {Object} d 请求参数
 * @param {String} d.access_token 用户token
 * @param {String} d.address_id 地址id
*/
export const delAddress = d => http.post('?c=user&a=del_user_address', d);
/**
 * 获取首页分类
 * @param {Object} d 请求参数
 * @param {String} d.access_token 用户token
*/
export const getHomeCategory = d => http.post('?c=user&a=get_work_type_all', d);

/**
 * 获取他人用户信息
 * @param {Object} d 请求参数
 * @param {String} d.access_token 用户token
 * @param {Number} d.to_user_id 用户id
*/
export const getOtherUserInfo = d => http.post('?c=user&a=get_to_user_info', d);

export default {
    getOpenId,
    codeLogin,
    login,
    sendCode,
    register,
    getphone,
    getUserInfo,
    updateUser,

    getUserDevice,
    getRegionList,
    getUserWorkList,
    addWork,
    getUserIndex,
    createTag,
    getUserBalance,
    updatePhone,
    getUserWallet,
    getUserWalletList,
    mobileLogin,
    setPassword,
    getUserAddressList,
    createAddress,
    editAddress,
    delAddress,
    getHomeCategory,
    getOtherUserInfo
};
