<template>
    <view class="container">
        <!-- VIP会员卡片 - 参考爱奇艺设计 -->
        <view class="vip-member-card">
            <!-- 背景装饰 -->
            <view class="card-bg-decoration">
                <view class="decoration-circle decoration-1"></view>
                <view class="decoration-circle decoration-2"></view>
                <view class="decoration-circle decoration-3"></view>
            </view>

            <!-- 用户信息区域 -->
            <view class="user-section">
                <view class="avatar-container">
                    <image :src="userInfo.headimgurl || defaultAvatar" mode="aspectFill" class="user-avatar"></image>
                    <!-- VIP皇冠图标 -->
                    <view class="vip-crown" v-if="memberStatus.isValid">
                        <text class="crown-icon">👑</text>
                    </view>
                </view>
                <view class="user-details">
                    <view class="username">{{ userInfo.nickname || '微信用户' }}</view>
                    <!-- 会员状态显示 -->
                    <view class="member-status-display">
                        <view v-if="memberStatus.isValid" class="member-active">
                            <view class="status-badge" :style="{ backgroundColor: memberStatus.statusColor }">
                                <text class="status-text">{{ memberStatus.statusText }}</text>
                            </view>
                            <view class="expire-info">
                                <text class="expire-label">有效期至</text>
                                <text class="expire-date">{{ memberStatus.expireDate }}</text>
                            </view>
                            <view class="remaining-days" v-if="memberStatus.remainingDays > 0">
                                <text>剩余{{ memberStatus.remainingDays }}天</text>
                            </view>
                        </view>
                        <view v-else class="member-inactive">
                            <view class="status-badge inactive">
                                <text class="status-text">普通用户</text>
                            </view>
                            <view class="upgrade-hint">
                                <text>开通会员享受专属特权</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 会员特权预览 -->
            <view class="privileges-preview" v-if="!memberStatus.isValid">
                <view class="privilege-item" v-for="(privilege, index) in previewPrivileges" :key="index">
                    <text class="privilege-icon">{{ privilege.icon }}</text>
                    <text class="privilege-text">{{ privilege.text }}</text>
                </view>
            </view>
        </view>

        <view class="section-title">
            <text>会员套餐</text>
            <text class="subtitle">选择最适合您的套餐</text>
        </view>

        <view class="plans">
            <view v-for="(plan, index) in goodList" :key="index" :class="['plan-card', plan.isPopular ? 'popular' : '']" @click="selectPlan(index)">
                <view class="selected-icon" v-if="selectedPlanIndex === index"></view>
                <text v-if="plan.isPopular" class="popular-tag">热门推荐</text>
                <view class="plan-name">{{ plan.name }}</view>
                <view class="price">¥{{ plan.price }}</view>
                <view class="original-price">原价: ¥{{ plan.original_price || plan.price }}</view>
                <view class="discount" v-if="plan.original_price">立省 ¥{{ Number(plan.original_price - plan.price).toFixed(2) }}</view>
                <view class="plan-desc">{{ plan.desc }}</view>
                <button class="subscribe-btn" @click.stop="handleSubscribe(plan)">立即开通</button>
            </view>
        </view>

        <view class="section-title">会员特权</view>

        <view class="benefits">
            <view class="benefit-grid">
                <view v-for="(benefit, index) in benefits" :key="index" class="benefit-item">
                    <view class="benefit-icon iconfont">{{ benefit.icon }}</view>
                    <view class="benefit-content">
                        <view class="benefit-title">{{ benefit.title }}</view>
                        <view class="benefit-desc">{{ benefit.description }}</view>
                    </view>
                </view>
            </view>
        </view>

        <view class="footer">
            <view class="agreement">开通会员即代表您同意我们的《会员服务协议》和《隐私政策》</view>
            <view class="terms">
                <text @click="openTerms('agreement')">会员协议</text>
                <text @click="openTerms('privacy')">隐私政策</text>
                <text @click="openTerms('refund')">退款说明</text>
            </view>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
import { getOrderGoodList, createMemberOrder } from '@/utils/vmeitime-http/order.js';
import { getUserInfo } from '@/utils/vmeitime-http/user.js';
import { getMemberStatus } from '@/utils/memberUtils.js';

export default {
    data() {
        return {
            defaultAvatar: 'http://static.wdaoyun.com/wdy/source/2023-10-18/132.jpg',
            selectedPlanIndex: 0,
            loading: false,
            goodList: [],
            // 会员特权预览（非会员时显示）
            previewPrivileges: [
                { icon: '🚀', text: '加好友无限制' },
                { icon: '💬', text: '加微信无限制' },
                { icon: '🔥', text: '推荐频道展示' },
                { icon: '👁️', text: '访客记录查看' },
            ],
            defaultPlans: [
                {
                    name: '月卡会员',
                    price: 29,
                    original_price: 39,
                    desc: '适合短期体验会员特权的用户，随时开通随时使用',
                    isPopular: false,
                },
                {
                    name: '季卡会员',
                    price: 79,
                    original_price: 117,
                    desc: '最具性价比的选择，平均每月仅需¥26.3',
                    isPopular: true,
                },
                {
                    name: '年卡会员',
                    price: 258,
                    original_price: 468,
                    desc: '最实惠的长期选择，平均每月仅需¥21.5',
                    isPopular: false,
                },
            ],
            benefits: [
                {
                    icon: '\ue60c',
                    title: '加好友无限制',
                    description: '解除好友添加限制，自由扩展社交圈',
                },
                {
                    icon: '\ue856',
                    title: '加微信无限制',
                    description: '自由交换联系方式，建立更深联系',
                },
                // {
                //     icon: 'icon-fire',
                //     title: '推荐频道展示',
                //     description: '每月3次，每次24小时，增加曝光机会',
                // },
                // {
                //     icon: 'icon-eye',
                //     title: '访客记录查看',
                //     description: '查看谁访问过你的主页，不错过任何机会',
                // },
                {
                    icon: '\ue612',
                    title: '专属会员勋章',
                    description: '尊贵身份标识，展示您的会员身份',
                },
                {
                    icon: '\uec2e',
                    title: '专属客服通道',
                    description: '优先处理会员问题，享受VIP服务',
                },
            ],
        };
    },
    computed: {
        ...mapState(['userInfo', 'hasLogin']),
        token() {
            return uni.getStorageSync('token') || '';
        },
        // 计算会员状态
        memberStatus() {
            return getMemberStatus(this.userInfo);
        },
    },
    onLoad() {
        this.getOrderGoodList();
        this.refreshUserInfo();
    },
    methods: {
        // 刷新用户信息
        async refreshUserInfo() {
            if (!this.hasLogin || !this.token) return;

            try {
                const res = await getUserInfo({
                    access_token: this.token,
                });

                if (res.data) {
                    // 更新store中的用户信息
                    this.$store.commit('updateUserInfo', res.data);
                }
            } catch (error) {
                console.error('获取用户信息失败:', error);
            }
        },

        // 获取会员套餐列表
        async getOrderGoodList() {
            if (this.loading) return;
            this.loading = true;

            try {
                const res = await getOrderGoodList({
                    type: 2, // 会员费类型
                    access_token: this.token,
                });

                if (res.data && res.data.list && res.data.list.length > 0) {
                    // 处理数据，按照API实际返回的格式处理
                    this.goodList = res.data.list.map((item, index) => {
                        // 判断是否为热门套餐，这里将年费套餐设为热门
                        const isPopular = item.type_value === 'year';

                        // 设置原价为当前价格的1.2倍以展示折扣效果
                        const original_price = parseFloat((parseFloat(item.amount) * 1.2).toFixed(2));

                        return {
                            id: item.id,
                            name: item.title,
                            price: parseFloat(item.amount),
                            original_price: original_price,
                            desc: item.content || `${item.title}，尊享VIP特权`,
                            isPopular: isPopular,
                            type_value: item.type_value,
                        };
                    });

                    // 排序：月费在前，年费在后
                    this.goodList.sort((a, b) => {
                        if (a.type_value === 'month' && b.type_value === 'year') return -1;
                        if (a.type_value === 'year' && b.type_value === 'month') return 1;
                        return 0;
                    });
                } else {
                    // 如果接口失败或无数据，使用默认数据
                    this.goodList = this.defaultPlans;
                }
            } catch (error) {
                console.error('获取会员套餐失败:', error);
                this.goodList = this.defaultPlans;
            } finally {
                this.loading = false;
            }
        },

        selectPlan(index) {
            this.selectedPlanIndex = index;
        },

        async handleSubscribe(plan) {
            if (!this.hasLogin) {
                uni.navigateTo({
                    url: '/pages/login/login',
                });
                return;
            }

            try {
                uni.showLoading({
                    title: '处理中...',
                });

                const res = await createMemberOrder({
                    good_id: plan.id,
                    num: 1,
                    access_token: uni.getStorageSync('token'),
                });
                if (res.status !== 0) {
                    throw new Error(res.msg || '创建订单失败');
                }

                // 获取用户openid（小程序支付需要）
                const openid = this.userInfo.openid;
                if (!openid) {
                    throw new Error('获取用户信息失败，请重新登录');
                }

                // 调用支付接口
                const payRes = await this.$api.createMiniPay({
                    openid: openid,
                    orderid: res.data.order_no,
                    access_token: uni.getStorageSync('token'),
                });

                if (payRes.status !== 0) {
                    throw new Error(payRes.msg || '支付失败');
                }

                // 调用微信支付
                await this.requestPayment(payRes.data);

                uni.hideLoading();
                uni.showToast({
                    title: '开通成功',
                    icon: 'success',
                });

                // 更新用户信息
                await this.VAgetUser();

                setTimeout(() => {
                    uni.navigateBack();
                }, 1500);
            } catch (error) {
                console.error('支付失败:', error);
                uni.hideLoading();
                uni.showToast({
                    title: '网络异常，请重试',
                    icon: 'none',
                });
            }
        },

        // 调用微信支付
        async requestPayment(paymentData) {
            return new Promise((resolve, reject) => {
                uni.requestPayment({
                    provider: 'wxpay',
                    timeStamp: paymentData.timeStamp,
                    nonceStr: paymentData.nonceStr,
                    package: paymentData.package,
                    signType: paymentData.signType,
                    paySign: paymentData.paySign,
                    success: res => {
                        console.log('支付成功:', res);
                        resolve(res);
                    },
                    fail: err => {
                        console.error('支付失败:', err);
                        reject(new Error('支付失败'));
                    },
                });
            });
        },
        openTerms(type) {
            const urls = {
                agreement: '/pages/terms/agreement',
                privacy: '/pages/terms/privacy',
                refund: '/pages/terms/refund',
            };

            uni.navigateTo({
                url: urls[type],
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.container {
    min-height: 100vh;
    background: #f5f7fa;
    padding: 30rpx;

    .header {
        text-align: center;
        padding: 40rpx 0;

        .logo {
            font-size: 40rpx;
            font-weight: bold;
            background: linear-gradient(90deg, #8966ef, #f0cc6c);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 20rpx;
        }

        .subtitle {
            font-size: 28rpx;
            color: #666;
        }
    }

    /* VIP会员卡片 - 参考爱奇艺设计 */
    .vip-member-card {
        background: linear-gradient(135deg, #8966ef 0%, #a584f2 50%, #f0cc6c 100%);
        border-radius: 24rpx;
        margin: 30rpx;
        padding: 40rpx;
        position: relative;
        overflow: hidden;
        box-shadow: 0 8rpx 32rpx rgba(137, 102, 239, 0.3);

        /* 背景装饰 */
        .card-bg-decoration {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;

            .decoration-circle {
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.1);

                &.decoration-1 {
                    width: 200rpx;
                    height: 200rpx;
                    top: -100rpx;
                    right: -50rpx;
                }

                &.decoration-2 {
                    width: 120rpx;
                    height: 120rpx;
                    bottom: -60rpx;
                    left: -30rpx;
                    background: rgba(240, 204, 108, 0.2);
                }

                &.decoration-3 {
                    width: 80rpx;
                    height: 80rpx;
                    top: 50%;
                    right: 20rpx;
                    background: rgba(255, 255, 255, 0.15);
                }
            }
        }

        /* 用户信息区域 */
        .user-section {
            display: flex;
            align-items: flex-start;
            position: relative;
            z-index: 2;

            .avatar-container {
                position: relative;
                margin-right: 30rpx;

                .user-avatar {
                    width: 120rpx;
                    height: 120rpx;
                    border-radius: 60rpx;
                    border: 4rpx solid rgba(255, 255, 255, 0.3);
                }

                .vip-crown {
                    position: absolute;
                    top: -10rpx;
                    right: -10rpx;
                    width: 40rpx;
                    height: 40rpx;
                    background: linear-gradient(45deg, #f0cc6c, #edc353);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-shadow: 0 2rpx 8rpx rgba(240, 204, 108, 0.5);

                    .crown-icon {
                        font-size: 24rpx;
                    }
                }
            }

            .user-details {
                flex: 1;

                .username {
                    font-size: 36rpx;
                    color: #fff;
                    font-weight: 600;
                    margin-bottom: 20rpx;
                    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
                }

                .member-status-display {
                    .member-active {
                        .status-badge {
                            display: inline-flex;
                            align-items: center;
                            padding: 8rpx 20rpx;
                            border-radius: 30rpx;
                            margin-bottom: 16rpx;

                            .status-text {
                                color: #fff;
                                font-size: 24rpx;
                                font-weight: bold;
                            }
                        }

                        .expire-info {
                            display: flex;
                            align-items: center;
                            margin-bottom: 8rpx;

                            .expire-label {
                                color: rgba(255, 255, 255, 0.8);
                                font-size: 24rpx;
                                margin-right: 10rpx;
                            }

                            .expire-date {
                                color: #fff;
                                font-size: 26rpx;
                                font-weight: 500;
                            }
                        }

                        .remaining-days {
                            color: rgba(255, 255, 255, 0.9);
                            font-size: 22rpx;
                        }
                    }

                    .member-inactive {
                        .status-badge.inactive {
                            background: rgba(255, 255, 255, 0.2);
                            padding: 8rpx 20rpx;
                            border-radius: 30rpx;
                            margin-bottom: 16rpx;

                            .status-text {
                                color: rgba(255, 255, 255, 0.8);
                                font-size: 24rpx;
                            }
                        }

                        .upgrade-hint {
                            color: rgba(255, 255, 255, 0.9);
                            font-size: 26rpx;
                        }
                    }
                }
            }
        }

        /* 会员特权预览 */
        .privileges-preview {
            margin-top: 30rpx;
            padding-top: 30rpx;
            border-top: 1rpx solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 2;

            .privilege-item {
                display: inline-flex;
                align-items: center;
                background: rgba(255, 255, 255, 0.15);
                padding: 8rpx 16rpx;
                border-radius: 20rpx;
                margin-right: 16rpx;
                margin-bottom: 12rpx;

                .privilege-icon {
                    font-size: 24rpx;
                    margin-right: 8rpx;
                }

                .privilege-text {
                    color: #fff;
                    font-size: 22rpx;
                }
            }
        }
    }

    .section-title {
        font-size: 32rpx;
        font-weight: bold;
        margin: 40rpx 0 30rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .subtitle {
            font-size: 24rpx;
            color: #666;
            font-weight: normal;
        }
    }

    .plans {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 30rpx;

        .plan-card {
            background: #fff;
            border-radius: 16rpx;
            padding: 30rpx;
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2rpx solid transparent;

            &.popular {
                border: 2rpx solid #f0cc6c;

                .popular-tag {
                    position: absolute;
                    top: -20rpx;
                    right: 30rpx;
                    background: #f0cc6c;
                    color: #fff;
                    font-size: 24rpx;
                    padding: 8rpx 20rpx;
                    border-radius: 30rpx;
                }
            }

            &.selected {
                border: 2rpx solid #8966ef;
                box-shadow: 0 6rpx 20rpx rgba(137, 102, 239, 0.2);
                transform: translateY(-4rpx);
            }

            &.selected.popular {
                border: 2rpx solid #f0cc6c;
                box-shadow: 0 6rpx 20rpx rgba(240, 204, 108, 0.2);
            }

            .selected-icon {
                position: absolute;
                top: 20rpx;
                right: 20rpx;
            }

            &.popular .selected-icon .iconfont {
                color: #f0cc6c;
            }

            .plan-name {
                font-size: 32rpx;
                color: #8966ef;
                font-weight: bold;
                margin-bottom: 20rpx;
            }

            .price {
                font-size: 48rpx;
                font-weight: bold;
                color: #333;
            }

            .original-price {
                color: #999;
                text-decoration: line-through;
                margin: 10rpx 0;
            }

            .discount {
                display: inline-block;
                background: #f9f6ff;
                color: #8966ef;
                padding: 6rpx 20rpx;
                border-radius: 30rpx;
                font-size: 24rpx;
                margin: 10rpx 0 20rpx;
            }

            .plan-desc {
                color: #666;
                font-size: 26rpx;
                margin-bottom: 30rpx;
            }

            .subscribe-btn {
                background: linear-gradient(90deg, #8966ef, #a584f2);
                color: #fff;
                border: none;
                padding: 20rpx;
                border-radius: 40rpx;
                font-size: 28rpx;
                font-weight: bold;
                width: 100%;
                position: relative;
                z-index: 2;
            }
        }
    }

    .benefits {
        background: #fff;
        border-radius: 16rpx;
        padding: 30rpx;
        margin-top: 40rpx;

        .benefit-grid {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            gap: 30rpx;

            .benefit-item {
                display: flex;
                align-items: flex-start;
                padding: 20rpx;
                background: #f9f9ff;
                border-radius: 16rpx;

                .benefit-icon {
                    width: 80rpx;
                    height: 80rpx;
                    background: linear-gradient(135deg, #8966ef 0%, #a584f2 100%);
                    border-radius: 40rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 20rpx;
                    &.iconfont{
                        color: #fff;
                        font-size: 42rpx;
                    }
                }

                .benefit-content {
                    flex: 1;

                    .benefit-title {
                        font-size: 28rpx;
                        font-weight: 500;
                        margin-bottom: 10rpx;
                    }

                    .benefit-desc {
                        font-size: 24rpx;
                        color: #666;
                        line-height: 1.4;
                    }
                }
            }
        }
    }

    .footer {
        text-align: center;
        padding: 40rpx 0;

        .agreement {
            font-size: 24rpx;
            color: #666;
            margin-bottom: 20rpx;
        }

        .terms {
            display: flex;
            justify-content: center;
            gap: 30rpx;

            text {
                color: #8966ef;
                font-size: 24rpx;
            }
        }
    }
}
</style>
