<template>
    <u-popup v-model="show" mode="bottom" border-radius="14" :safe-area-inset-bottom="true" :mask-close-able="true" :z-index="1000">
        <view class="reward-popup">
            <!-- 顶部区域 -->
            <view class="popup-header">
                <text class="popup-title">给作者送个小礼物</text>
                <view class="close-btn" @click="closePopup">
                    <u-icon name="close" color="#999" size="24"></u-icon>
                </view>
            </view>

            <!-- 作者信息 -->
            <view class="author-info" v-if="authorInfo">
                <image class="author-avatar" :src="authorInfo.headimgurl || '/static/images/we.png'" mode="aspectFill"></image>
                <text class="author-name">{{ authorInfo.nickname || '微信用户' }}</text>
            </view>

            <!-- 礼物选项区域 -->
            <view class="gift-options">
                <view class="gift-option" 
                      v-for="gift in giftList" 
                      :key="gift.id"
                      :class="{ selected: selectedGift === gift.id }" 
                      @click="selectGift(gift.id)">
                    <view class="gift-icon">
                        <image :src="gift.icon" mode="widthFix"></image>
                        <text class="gift-count" v-if="gift.count">x{{ gift.count }}</text>
                    </view>
                    <view class="gift-info">
                        <text class="gift-name">{{ gift.name }}</text>
                        <text class="gift-price">${{ gift.price }}</text>
                    </view>
                    <view class="selected-mark" v-if="selectedGift === gift.id">
                        <u-icon name="checkmark" color="#ffffff" size="24"></u-icon>
                    </view>
                </view>
            </view>

            <!-- 余额显示 -->
            <view class="balance-info">
                <text class="balance-text">当前余额：${{ userBalance }}</text>
                <text class="recharge-link" @click="goToRecharge">充值</text>
            </view>

            <!-- 底部按钮区域 -->
            <view class="action-buttons">
                <button class="action-button reward-btn" @click="handleReward" :disabled="!selectedGift">
                    <text class="button-text">{{ rewardButtonText }}</text>
                </button>
            </view>
        </view>
    </u-popup>
</template>

<script>
export default {
    name: 'reward-popup',
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        // 作品ID
        workId: {
            type: String,
            required: true,
            default: '',
        },
        // 作者信息
        authorInfo: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            show: false,
            selectedGift: null,
            userBalance: '0.00',
            giftList: [
                {
                    id: 1,
                    name: '10朵鲜花',
                    price: 10,
                    count: 10,
                    icon: '/static/images/hua.png',
                    good_id: 5, // 对应后端商品ID
                },
                {
                    id: 2,
                    name: '20朵鲜花',
                    price: 20,
                    count: 20,
                    icon: '/static/images/hua.png',
                    good_id: 5,
                },
                {
                    id: 3,
                    name: '30朵鲜花',
                    price: 30,
                    count: 30,
                    icon: '/static/images/hua.png',
                    good_id: 5,
                },
                {
                    id: 4,
                    name: '100朵鲜花',
                    price: 100,
                    count: 100,
                    icon: '/static/images/hua.png',
                    good_id: 5,
                },
            ],
        };
    },
    computed: {
        rewardButtonText() {
            if (!this.selectedGift) {
                return '请选择礼物';
            }
            const gift = this.giftList.find(g => g.id === this.selectedGift);
            return gift ? `赠送 $${gift.price}` : '赠送礼物';
        },
        selectedGiftInfo() {
            return this.giftList.find(g => g.id === this.selectedGift);
        },
    },
    watch: {
        value: {
            handler(val) {
                this.show = val;
                if (val) {
                    this.loadUserBalance();
                    this.loadGiftList();
                }
            },
            immediate: true,
        },
        show(val) {
            this.$emit('input', val);
        },
    },
    methods: {
        closePopup() {
            this.show = false;
            this.$emit('input', false);
            this.$emit('close');
        },
        
        selectGift(giftId) {
            this.selectedGift = giftId;
        },
        
        // 加载用户余额
        async loadUserBalance() {
            try {
                const res = await this.$api.getUserWallet({
                    access_token: uni.getStorageSync('token'),
                });
                
                if (res.status === 0 && res.data && res.data.balance) {
                    this.userBalance = res.data.balance.balance || '0.00';
                }
            } catch (error) {
                console.error('获取余额失败', error);
            }
        },
        
        // 加载礼物列表（可选，如果需要从后端获取）
        async loadGiftList() {
            try {
                // 可以从后端获取礼物配置
                const res = await this.$api.getOrderGoodList({
                    type: 3, // 假设3是礼物类型
                    access_token: uni.getStorageSync('token')
                });
                console.log(res)
                // 这里使用默认配置
            } catch (error) {
                console.error('获取礼物列表失败', error);
            }
        },
        
        // 处理打赏
        async handleReward() {
            if (!this.selectedGiftInfo) {
                return;
            }
            
            const gift = this.selectedGiftInfo;
            const currentBalance = parseFloat(this.userBalance);
            
            // 检查余额是否足够
            if (currentBalance < gift.price) {
                this.showRechargeConfirm(gift.price);
                return;
            }
            
            // 显示确认弹窗
            this.showRewardConfirm(gift);
        },
        
        // 显示充值确认弹窗
        showRechargeConfirm(requiredAmount) {
            uni.showModal({
                title: '余额不足',
                content: `当前余额不足，需要¥${requiredAmount}，是否前往充值？`,
                confirmText: '去充值',
                cancelText: '取消',
                success: (res) => {
                    if (res.confirm) {
                        this.goToRecharge();
                    }
                }
            });
        },
        
        // 显示打赏确认弹窗
        showRewardConfirm(gift) {
            uni.showModal({
                title: '确认打赏',
                content: `确定要赠送${gift.name}吗？将消耗¥${gift.price}`,
                confirmText: '确认赠送',
                cancelText: '取消',
                success: (res) => {
                    if (res.confirm) {
                        this.sendReward(gift);
                    }
                }
            });
        },
        
        // 发送打赏
        async sendReward(gift) {
            uni.showLoading({
                title: '打赏中...'
            });

            try {
                const res = await this.$api.rewardAuthor({
                    good_id: gift.good_id,
                    num: gift.count,
                    work_id: this.workId,
                    access_token: uni.getStorageSync('token')
                });

                uni.hideLoading();

                if (res.status === 0) {
                    uni.showToast({
                        title: '打赏成功',
                        icon: 'none'
                    });
                    
                    // 更新余额
                    this.loadUserBalance();
                    
                    // 通知父组件
                    this.$emit('reward-success', {
                        gift: gift,
                        workId: this.workId
                    });
                    
                    this.closePopup();
                } else {
                    uni.showToast({
                        title: res.msg || '赠送失败',
                        icon: 'none'
                    });
                }
            } catch (error) {
                console.error('赠送失败', error);
                uni.hideLoading();
                uni.showToast({
                    title: '网络异常，请稍后重试',
                    icon: 'none'
                });
            }
        },
        
        // 前往充值页面
        goToRecharge() {
            this.closePopup();
            uni.navigateTo({
                url: '/views/wallet/recharge'
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.reward-popup {
    background: #fff;
    border-radius: 14rpx 14rpx 0 0;
    padding: 40rpx 30rpx;
    max-height: 80vh;
    overflow-y: auto;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
    
    .popup-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
    }
    
    .close-btn {
        padding: 10rpx;
    }
}

.author-info {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 40rpx;
    
    .author-avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        margin-right: 20rpx;
    }
    
    .author-name {
        font-size: 28rpx;
        color: #666;
    }
}

.gift-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;
    margin-bottom: 30rpx;
}

.gift-option {
    position: relative;
    background: #f8f9fa;
    border: 2rpx solid transparent;
    border-radius: 16rpx;
    padding: 30rpx 20rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: all 0.3s ease;
    
    &.selected {
        // background: linear-gradient(135deg, #8966ef, #a584f2);
        border-color: #8966ef;
        color: #fff;
        
        .gift-info {
            color: #fff;
        }
    }
    
    .gift-icon {
        position: relative;
        margin-bottom: 15rpx;
        
        image {
            width: 60rpx;
            height: 60rpx;
        }
        
        .gift-count {
            position: absolute;
            bottom: -5rpx;
            right: -5rpx;
            background: #ff4757;
            color: #fff;
            font-size: 20rpx;
            padding: 2rpx 8rpx;
            border-radius: 10rpx;
            min-width: 30rpx;
            text-align: center;
        }
    }
    
    .gift-info {
        text-align: center;
        
        .gift-name {
            display: block;
            font-size: 24rpx;
            color: #333;
            margin-bottom: 5rpx;
        }
        
        .gift-price {
            font-size: 28rpx;
            font-weight: bold;
            color: #8966ef;
        }
    }
    
    .selected-mark {
        position: absolute;
        top: 10rpx;
        right: 10rpx;
        width: 40rpx;
        height: 40rpx;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

.balance-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
    margin-bottom: 30rpx;
    
    .balance-text {
        font-size: 26rpx;
        color: #666;
    }
    
    .recharge-link {
        font-size: 26rpx;
        color: #8966ef;
        text-decoration: underline;
    }
}

.action-buttons {
    .action-button {
        width: 100%;
        height: 88rpx;
        background: linear-gradient(135deg, #8966ef, #a584f2);
        border: none;
        border-radius: 44rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &:disabled {
            background: #ccc;
        }
        
        .button-text {
            font-size: 32rpx;
            font-weight: bold;
            color: #fff;
        }
    }
}
</style>
