<template>
    <view class="login-container">
        <!-- 背景装饰 -->
        <view class="bg-decoration">
            <view class="bg-circle bg-circle-1"></view>
            <view class="bg-circle bg-circle-2"></view>
            <view class="bg-circle bg-circle-3"></view>
        </view>

        <!-- 骨架屏 -->
        <block v-if="skeLoading">
            <view class="ske-top u-skeleton-fillet"></view>
            <view class="ske-item u-skeleton-fillet" v-for="i in 6" :key="i"></view>
        </block>

        <!-- 主要内容 -->
        <view class="main-content" v-else>
            <!-- Logo区域 -->
            <view class="logo-section">
                <view class="logo-container">
                    <image class="logo-image" mode="aspectFit" src="https://friend.wdaoyun.cn/static/images/index/logo.png"></image>
                </view>
                <view class="welcome-text">
                    <text class="welcome-title">欢迎来到跃汇街</text>
                    <text class="welcome-subtitle">专业的体育社交平台</text>
                </view>
            </view>

            <!-- 登录表单 -->
            <view class="login-form">
                <view class="form-card">
                    <!-- 手机号输入 -->
                    <view class="input-group">
                        <view class="input-icon">
                            <u-icon name="phone" size="32" color="#8966ef"></u-icon>
                        </view>
                        <u-input
                            class="custom-input"
                            v-model="mobile"
                            :clearable="false"
                            :custom-style="inputStyle"
                            :placeholder-style="placeholderStyle"
                            placeholder="请输入手机号"
                            type="number"
                            maxlength="11"
                        />
                    </view>

                    <!-- 验证码输入 -->
                    <view class="input-group">
                        <view class="input-icon">
                            <u-icon name="checkmark-circle" size="32" color="#8966ef"></u-icon>
                        </view>
                        <u-input
                            class="custom-input"
                            v-model="code"
                            :clearable="false"
                            :custom-style="inputStyle"
                            :placeholder-style="placeholderStyle"
                            placeholder="请输入验证码"
                            type="number"
                            maxlength="6"
                        />
                        <view class="code-btn" @click="getCode" :class="{ 'code-btn-disabled': !agreement || !$u.test.mobile(mobile) }">
                            {{ tips || '获取验证码' }}
                        </view>
                    </view>

                    <!-- 提示信息 -->
                    <view class="form-notice">
                        <u-icon name="info-circle" size="14" color="#f0cc6c"></u-icon>
                        <text>此小程序仅为跃汇街设备用户服务，请凭账号登录</text>
                    </view>

                    <!-- 登录按钮 -->
                    <view class="submit-section">
                        <button class="login-btn" :class="{ 'login-btn-disabled': !agreement }" @click="$u.throttle(login, 2000)" :disabled="!agreement">
                            <text class="login-btn-text">立即登录</text>
                        </button>
                    </view>

                    <!-- 隐私协议 -->
                    <view class="privacy-section">
                        <view class="privacy-check">
                            <u-checkbox class="custom-checkbox" active-color="#8966ef" v-model="agreement" size="30">
                                <text class="privacy-text">我已阅读并同意</text>
                            </u-checkbox>
                            <button id="agree-btn" type="default" open-type="agreePrivacyAuthorization" class="privacy-btn" @agreeprivacyauthorization="handleAgree"></button>
                        </view>
                        <text class="privacy-link" @click.stop="openPrivacyContract">《跃汇街小程序隐私保护指引》</text>
                    </view>
                </view>
            </view>
        </view>
        <!-- <view class="other">
            <view class="other-title">
                <view class="other-title-line"></view>
                <text class="other-title-text">其他登录方式</text>
                <view class="other-title-line"></view>
            </view>
            <view class="other-icon">
                <view class="other-icon-img" v-if="agreement">
                    <image src="/static/images/phone.png"></image>
                    <button class="phone-btn" @getphonenumber="getphonenumber" open-type="getPhoneNumber"></button>
                </view>
                <view class="other-icon-img" v-else @click="getPhoneFail">
                    <image src="/static/images/phone.png"></image>
                </view>
                <view class="other-icon-text">手机号快捷登录</view>
            </view>
        </view> -->
        <u-skeleton :loading="skeLoading" :animation="true" bgColor="#FFF"></u-skeleton>
        <u-verification-code change-text="xs" ref="uCode" @change="codeChange"></u-verification-code>
    </view>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import { updatePhone, setPassword } from '../../utils/vmeitime-http/user.js';
export default {
    computed: { ...mapState(['appType']) },
    data() {
        return {
            title: 'Hello',
            mobile: '',
            inputStyle: {
                fontSize: '32rpx',
                color: '#333',
                backgroundColor: 'transparent',
                border: 'none',
            },
            placeholderStyle: 'font-size:28rpx;color:rgba(255,255,255,0.6)',
            agreement: false,
            skeLoading: true,
            tips: '',
            code: '',
            codeId: '',
            privacyResolves: new Set(),
            route: '',
        };
    },
    onLoad(e) {
        // if (uni.getStorageSync('token')) {
        //     uni.reLaunch({
        //         url: '/pages/index/device',
        //     });
        //     return;
        // }
        // this.getCodes();
        this.route = e?.route || '';
        this.skeLoading = false;
        this.handleTouchInput();
        if (wx.onNeedPrivacyAuthorization) {
            wx.onNeedPrivacyAuthorization(resolve => {
                this.privacyHandler(resolve);
            });
        } else {
            uni.showModal({
                title: '提示',
                content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。',
                confirmText: '我知道了',
                showCancel: false,
            });
        }
    },
    methods: {
        ...mapActions(['VAgetUser']),
        async getCodes() {
            console.log(await this.wxLogin());
        },
        // 登录
        async login() {
            console.log('login');
            if (!this.$u.test.mobile(this.mobile)) {
                this.$u.toast('请输入正确的手机号码', 2000);
                return;
            }
            if (!this.codeId) {
                this.$u.toast('验证码错误，请重试', 2000);
                return;
            }
            const code = await this.wxLogin();
            let res = await this.$api.codeLogin({
                code: code,
                app_type: this.appType,
            });
            if (res.status == 0) {
                const token = res.data.access_token;
                uni.setStorage({
                    key: 'token',
                    data: token,
                });
                uni.setStorageSync('loginTime', new Date().getTime());
                uni.$emit('loginSuccess');
                this.loginSuccess(token);
            }
        },
        // 登陆成功
        async loginSuccess(data) {
            this.VAgetUser().then(async res => {
                console.log(res);
                if (!res.data.mobile) {
                    await updatePhone({
                        mobile: this.mobile,
                        code: this.code,
                        code_id: this.codeId,
                        access_token: data,
                    });
                }
                if (!res.data.password) {
                    await setPassword({
                        access_token: data,
                        password: '123456',
                        old_password: '',
                    });
                }
                if (this.route) {
                    uni.navigateBack();
                    return;
                }
                uni.reLaunch({
                    url: '/pages/index/index',
                });
            });
        },
        async getOpenId(code, token) {
            this.$api.getOpenId({
                access_token: token,
                code: code,
            });
        },

        // 打开隐私链接
        openPrivacyContract() {
            wx.openPrivacyContract({
                success: res => {
                    console.log('openPrivacyContract success');
                },
                fail: res => {
                    console.error('openPrivacyContract fail', res);
                },
            });
        },
        // 微信登录
        async wxLogin() {
            return new Promise((resolve, reject) => {
                uni.login({
                    provider: 'weixin',
                    success: res => {
                        resolve(res.code);
                    },
                    fail: res => {
                        uni.showToast({
                            title: '获取code失败,请重新授权尝试获取！',
                            icon: 'none',
                        });
                    },
                });
            });
        },

        getPhoneFail() {
            if (!this.agreement) {
                uni.showToast({
                    title: '请先阅读并同意用户协议',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
        },

        // 微信手机号码登录
        async getphonenumber(e) {
            let res = await this.$api.getphone({
                code: e.detail.code,
                app_type: this.appType,
            });
            if (res.status == 0) {
                this.loginSuccess(res.data);
            }
        },

        privacyHandler(resolve) {
            this.privacyResolves.add(resolve);
        },

        // 获取验证码
        async getCode() {
            if (!this.agreement) {
                uni.showToast({
                    title: '请先阅读并同意用户协议',
                    icon: 'none',
                    duration: 2000,
                });
                return;
            }
            if (!this.$u.test.mobile(this.mobile)) {
                this.$u.toast('请输入正确的手机号码', 2000);
                return;
            }
            if (this.$refs.uCode.canGetCode) {
                // 模拟向后端请求验证码
                uni.showLoading({
                    title: '获取验证码',
                });
                try {
                    let res = await this.$api.sendCode({
                        mobile: this.mobile,
                        app_type: this.appType,
                    });
                    if (res.status == 0) {
                        this.codeId = res.data;
                    }
                    uni.hideLoading();
                    this.$u.toast('验证码已发送', 2000);
                    this.$refs.uCode.start();
                } catch (error) {
                    uni.hideLoading();
                }
            } else {
                // this.$u.toast('60s内无法'), 2000);
            }
        },

        // 验证码变化
        codeChange(text, index) {
            this.tips = text;
        },

        handleAgree(e) {
            console.log('handleAgree');
            // this.disPopUp();
            // 这里演示了同时调用多个wx隐私接口时要如何处理：让隐私弹窗保持单例，点击一次同意按钮即可让所有pending中的wx隐私接口继续执行 （看page/index/index中的 wx.getClipboardData 和 wx.startCompass）
            this.privacyResolves.forEach(resolve => {
                resolve({
                    event: 'agree',
                    buttonId: 'agree-btn',
                });
            });
            this.privacyResolves.clear();
            this.agreement = !this.agreement;
        },
        routeLink(url) {
            // url = encodeURIComponent(url);
            console.log(url);
            uni.navigateTo({
                url: url,
                fail: err => {
                    console.log(err);
                },
            });
        },
        handleTouchInput() {
            if (wx.getPrivacySetting) {
                wx.getPrivacySetting({
                    success: res => {
                        console.log(res); // 返回结果为: res = { needAuthorization: true/false, privacyContractName: '《xxx隐私保护指引》' }
                        if (res.needAuthorization) {
                            // 需要弹出隐私协议
                            // uni.showToast({
                            //     title: '请先阅读并同意用户协议',
                            //     icon: 'none',
                            //     duration: 2000,
                            // });
                        }
                    },
                    fail: err => {
                        console.log(err);
                    },
                });
                return;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #87ceeb 0%, #ba55d3 100%);
    position: relative;
    overflow: hidden;
}

/* 背景装饰 */
.bg-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;

    .bg-circle {
        position: absolute;
        border-radius: 50%;
        opacity: 0.15;
        backdrop-filter: blur(8px);

        &.bg-circle-1 {
            width: 300rpx;
            height: 300rpx;
            background: linear-gradient(135deg, #8a2be2, #4682b4);
            top: -150rpx;
            right: -150rpx;
            animation: float 6s ease-in-out infinite;
            box-shadow: 0 8rpx 32rpx rgba(138, 43, 226, 0.2);
        }

        &.bg-circle-2 {
            width: 200rpx;
            height: 200rpx;
            background: linear-gradient(135deg, #ff6347, #ffd700);
            bottom: 200rpx;
            left: -100rpx;
            animation: float 8s ease-in-out infinite reverse;
            box-shadow: 0 6rpx 24rpx rgba(255, 99, 71, 0.2);
        }

        &.bg-circle-3 {
            width: 150rpx;
            height: 150rpx;
            background: linear-gradient(135deg, #32cd32, #00ced1);
            top: 50%;
            right: 50rpx;
            animation: float 10s ease-in-out infinite;
            box-shadow: 0 4rpx 16rpx rgba(50, 205, 50, 0.2);
        }
    }
}

@keyframes float {
    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

/* 主要内容 */
.main-content {
    position: relative;
    z-index: 1;
    padding: 60rpx 40rpx 40rpx;
}

/* Logo区域 */
.logo-section {
    text-align: center;
    margin-bottom: 80rpx;

    .logo-container {
        margin-bottom: 40rpx;

        .logo-image {
            width: 160rpx;
            height: 160rpx;
            border-radius: 50%;
            box-shadow: 0 12rpx 40rpx rgba(255, 255, 255, 0.3);
        }
    }

    .welcome-text {
        .welcome-title {
            display: block;
            font-size: 48rpx;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 16rpx;
            text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
            font-family: 'San Francisco Pro', system-ui, sans-serif;
        }

        .welcome-subtitle {
            display: block;
            font-size: 28rpx;
            color: #e0e0e0;
            opacity: 0.9;
            font-weight: 300;
        }
    }
}

/* 登录表单 */
.login-form {
    .form-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(15px);
        border-radius: 32rpx;
        padding: 60rpx 40rpx;
        box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
}
/* 输入框组 */
.input-group {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    border-radius: 24rpx;
    padding: 24rpx 32rpx;
    margin-bottom: 32rpx;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;

    &:focus-within {
        border-color: rgba(255, 255, 255, 0.3);
        background: rgba(255, 255, 255, 0.15);
        box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
    }

    .input-icon {
        margin-right: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .custom-input {
        flex: 1;
        font-size: 32rpx;
        color: #ffffff;
        font-family: 'San Francisco Pro', system-ui, sans-serif;
    }

    .code-btn {
        padding: 16rpx 24rpx;
        background: linear-gradient(135deg, #8a2be2, #4682b4);
        color: #ffffff;
        border-radius: 16rpx;
        font-size: 24rpx;
        font-weight: 500;
        text-align: center;
        min-width: 160rpx;
        transition: all 0.3s ease;
        box-shadow: 0 4rpx 10rpx rgba(0, 0, 0, 0.2);

        &:active {
            transform: scale(0.95);
        }

        &.code-btn-disabled {
            background: rgba(255, 255, 255, 0.3);
            color: rgba(255, 255, 255, 0.6);
            box-shadow: none;
        }
    }
}

/* 提示信息 */
.form-notice {
    display: flex;
    align-items: center;
    padding: 24rpx;
    background: rgba(255, 215, 0, 0.15);
    backdrop-filter: blur(8px);
    border-radius: 16rpx;
    margin-bottom: 40rpx;
    border: 1px solid rgba(255, 215, 0, 0.2);

    text {
        margin-left: 16rpx;
        font-size: 24rpx;
        color: #e0e0e0;
        line-height: 1.4;
        font-weight: 300;
    }
}

/* 登录按钮 */
.submit-section {
    margin-bottom: 40rpx;

    .login-btn {
        width: 100%;
        height: 96rpx;
        background: linear-gradient(135deg, #8a2be2, #4682b4);
        border-radius: 24rpx;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;

        &:active {
            transform: translateY(2rpx);
            box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.2);
        }

        &.login-btn-disabled {
            background: rgba(255, 255, 255, 0.3);
            box-shadow: none;

            .login-btn-text {
                color: rgba(255, 255, 255, 0.6);
            }
        }

        .login-btn-text {
            font-size: 32rpx;
            font-weight: 500;
            color: #ffffff;
            font-family: 'San Francisco Pro', system-ui, sans-serif;
        }
    }
}
/* 隐私协议 */
.privacy-section {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 16rpx;

    .privacy-check {
        position: relative;
        display: flex;
        align-items: center;

        .custom-checkbox {
            ::v-deep .u-checkbox__label {
                margin-right: 0;
                font-size: 24rpx;
                color: #e0e0e0;
                font-weight: 300;
            }
        }

        .privacy-text {
            font-size: 24rpx;
            color: #e0e0e0;
            margin-left: 8rpx;
            font-weight: 300;
        }

        .privacy-btn {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            border: none;
            background: transparent;
            z-index: 9;
        }
    }

    .privacy-link {
        font-size: 24rpx;
        color: #ffffff;
        text-decoration: underline;
        cursor: pointer;
        font-weight: 400;

        &:active {
            opacity: 0.7;
        }
    }
}
button::after {
    border: none;
}

/* 骨架屏样式 */
.ske-top {
    height: 200rpx;
    margin: 60rpx 40rpx 40rpx;
    border-radius: 16rpx;
}

.ske-item {
    height: 80rpx;
    margin: 20rpx 40rpx;
    border-radius: 12rpx;
}
</style>
