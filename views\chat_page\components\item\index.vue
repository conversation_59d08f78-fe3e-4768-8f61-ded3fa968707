<template>
	<view>
		<!-- 群公告 -->
		<view class="text_26 icon_ group_notice" v-if="item.type === 'group_notice'">
			管理员设置了新的
			<text style="color: #fe6702; margin: 0 10rpx">群公告</text>
			请及时查看
		</view>
		<view class="text_26 icon_ group_notice" v-else-if="item.type === 'update_group_name'">
			管理员修改了群名称为:"
			<text style="color: #fe6702; margin: 0 10rpx">{{ item.payload.name }}</text>
			"
		</view>
		<!-- 使用新的消息气泡组件 -->
		<view class="message-item" v-else>
			<new-chat-bubble 
				:message="formatMessage(item)" 
				:isSelf="isMy" 
				:avatar="item.senderData.avatar"
				@longpressAvatar="longpressAvatar"
				@longpressMessage="longpress"
				@click="onClick"
			/>
			<view class="sender-name" v-if="!isMy && item.senderData.name">
				{{ item.senderData.name }}
			</view>
		</view>
	</view>
</template>

<script>
import { to, vibrateShort } from '../../utils/index.js';

let isLongpress = false;
let isLongpressAvatar = false;
export default {
	components: {
	},
	props: {
		myid: {
			type: [String, Number],
			default: null
		},
		isMy: {
			type: [Boolean, Number],
			default: false
		},
		item: {
			type: Object,
			default: {}
		},
		index: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			userInforData: {}
		};
	},
	methods: {
		// 将原始消息转换为NewChatBubble需要的格式
		formatMessage(item) {
			let message = {
				type: item.type,
				content: '',
				status: item.status
			};
			
			// 根据不同消息类型设置内容
			switch(item.type) {
				case 'text':
				case 'text_quote':
					message.type = 'text';
					message.content = item.payload?.text || item.payload?.content || item.content || '';
					break;
				case 'image':
				case 'image_transmit':
					message.type = 'image';
					message.content = item.payload?.url || item.content || '';
					break;
				case 'audio':
					message.type = 'voice';
					message.content = item.payload?.url || item.content || '';
					message.duration = item.payload?.duration || 1;
					break;
				case 'video':
					message.type = 'video';
					message.content = item.payload?.url || item.content || '';
					break;
				case 'map':
					message.type = 'map';
					message.content = item.payload || item.content || '';
					break;
				default:
					message.type = 'text';
					message.content = JSON.stringify(item.payload || item.content || '');
			}
			
			return message;
		},
		
		onClick() {
			if (isLongpress) return (isLongpress = false);
			if (this.item.status === 'error') return;
			this.$emit('onClick', this.item, this.index);
		},
		
		// @某人
		longpressAvatar() {
			isLongpressAvatar = true;
			this.$emit('mention', this.item, this.index);
		},
		
		onItem() {
			if (isLongpressAvatar) return (isLongpressAvatar = false);
			console.log(this.item);
			to('/pagesGoEasy/group_member_infor/index', { member_id: this.item.senderId, group_id: this.item.groupId });
		},

		// 长按消息
		longpress(e) {
			isLongpress = true;
			vibrateShort();
			console.log(this.item);
			this.$nextTick(() => {
				const query = uni.createSelectorQuery().in(this);
				query
					.select(`.message-item`)
					.boundingClientRect((data) => {
						this.$emit('onLongpress', this.item, data);
					})
					.exec();
			});
		}
	}
};
</script>

<style scoped lang="scss">
.group_notice {
	width: 100%;
	height: 80rpx;
	color: #a3a3a3;
	text-align: center;
	padding: 10rpx 0;
}

.message-item {
	position: relative;
	width: 100%;
	padding: 0 30rpx;
	margin-bottom: 30rpx;
	box-sizing: border-box;
}

.sender-name {
	font-size: 24rpx;
	color: #999;
	margin-left: 110rpx;
	margin-top: -5rpx;
}
</style>
