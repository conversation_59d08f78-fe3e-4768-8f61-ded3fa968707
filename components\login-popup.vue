<template>
    <u-popup v-model="show" mode="center" border-radius="24" width="650rpx" height="auto" :mask-close-able="true" :safe-area-inset-bottom="true" :custom-style="{ background: 'transparent' }">
        <view class="login-popup">
            <!-- 关闭按钮 -->
            <view class="close-btn" @click="closePopup">
                <u-icon name="close" color="#999" size="32"></u-icon>
            </view>

            <!-- Logo区域 -->
            <view class="logo-section">
                <view class="logo-container">
                    <image class="logo-img" src="https://static.wdaoyun.com/friend/2025-07-02/cf6c218c3e5a2948e02cbc2e31dd5266.png" mode="aspectFit"></image>
                </view>
                <view class="welcome-text">
                    <text class="main-title">请先登录</text>
                    <text class="sub-title">登录后可享受更多功能</text>
                </view>
            </view>

            <!-- 提示信息 -->
            <view class="notice-section">
                <view class="notice-item">
                    <u-icon name="info-circle" size="32" color="#8966ef"></u-icon>
                    <text class="notice-text">登录后可以私聊、评论、关注等</text>
                </view>
            </view>

            <!-- 按钮区域 -->
            <view class="button-section">
                <button class="login-btn" @click="goToLogin" hover-class="btn-hover">
                    <view class="btn-content">
                        <u-icon name="account" size="36" color="#ffffff"></u-icon>
                        <text class="btn-text">立即登录</text>
                    </view>
                </button>

                <button class="cancel-btn" @click="closePopup" hover-class="cancel-btn-hover">
                    <text class="cancel-text">暂不登录</text>
                </button>
            </view>

            <!-- 装饰元素 -->
            <view class="decoration-circle circle-1"></view>
            <view class="decoration-circle circle-2"></view>
            <view class="decoration-circle circle-3"></view>
        </view>
    </u-popup>
</template>

<script>
export default {
    name: 'LoginPopup',
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },
    computed: {
        show: {
            get() {
                return this.value;
            },
            set(val) {
                this.$emit('input', val);
            },
        },
    },
    methods: {
        closePopup() {
            this.show = false;
            this.$emit('close');
        },

        goToLogin() {
            // 获取当前页面栈的实例
            let curPage = getCurrentPages();

            // 获取当前页面的路由
            let route = curPage[curPage.length - 1].route;
            this.closePopup();
            uni.navigateTo({
                url: `/pages/index/login?route=${route}`,
            });
            this.$emit('login');
        },
    },
};
</script>

<style lang="scss" scoped>
.login-popup {
    position: relative;
    background: linear-gradient(135deg, #ffffff 0%, #f8f6ff 100%);
    border-radius: 24rpx;
    padding: 60rpx 40rpx 50rpx;
    overflow: hidden;
    box-shadow: 0 20rpx 60rpx rgba(137, 102, 239, 0.15);
}

.close-btn {
    position: absolute;
    top: 30rpx;
    right: 30rpx;
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    backdrop-filter: blur(10rpx);
    z-index: 10;
    transition: all 0.3s ease;

    &:active {
        transform: scale(0.9);
        background: rgba(255, 255, 255, 0.9);
    }
}

.logo-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 50rpx;
}

.logo-container {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, #8966ef, #a584f2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 30rpx;
    box-shadow: 0 10rpx 30rpx rgba(137, 102, 239, 0.3);
    animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
    0%,
    100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10rpx);
    }
}

.logo-img {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
}

.welcome-text {
    text-align: center;
}

.main-title {
    display: block;
    font-size: 40rpx;
    font-weight: 600;
    background: linear-gradient(135deg, #8966ef, #a584f2);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 12rpx;
}

.sub-title {
    display: block;
    font-size: 28rpx;
    color: #666;
    line-height: 1.4;
}

.notice-section {
    margin-bottom: 50rpx;
}

.notice-item {
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(137, 102, 239, 0.08);
    padding: 20rpx 30rpx;
    border-radius: 16rpx;
    border: 2rpx solid rgba(137, 102, 239, 0.1);
}

.notice-text {
    font-size: 26rpx;
    color: #8966ef;
    margin-left: 12rpx;
    font-weight: 500;
}

.button-section {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.login-btn {
    background: linear-gradient(135deg, #8966ef, #a584f2);
    border: none;
    border-radius: 20rpx;
    height: 88rpx;
    box-shadow: 0 8rpx 24rpx rgba(137, 102, 239, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    // padding:0 12rpx;
    width: 250rpx;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    &:active::before {
        left: 100%;
    }
}

.btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    gap: 12rpx;
}

.btn-text {
    font-size: 32rpx;
    color: #ffffff;
    font-weight: 600;
}

.btn-hover {
    transform: translateY(2rpx);
    box-shadow: 0 4rpx 16rpx rgba(137, 102, 239, 0.4);
}

.cancel-btn {
    background: transparent;
    border: 2rpx solid #e0e0e0;
    border-radius: 20rpx;
    height: 88rpx;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 250rpx;
}

.cancel-text {
    font-size: 30rpx;
    color: #999;
    font-weight: 500;
}

.cancel-btn-hover {
    border-color: #8966ef;
    background: rgba(137, 102, 239, 0.05);

    .cancel-text {
        color: #8966ef;
    }
}

/* 装饰元素 */
.decoration-circle {
    position: absolute;
    border-radius: 50%;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}

.circle-1 {
    width: 80rpx;
    height: 80rpx;
    background: linear-gradient(135deg, #8966ef, #a584f2);
    top: 20rpx;
    left: 20rpx;
    animation-delay: 0s;
}

.circle-2 {
    width: 60rpx;
    height: 60rpx;
    background: linear-gradient(135deg, #f0cc6c, #edc353);
    top: 50%;
    right: 30rpx;
    animation-delay: 2s;
}

.circle-3 {
    width: 40rpx;
    height: 40rpx;
    background: linear-gradient(135deg, #8966ef, #f0cc6c);
    bottom: 30rpx;
    left: 40rpx;
    animation-delay: 4s;
}

@keyframes float {
    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20rpx) rotate(180deg);
    }
}
</style>
