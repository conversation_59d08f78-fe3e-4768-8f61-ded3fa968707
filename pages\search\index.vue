<template>
    <view class="search-page">
        <!-- 搜索头部 -->
        <view class="search-header">
            <view class="search-input-container">
                <view class="back-btn" @click="goBack">
                    <text class="back-icon">←</text>
                </view>
                <view class="search-input-box">
                    <input 
                        class="search-input" 
                        v-model="searchKeyword" 
                        placeholder="搜索感兴趣的内容..." 
                        @input="onSearchInput"
                        @confirm="onSearchConfirm"
                        focus
                    />
                    <view class="search-btn" @click="onSearchConfirm" v-if="searchKeyword">
                        <text class="search-icon">🔍</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 搜索建议 -->
        <view class="search-suggestions" v-if="!searchKeyword && !hasSearched">
            <view class="suggestion-section">
                <text class="section-title">热门搜索</text>
                <view class="suggestion-tags">
                    <view 
                        class="suggestion-tag" 
                        v-for="(tag, index) in hotSearchTags" 
                        :key="index"
                        @click="selectSuggestion(tag)"
                    >
                        {{ tag }}
                    </view>
                </view>
            </view>
        </view>

        <!-- 搜索结果 -->
        <view class="search-results" v-if="hasSearched">
            <view class="results-header">
                <text class="results-count">找到 {{ searchResults.length }} 个结果</text>
            </view>
            
            <view class="results-grid" v-if="searchResults.length > 0">
                <view class="result-item" v-for="(item, index) in searchResults" :key="index">
                    <user-card :info="item" :index="index" @like="handleLike"></user-card>
                </view>
            </view>

            <view class="no-results" v-else>
                <text class="no-results-text">暂无相关结果</text>
                <text class="no-results-tip">试试其他关键词吧</text>
            </view>
        </view>
    </view>
</template>

<script>
import UserCard from '../../components/UserCard';

export default {
    components: {
        UserCard
    },
    data() {
        return {
            searchKeyword: '',
            hasSearched: false,
            searchResults: [],
            hotSearchTags: ['附近', '活跃', '在线', '新人', '认证', '颜值']
        };
    },
    methods: {
        goBack() {
            uni.navigateBack();
        },
        
        onSearchInput(e) {
            this.searchKeyword = e.detail.value;
        },
        
        onSearchConfirm() {
            if (!this.searchKeyword.trim()) return;
            this.performSearch();
        },
        
        selectSuggestion(tag) {
            this.searchKeyword = tag;
            this.performSearch();
        },
        
        async performSearch() {
            this.hasSearched = true;
            // 这里应该调用实际的搜索API
            try {
                // 模拟搜索结果
                this.searchResults = [];
                uni.showToast({
                    title: '搜索功能开发中',
                    icon: 'none'
                });
            } catch (error) {
                console.error('搜索失败', error);
                uni.showToast({
                    title: '搜索失败',
                    icon: 'none'
                });
            }
        },
        
        handleLike(like, index) {
            // 处理点赞逻辑
            this.$set(this.searchResults[index], 'is_like', like);
        }
    }
};
</script>

<style lang="scss" scoped>
.search-page {
    min-height: 100vh;
    background: linear-gradient(180deg, #f8f6ff 0%, #f5f5f5 100%);
}

.search-header {
    background: linear-gradient(135deg, #8966ef 0%, #a584f2 100%);
    padding: 20rpx 30rpx 30rpx;
    box-shadow: 0 8rpx 24rpx rgba(137, 102, 239, 0.15);
}

.search-input-container {
    display: flex;
    align-items: center;
    gap: 20rpx;
}

.back-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    backdrop-filter: blur(10rpx);
}

.back-icon {
    color: #fff;
    font-size: 32rpx;
    font-weight: bold;
}

.search-input-box {
    flex: 1;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 50rpx;
    padding: 20rpx 30rpx;
    display: flex;
    align-items: center;
    backdrop-filter: blur(10rpx);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.search-input {
    flex: 1;
    font-size: 28rpx;
    color: #333;
}

.search-btn {
    margin-left: 20rpx;
}

.search-icon {
    font-size: 28rpx;
    color: #8966ef;
}

.search-suggestions {
    padding: 40rpx 30rpx;
}

.suggestion-section {
    margin-bottom: 40rpx;
}

.section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
}

.suggestion-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
}

.suggestion-tag {
    padding: 16rpx 32rpx;
    background: #fff;
    border-radius: 50rpx;
    font-size: 26rpx;
    color: #666;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.suggestion-tag:hover {
    background: linear-gradient(135deg, #8966ef, #a584f2);
    color: #fff;
    transform: translateY(-2rpx);
}

.search-results {
    padding: 20rpx 30rpx;
}

.results-header {
    margin-bottom: 30rpx;
}

.results-count {
    font-size: 28rpx;
    color: #666;
    font-weight: 500;
}

.results-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
}

.result-item {
    width: calc(50% - 10rpx);
}

.no-results {
    text-align: center;
    padding: 100rpx 0;
}

.no-results-text {
    display: block;
    font-size: 32rpx;
    color: #999;
    margin-bottom: 20rpx;
}

.no-results-tip {
    display: block;
    font-size: 26rpx;
    color: #ccc;
}
</style>
