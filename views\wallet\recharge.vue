<template>
    <view class="recharge-container">
        <!-- 顶部标题 -->
        <view class="section-title">请选择充值金额</view>
        
        <!-- 充值金额选择区 -->
        <view class="amount-selection">
            <view 
                class="amount-item" 
                v-for="(item, index) in amountOptions" 
                :key="index" 
                :class="{active: selectedAmount === item.id}"
                @click="selectAmount(item.id)">
                <view class="amount">¥{{ item.price }}</view>
                <view class="desc" v-if="item.desc">{{ item.desc }}</view>
            </view>
        </view>
        
        <!-- 支付按钮 -->
        <view class="btn-area">
            <button class="recharge-btn" @click="handleRecharge" :disabled="!selectedAmount">立即充值</button>
        </view>
        
        <!-- 充值说明 -->
        <view class="recharge-tips">
            <view class="tips-title">充值说明：</view>
            <view class="tips-item">1. 充值成功后，余额可用于购买平台服务。</view>
            <view class="tips-item">2. 充值金额不支持提现和退款。</view>
            <view class="tips-item">3. 如有问题，请联系客服。</view>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
import { getOrderGoodList, createBalanceOrder } from '../../utils/vmeitime-http/order.js';

export default {
    data() {
        return {
            amountOptions: [],
            selectedAmount: null,
            loading: false
        };
    },
    computed: {
        ...mapState(['hasLogin', 'userInfo']),
    },
    onLoad() {
        this.loadGoodsList();
    },
    methods: {
        // 加载充值商品列表
        async loadGoodsList() {
            if (!this.hasLogin) {
                return;
            }
            
            uni.showLoading({
                title: '加载中...'
            });
            
            try {
                const res = await getOrderGoodList({
                    type: 1, // 金币/余额类型
                    access_token: uni.getStorageSync('token')
                });
                
                if (res.status === 0 && res.data && res.data.list) {
                    this.amountOptions = res.data.list.map(item => ({
                        id: item.id,
                        price: item.amount,
                        desc: item.title
                    }));
                    
                    // 默认选中第一个
                    if (this.amountOptions.length > 0) {
                        this.selectedAmount = this.amountOptions[0].id;
                    }
                } else {
                    uni.showToast({
                        title: res.msg || '获取充值选项失败',
                        icon: 'none'
                    });
                }
            } catch (error) {
                console.error('获取充值选项失败', error);
                uni.showToast({
                    title: '网络异常，请稍后重试',
                    icon: 'none'
                });
            } finally {
                uni.hideLoading();
            }
        },
        
        // 选择充值金额
        selectAmount(id) {
            this.selectedAmount = id;
        },
        
        // 处理充值
        async handleRecharge() {
            if (!this.hasLogin) {
                uni.showToast({
                    title: '请先登录',
                    icon: 'none'
                });
                return;
            }
            
            if (!this.selectedAmount) {
                uni.showToast({
                    title: '请选择充值金额',
                    icon: 'none'
                });
                return;
            }
            
            if (this.loading) {
                return;
            }
            
            this.loading = true;
            uni.showLoading({
                title: '处理中...'
            });
            
            try {
                const res = await createBalanceOrder({
                    good_id: this.selectedAmount,
                    num: 1,
                    access_token: uni.getStorageSync('token')
                });
                
                uni.hideLoading();
                
                if (res.status === 0) {
                    if (res.data && res.data.order_no) {
                        this.payOrder(res.data.order_no);
                    } else {
                        uni.showToast({
                            title: '创建订单成功，但缺少支付信息',
                            icon: 'none'
                        });
                    }
                } else {
                    uni.showToast({
                        title: res.msg || '创建充值订单失败',
                        icon: 'none'
                    });
                }
            } catch (error) {
                console.error('充值失败', error);
                uni.hideLoading();
                uni.showToast({
                    title: '网络异常，请稍后重试',
                    icon: 'none'
                });
            } finally {
                this.loading = false;
            }
        },
        
        // 支付订单
        async payOrder(orderId) {
            try {
                uni.showLoading({
                    title: '拉起支付...'
                });
                
                const res = await this.$api.createMiniPay({
                    orderid: orderId,
                    openid: this.userInfo.openid,
                    access_token: uni.getStorageSync('token')
                });
                
                uni.hideLoading();
                
                if (res.status !== 0 || !res.data) {
                    uni.showToast({
                        title: res.msg || '获取支付信息失败',
                        icon: 'none'
                    });
                    return;
                }
                
                // 调用微信支付
                uni.requestPayment({
                    timeStamp: res.data.timeStamp,
                    nonceStr: res.data.nonceStr,
                    package: res.data.package,
                    signType: res.data.signType,
                    paySign: res.data.paySign,
                    success: () => {
                        uni.showToast({
                            title: '充值成功',
                            icon: 'success'
                        });
                        
                        // 支付成功后返回到钱包首页，并刷新数据
                        setTimeout(() => {
                            uni.navigateBack({
                                success: () => {
                                    // 通知上一个页面刷新数据
                                    uni.$emit('refreshBalance');
                                }
                            });
                        }, 1500);
                    },
                    fail: (err) => {
                        console.log('支付失败', err);
                        uni.showToast({
                            title: '支付失败',
                            icon: 'none'
                        });
                    }
                });
            } catch (error) {
                console.error('支付失败', error);
                uni.hideLoading();
                uni.showToast({
                    title: '网络异常，请稍后重试',
                    icon: 'none'
                });
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.recharge-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);
    padding: 30rpx;
}

.section-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 30rpx;
}

.amount-selection {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    
    .amount-item {
        width: 31%;
        height: 160rpx;
        background-color: #fff;
        margin-bottom: 20rpx;
        border-radius: 12rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
        border: 2rpx solid transparent;
        
        &.active {
            border-color: #8966ef;
            background-color: rgba(137, 102, 239, 0.05);
        }
        
        .amount {
            font-size: 36rpx;
            font-weight: bold;
            color: #333;
        }
        
        .desc {
            margin-top: 8rpx;
            font-size: 24rpx;
            color: #999;
        }
    }
}

.btn-area {
    margin: 50rpx 0;
    
    .recharge-btn {
        height: 90rpx;
        line-height: 90rpx;
        background: linear-gradient(135deg, #8966ef, #a584f2);
        color: #fff;
        font-size: 32rpx;
        border-radius: 45rpx;
        
        &:disabled {
            opacity: 0.6;
        }
    }
}

.recharge-tips {
    padding: 30rpx;
    background-color: #fff;
    border-radius: 12rpx;
    
    .tips-title {
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 20rpx;
    }
    
    .tips-item {
        font-size: 26rpx;
        color: #666;
        margin-bottom: 10rpx;
        
        &:last-child {
            margin-bottom: 0;
        }
    }
}
</style> 