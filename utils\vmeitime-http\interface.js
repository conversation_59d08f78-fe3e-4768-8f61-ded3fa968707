import store from '../../store/index';
const install = (Vue, vm) => {
    // 此为自定义配置参数，具体参数见上方说明
    Vue.prototype.$u.http.setConfig({
        // baseUrl: 'https://yzk.wdaoyun.cn' + '/app/api',
        baseUrl: 'https://friend.wdaoyun.cn' + '/app/api',
        loadingText: 'Loading~', // 请求loading中的文字提示
        //originalData: true,
        // 配置请求头信息
        showLoading: false,
        loadingTime: 800,
        header: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        timeout: 3000,
    });

    // 请求拦截，配置Token等参数
    Vue.prototype.$u.http.interceptor.request = config => {
        // 配置请求中不显示loading
        let notLoadings = [
            '?c=device&a=getDeviceInfo',
            '?c=device&a=insertDeviceError',
            '?c=jiating&a=checkPassword',
            '?c=device&a=getDeviceLogList',
            '?c=user&a=getuser',
            '?c=device&a=getDeviceApply',
            '?c=device&a=publish_video',
            '?c=device&a=addDeviceLog',
            '?c=device&a=checkDevice',
        ];
        vm.$u.http.setConfig({
            // showLoading: notLoadings.indexOf(config.url) < 0,
            showLoading: false,
        });
        // console.log(config)
        return config;
    };

    // 响应拦截，判断状态码是否通过
    Vue.prototype.$u.http.interceptor.response = res => {
        if (res.status == 0 || res.code == 0) {
            // res为服务端返回值，可能有code，result等字段
            // 这里对res.result进行返回，将会在this.$u.post(url).then(res => {})的then回调中的res的到
            // 如果配置了originalData为true，请留意这里的返回值
            return res;
        } else if (res.status == 25 || res.status == 99 || res.status == 999 || res.status == 9999) {
            // store.commit('setHasLogin', false);
            // store.commit('updateUserInfo', {});
            // uni.removeStorageSync('userInfo');
            // uni.removeStorageSync('token');
            vm.$u.toast(res.msg, 3000);
            // console.log(res) 
            return false;
        } else if (res.status == 1000) {
            // 假设201为token失效，这里跳转登录
            // vm.$u.toast('验证失败，请重新登录', 3000);
            store.state.hasLogin = false;
            // vm.$u.toast(res.msg, 3000);
            return false;
        } else if (res.code == -1) {
            vm.$u.toast(res.msg, 3000);
            return false;
        } else {
            vm.$u.toast(res.msg, 3000);
            // 如果返回false，则会调用Promise的reject回调，
            // 并将进入this.$u.post(url).then().catch(res=>{})的catch回调中，res为服务端的返回值
            return false;
        }
    };
};

export default {
    install,
};
