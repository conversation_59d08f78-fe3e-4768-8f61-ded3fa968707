<script>
export default {
    onLaunch: function (option) {
        console.log('App Launch');
        // uni.openBluetoothAdapter({});
        // #ifdef MP-WEIXIN
        let accountInfo = wx.getAccountInfoSync();
        let version = accountInfo.miniProgram.version;
        uni.setStorageSync('version', version);
        uni.setStorageSync('env', __wxConfig.envVersion);

        // #endif
        uni.getSystemInfo({
            success: res => {
                this.$store.commit('setSystemInfo', res);
                uni.setStorageSync('isIos', res.platform.toLowerCase().indexOf('ios') > -1 ? true : false);
            },
        });
        // uni.setStorageSync('token', '9HiPkRvZxRmOc2Uouvw');
        const info = uni.getStorageSync('userInfo');
        this.$store.commit('setHasLogin', true);
        this.$store.commit('updateUserInfo', info);

        if (typeof info.uid != 'undefined') {
            this.$store.commit('updateUserInfo', info);
            this.$store.commit('setHasLogin', true);
            this.$store.commit('setValidMember', true);
        } else {
            this.clearStorage();
        }
        // #ifdef MP-WEIXIN
        if (option.scene != 1154) {
            const updateManager = wx.getUpdateManager();
            updateManager.onCheckForUpdate(function (res) {
                // 请求完新版本信息的回调
                console.log('hasNewVersion--', res.hasUpdate);
            });
            updateManager.onUpdateReady(function () {
                wx.showModal({
                    title: '更新提示',
                    content: '新版本已经准备好，是否重启应用？',
                    showCancel: false,
                    success(res) {
                        if (res.confirm) {
                            updateManager.applyUpdate();
                        }
                    },
                });
            });
        }
        // #endif
    },
    onShow: function () {},
    onHide: function () {},
    methods: {
        clearStorage() {
            uni.removeStorageSync('token');
            uni.removeStorageSync('userInfo');
            this.$store.commit('setHasLogin', false);
            this.$store.commit('updateUserInfo', {});
        },
    },
};
</script>

<style lang="scss">
/*每个页面公共css */
@import 'uview-ui/index.scss';
@import 'static/index.scss';
/* 在线链接服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
@font-face {
  font-family: 'iconfont';  /* Project id 3197628 */
  src: url('https://at.alicdn.com/t/c/font_3197628_akf9uowaib5.woff2?t=1751599631814') format('woff2'),
       url('https://at.alicdn.com/t/c/font_3197628_akf9uowaib5.woff?t=1751599631814') format('woff'),
       url('https://at.alicdn.com/t/c/font_3197628_akf9uowaib5.ttf?t=1751599631814') format('truetype');
}
.iconfont {
    font-family: iconfont !important;
    font-size: 28rpx;
    font-style: normal;
    display: inline-block;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.content {
    overflow-x: hidden;
    min-height: 100vh;
}
// 浮动动画
@keyframes float {
    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20rpx) rotate(180deg);
    }
}
</style>
