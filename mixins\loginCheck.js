/**
 * 登录检查混入
 * 提供统一的登录状态检查和登录弹窗显示功能
 */

import { mapState } from 'vuex';

export default {
    computed: {
        ...mapState(['hasLogin'])
    },
    
    data() {
        return {
            showLoginPopup: false
        };
    },
    
    methods: {
        /**
         * 检查登录状态
         * @param {Function} callback - 登录成功后的回调函数
         * @param {Object} options - 配置选项
         * @param {boolean} options.showPopup - 是否显示登录弹窗，默认true
         * @param {string} options.message - 自定义提示消息
         * @returns {boolean} 是否已登录
         */
        checkLogin(callback = null, options = {}) {
            const { showPopup = true, message = '请先登录' } = options;
            
            if (!this.hasLogin) {
                if (showPopup) {
                    this.showLoginPopup = true;
                } else {
                    uni.showToast({
                        title: message,
                        icon: 'none',
                        duration: 2000
                    });
                }
                return false;
            }
            
            // 如果已登录且有回调函数，执行回调
            if (callback && typeof callback === 'function') {
                callback();
            }
            
            return true;
        },
        
        /**
         * 需要登录的操作包装器
         * @param {Function} action - 需要登录才能执行的操作
         * @param {Object} options - 配置选项
         */
        requireLogin(action, options = {}) {
            this.checkLogin(action, options);
        },
        
        /**
         * 处理登录弹窗关闭
         */
        handleLoginPopupClose() {
            this.showLoginPopup = false;
        },
        
        /**
         * 处理登录操作
         */
        handleLoginAction() {
            console.log('用户点击了登录按钮');
            // 可以在这里添加额外的登录逻辑
        },
        
        /**
         * 快速导航到登录页面
         */
        goToLogin() {
            uni.navigateTo({
                url: '/pages/index/login'
            });
        }
    }
};
