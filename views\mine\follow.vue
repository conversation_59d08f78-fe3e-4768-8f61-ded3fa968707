<template>
    <view class="follow-page">
        <!-- 关注列表 -->
        <view class="follow-list" v-if="followList.length > 0">
            <view class="follow-item" v-for="(item, index) in followList" :key="index">
                <view class="user-info" @click="goToUserProfile(item.to_user.uid)">
                    <view class="user-avatar">
                        <image :src="item.to_user.headimgurl || 'http://static.wdaoyun.com/wdy/source/2023-10-18/132.jpg'" mode="aspectFill"></image>
                    </view>
                    <view class="user-detail">
                        <view class="user-name">{{ item.to_user.nickname || '微信用户' }}</view>
                        <view class="follow-time">你在{{ formatTime(item.addtime) }}关注了TA</view>
                    </view>
                </view>
                <view class="follow-action">
                    <u-button v-if="item.follow_type == 2" type="primary" size="mini" :custom-style="followedButtonStyle" @click="handleUnfollow(item, index)">取消关注</u-button>
                </view>
            </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="!loading && followList.length === 0">
            <view class="empty-content">
                <image class="empty-image" src="http://static.wdaoyun.com/wdy/source/2023-12-06/2023120617034.png" mode="aspectFit"></image>
                <view class="empty-text">暂无关注用户</view>
                <view class="empty-tip">快去关注感兴趣的用户吧~</view>
            </view>
        </view>

        <!-- 统一的加载更多组件 -->
        <u-loadmore :status="loadStatus" :load-text="loadText" @loadmore="loadMore" v-if="followList.length > 0"></u-loadmore>
    </view>
</template>

<script>
import { mapState } from 'vuex';

export default {
    name: 'FollowList',
    data() {
        return {
            followList: [],
            loading: false,
            finished: false,
            page: 1,
            pagesize: 10,
            total: 0,
            loadStatus: 'loadmore',
            loadText: {
                loadmore: '点击或上拉加载更多',
                loading: '正在加载...',
                nomore: '没有更多数据了',
            },
            followButtonStyle: {
                background: '#8966ef',
                borderColor: '#8966ef',
                color: '#fff',
            },
            followedButtonStyle: {
                background: '#f0f0f0',
                borderColor: '#f0f0f0',
                color: '#666',
            },
        };
    },
    computed: {
        ...mapState(['hasLogin']),
    },
    onLoad() {
        this.getFollowList();
    },
    onPullDownRefresh() {
        this.refreshData();
    },
    onReachBottom() {
        this.loadMore();
    },
    methods: {
        // 获取关注列表
        async getFollowList(isLoadMore = false) {
            if (this.loading) return;

            this.loading = true;
            this.loadStatus = 'loading';

            try {
                const res = await this.$api.getFollowList({
                    page: this.page,
                    pagesize: this.pagesize,
                    access_token: uni.getStorageSync('token'),
                });

                if (res.status === 0) {
                    const list = res.data.list || [];
                    this.total = Number(res.data.total || 0);

                    if (isLoadMore) {
                        this.followList = [...this.followList, ...list];
                    } else {
                        this.followList = list;
                    }

                    // 判断是否加载完毕
                    if (list.length === 0 || list.length < this.pagesize || this.followList.length >= this.total) {
                        this.finished = true;
                        this.loadStatus = 'nomore';
                    } else {
                        this.finished = false;
                        this.loadStatus = 'loadmore';
                    }
                } else {
                    uni.showToast({
                        title: res.msg || '获取数据失败',
                        icon: 'none',
                    });
                }
            } catch (error) {
                console.error('获取关注列表失败:', error);
                uni.showToast({
                    title: '获取数据失败',
                    icon: 'none',
                });

                if (isLoadMore && this.page > 1) {
                    this.page--;
                }
            } finally {
                this.loading = false;
                uni.stopPullDownRefresh();
            }
        },

        // 加载更多
        loadMore() {
            if (this.finished || this.loading) return;

            this.page++;
            this.getFollowList(true);
        },

        // 下拉刷新
        refreshData() {
            this.page = 1;
            this.finished = false;
            this.getFollowList();
        },

        // 取消关注
        async handleUnfollow(item, index) {
            try {
                const res = await this.$api.followUser({
                    follow_user_id: item.to_user.uid,
                    access_token: uni.getStorageSync('token'),
                });

                this.followList.splice(index, 1);
                this.total--;
                uni.showToast({
                    title: '已取消关注',
                    icon: 'success',
                });
            } catch (error) {
                console.error('取消关注失败:', error);
                uni.showToast({
                    title: '操作失败',
                    icon: 'none',
                });
            }
        },

        // 跳转到用户详情
        goToUserProfile(uid) {
            uni.navigateTo({
                url: `/views/mine/userProfile?uid=${uid}`,
            });
        },

        // 返回上一页
        goBack() {
            uni.navigateBack();
        },

        // 格式化时间
        formatTime(time) {
            if (!time) return '';
            const date = new Date(time);
            const now = new Date();
            const diff = now.getTime() - date.getTime();

            if (diff < 60000) {
                return '刚刚';
            } else if (diff < 3600000) {
                return Math.floor(diff / 60000) + '分钟前';
            } else if (diff < 86400000) {
                return Math.floor(diff / 3600000) + '小时前';
            } else {
                return Math.floor(diff / 86400000) + '天前';
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.follow-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);
}

.nav-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 30rpx;
    background-color: #fff;
    border-bottom: 1rpx solid #eee;

    .nav-back,
    .nav-placeholder {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .nav-title {
        font-size: 36rpx;
        font-weight: 600;
        color: #333;
    }
}

.follow-list {
    padding: 20rpx 30rpx;
}

.follow-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    background-color: #fff;
    &:last-child {
        border-bottom: none;
    }
}

.user-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.user-avatar {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 24rpx;

    image {
        width: 100%;
        height: 100%;
    }
}

.user-detail {
    flex: 1;
}

.user-name {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
}

.user-desc {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 8rpx;
}

.follow-time {
    font-size: 24rpx;
    color: #999;
}

.follow-action {
    margin-left: 20rpx;
}

.empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
}

.empty-content {
    text-align: center;
}

.empty-image {
    width: 300rpx;
    height: 300rpx;
    margin-bottom: 40rpx;
}

.empty-text {
    font-size: 32rpx;
    color: #666;
    margin-bottom: 16rpx;
}

.empty-tip {
    font-size: 28rpx;
    color: #999;
}

.load-more {
    text-align: center;
    padding: 30rpx;
    font-size: 28rpx;
    color: #999;
}
</style>
