<template>
    <view class="wallet-container">
        <!-- 顶部余额卡片 -->
        <view class="balance-card">
            <view class="balance-info">
                <view class="balance-title">{{ type === 1 ? '我的余额' : '我的打赏' }}</view>
                <view class="balance-amount">
                    <!-- <text class="currency">¥</text> -->
                    <text class="amount">{{ type === 1 ? balanceInfo.balance || '0.00' : balanceInfo.ds_balance || '0.00' }}</text>
                </view>
            </view>
            <view class="balance-actions">
                <button v-if="type === 1" class="action-btn recharge-btn" @click="goToRecharge">充值</button>
                <button v-if="type === 2" class="action-btn withdraw-btn" @click="goToWithdraw">提现</button>
            </view>
        </view>

        <!-- 交易记录 -->
        <view class="transaction-section">
            <view class="section-header">
                <text class="section-title">{{ type === 1 ? '余额记录' : '打赏记录' }}</text>
            </view>

            <view class="transaction-list" v-if="transactions.length > 0">
                <view class="transaction-item" v-for="(item, index) in transactions" :key="index">
                    <view class="transaction-info">
                        <view class="transaction-title">{{ item.remark }}</view>
                        <view class="transaction-time">{{ item.addtime }}</view>
                    </view>
                    <!-- ¥ -->
                    <view class="transaction-amount" :class="item.type === '1' ? 'expense' : 'income'">{{ item.type === '1' ? '+' : '-' }}{{ item.amount }}</view>
                </view>
            </view>

            <view class="empty-state" v-else>
                <u-empty mode="data" :text="type === 1 ? '暂无余额记录' : '暂无打赏记录'"></u-empty>
            </view>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';

export default {
    data() {
        return {
            balanceInfo: {
                balance: '0.00',
                ds_balance: '0.00',
            },
            transactions: [],
            page: 1,
            pagesize: 10,
            loading: false,
            finished: false,
            type: 1, // 1余额 2打赏
        };
    },
    computed: {
        ...mapState(['hasLogin', 'userInfo']),
    },
    onLoad(options) {
        if (options.type) {
            this.type = parseInt(options.type);
        }
        this.init();
        uni.$on('refreshBalance', () => {
            this.init();
        });
    },
    onPullDownRefresh() {
        this.init();
        setTimeout(() => {
            uni.stopPullDownRefresh();
        }, 1000);
    },
    onReachBottom() {
        if (!this.loading && !this.finished) {
            this.page++;
            this.loadTransactionList();
        }
    },
    methods: {
        init() {
            this.page = 1;
            this.finished = false;
            this.loadBalanceInfo();
            this.loadTransactionList();
        },
        // 加载余额信息
        async loadBalanceInfo() {
            if (!this.hasLogin) {
                return;
            }

            try {
                const res = await this.$api.getUserWallet({
                    access_token: uni.getStorageSync('token'),
                });

                if (res.status === 0 && res.data && res.data.balance) {
                    this.balanceInfo = res.data.balance;
                } else {
                    uni.showToast({
                        title: res.msg || '获取余额失败',
                        icon: 'none',
                    });
                }
            } catch (error) {
                console.error('获取余额失败', error);
                uni.showToast({
                    title: '网络异常，请稍后重试',
                    icon: 'none',
                });
            }
        },

        // 加载交易记录
        async loadTransactionList() {
            if (!this.hasLogin || this.loading || this.finished) {
                return;
            }

            this.loading = true;

            try {
                const res = await this.$api.getUserWalletList({
                    access_token: uni.getStorageSync('token'),
                    page: this.page,
                    pagesize: this.pagesize,
                    type: this.type, // 1余额 2打赏
                });

                if (res.status === 0) {
                    const list = res.data.list || [];

                    if (this.page === 1) {
                        this.transactions = list;
                    } else {
                        this.transactions = [...this.transactions, ...list];
                    }

                    // 判断是否加载完毕
                    if (list.length < this.pagesize) {
                        this.finished = true;
                    }
                } else {
                    uni.showToast({
                        title: res.msg || '获取记录失败',
                        icon: 'none',
                    });
                }
            } catch (error) {
                console.error('获取记录失败', error);
                uni.showToast({
                    title: '网络异常，请稍后重试',
                    icon: 'none',
                });
            } finally {
                this.loading = false;
            }
        },

        // 前往充值页面
        goToRecharge() {
            if (!this.hasLogin) {
                uni.showToast({
                    title: '请先登录',
                    icon: 'none',
                });
                return;
            }

            uni.navigateTo({
                url: '/views/wallet/recharge',
            });
        },

        // 前往提现页面
        goToWithdraw() {
            if (!this.hasLogin) {
                uni.showToast({
                    title: '请先登录',
                    icon: 'none',
                });
                return;
            }

            // 检查是否有可提现余额
            // const dsBalance = parseFloat(this.balanceInfo.ds_balance || '0');
            // if (dsBalance < 1) {
            //     uni.showToast({
            //         title: '可提现余额不足1元',
            //         icon: 'none',
            //     });
            //     return;
            // }

            uni.navigateTo({
                url: '/views/wallet/withdraw',
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.wallet-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);
    padding-bottom: 30rpx;
}

.balance-card {
    background: linear-gradient(135deg, #8966ef, #a584f2);
    padding: 40rpx 30rpx;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .balance-info {
        flex: 1;

        .balance-title {
            font-size: 28rpx;
            opacity: 0.9;
            margin-bottom: 10rpx;
        }

        .balance-amount {
            display: flex;
            align-items: baseline;

            .currency {
                font-size: 36rpx;
                margin-right: 8rpx;
            }

            .amount {
                font-size: 60rpx;
                font-weight: bold;
            }
        }
    }

    .balance-actions {
        flex-shrink: 0;

        .action-btn {
            width: 120rpx;
            height: 60rpx;
            line-height: 60rpx;
            font-size: 26rpx;
            font-weight: 600;
            border-radius: 30rpx;
            text-align: center;
            border: none;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

            // 添加光泽效果
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
                transition: left 0.5s ease;
            }

            &:active {
                transform: translateY(1rpx);
                box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);

                &::before {
                    left: 100%;
                }
            }

            &.recharge-btn {
                background: linear-gradient(135deg, #fff, #f8f9fa);
                color: #8966ef;
                border: 2rpx solid rgba(137, 102, 239, 0.2);
                box-shadow: 0 4rpx 12rpx rgba(137, 102, 239, 0.15);

                &:active {
                    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                    box-shadow: 0 2rpx 8rpx rgba(137, 102, 239, 0.2);
                }
            }

            &.withdraw-btn {
                background: linear-gradient(135deg, #f0cc6c, #edc353);
                color: #fff;
                text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
                box-shadow: 0 4rpx 12rpx rgba(240, 204, 108, 0.3);

                &:active {
                    background: linear-gradient(135deg, #edc353, #e6b84a);
                    box-shadow: 0 2rpx 8rpx rgba(240, 204, 108, 0.4);
                }
            }
        }
    }
}

.transaction-section {
    margin-top: 20rpx;
    padding: 0 30rpx;
    background-color: #fff;
    border-radius: 12rpx;

    .section-header {
        padding: 30rpx 0;
        border-bottom: 1px solid #f0f0f0;

        .section-title {
            font-size: 32rpx;
            font-weight: 500;
            color: #333;
        }
    }

    .transaction-list {
        .transaction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 30rpx 0;
            border-bottom: 1px solid #f5f5f5;

            &:last-child {
                border-bottom: none;
            }

            .transaction-info {
                flex: 1;

                .transaction-title {
                    font-size: 28rpx;
                    color: #333;
                    margin-bottom: 10rpx;
                }

                .transaction-time {
                    font-size: 24rpx;
                    color: #999;
                }
            }

            .transaction-amount {
                font-size: 32rpx;
                font-weight: 500;

                &.income {
                    color: #07c160;
                }

                &.expense {
                    color: #fa5151;
                }
            }
        }
    }

    .empty-state {
        padding: 60rpx 0;
    }
}
</style>
