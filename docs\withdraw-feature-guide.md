# 提现功能使用指南

## 功能概述

提现功能允许用户将打赏收入提现到微信零钱，使用微信小程序的 `wx.requestMerchantTransfer` 接口实现企业付款到零钱功能。

## ✅ 已完成功能

### 1. 页面功能完成
- ✅ 新建 `views/wallet/withdraw.vue` 提现页面
- ✅ 新建 `views/wallet/withdraw-record.vue` 提现记录页面
- ✅ 更新 `views/wallet/index.vue` 钱包页面，添加提现按钮
- ✅ 在 `pages.json` 中注册新页面路由

### 2. API接口完成
- ✅ 添加 `withdrawApply` 提现申请接口
- ✅ 添加 `getWithdrawRecord` 提现记录接口
- ✅ 集成微信小程序企业付款接口

### 3. UI设计完成
- ✅ 与项目整体风格保持一致
- ✅ 使用项目主题色 `#8966ef` 和 `#f0cc6c`
- ✅ 响应式设计，适配不同屏幕尺寸
- ✅ 完善的交互反馈和状态提示

## 核心页面

### 1. 提现页面 (views/wallet/withdraw.vue)

**功能特点：**
- 显示可提现余额（仅打赏收入）
- 支持手动输入提现金额
- 快捷金额选择（10、50、100、200元 + 全部）
- 提现金额验证和限制
- 微信企业付款集成
- 完善的提现说明

**使用方法：**
```javascript
// 跳转到提现页面
uni.navigateTo({
    url: '/views/wallet/withdraw'
});
```

**核心功能：**
- 余额检查：只能提现打赏收入，不能提现充值余额
- 金额限制：单次提现最低1元，最高5000元
- 微信支付：使用 `wx.requestMerchantTransfer` 实现企业付款
- 实时验证：输入金额时实时检查是否超出余额

### 2. 提现记录页面 (views/wallet/withdraw-record.vue)

**功能特点：**
- 显示累计提现金额和次数统计
- 提现记录列表（分页加载）
- 提现状态显示（处理中、已到账、失败）
- 下拉刷新和上拉加载更多

**状态说明：**
- `0` - 处理中（黄色）
- `1` - 已到账（绿色）
- `2` - 失败（红色）

### 3. 钱包页面更新 (views/wallet/index.vue)

**新增功能：**
- 在打赏余额页面（type=2）添加提现按钮
- 提现前检查余额是否满足最低提现金额
- 与充值按钮样式保持一致

## API接口说明

### 1. 提现申请接口
```javascript
// 接口：withdrawApply
// 文件：utils/vmeitime-http/order.js
const res = await this.$api.withdrawApply({
    amount: 100.00,              // 提现金额
    access_token: uni.getStorageSync('token')
});
```

### 2. 提现记录接口
```javascript
// 接口：getWithdrawRecord
// 文件：utils/vmeitime-http/order.js
const res = await this.$api.getWithdrawRecord({
    page: 1,                     // 页码
    pagesize: 10,                // 每页条数
    access_token: uni.getStorageSync('token')
});
```

### 3. 微信企业付款
```javascript
// 微信小程序企业付款接口
wx.requestMerchantTransfer({
    openid: this.userInfo.openid,           // 用户openid
    amount: Math.round(amount * 100),       // 金额（分）
    desc: '跃汇街提现',                       // 付款描述
    success: (res) => {
        // 付款成功处理
    },
    fail: (err) => {
        // 付款失败处理
    }
});
```

## 使用流程

### 1. 提现流程
1. 用户在钱包的"我的打赏"页面点击"提现"按钮
2. 系统检查可提现余额是否满足最低金额要求
3. 跳转到提现页面，显示可提现余额
4. 用户选择或输入提现金额
5. 系统验证金额是否合法
6. 用户确认提现申请
7. 调用后端提现申请接口
8. 调用微信企业付款接口
9. 显示提现结果并更新余额

### 2. 记录查看流程
1. 在提现页面点击"查看提现记录"
2. 显示提现统计信息和记录列表
3. 支持下拉刷新和上拉加载更多
4. 显示每笔提现的状态和时间

## 配置说明

### 提现限制配置
在 `views/wallet/withdraw.vue` 中可以配置：

```javascript
data() {
    return {
        minWithdrawAmount: 1,        // 最低提现金额
        maxWithdrawAmount: 5000,     // 最高提现金额
        quickAmounts: [10, 50, 100, 200]  // 快捷金额选项
    };
}
```

### 主题色配置
提现功能使用项目统一主题色：
- **主色**: `#8966ef` - 用于按钮、强调元素
- **辅助色**: `#f0cc6c` - 用于提现按钮边框和特殊标识

## 安全考虑

1. **权限验证**: 所有接口都需要用户登录token
2. **金额验证**: 前端和后端双重验证提现金额
3. **余额检查**: 只能提现打赏收入，不能提现充值余额
4. **频次限制**: 每日提现次数限制（可在后端配置）
5. **状态跟踪**: 完整的提现状态跟踪和记录

## 注意事项

1. **微信企业付款**: 需要在微信商户平台开通企业付款功能
2. **openid获取**: 确保用户信息中包含正确的openid
3. **金额单位**: 微信接口使用分为单位，需要转换
4. **错误处理**: 完善的网络异常和支付失败处理
5. **用户体验**: 提供清晰的状态反馈和操作指引

## 扩展功能

1. **提现手续费**: 可以在后端配置提现手续费率
2. **银行卡提现**: 支持提现到银行卡（需要额外开发）
3. **提现审核**: 添加人工审核流程
4. **提现统计**: 更详细的提现数据统计和分析

---

**更新时间**: 2025-07-10  
**功能状态**: 已完成开发，可直接使用
