import Vue from 'vue';
import Vuex from 'vuex';
import util from '../utils/utils.js';
// import { imagePaths } from '../imagePaths';
import { getUserInfo, getphone } from '../utils/vmeitime-http/user.js';
// import chatModule from './modules/chat';

Vue.use(Vuex);

const store = new Vuex.Store({
    modules: {
        // chat: chatModule
    },
    state: {
        skeletonLoading: true, //骨架屏开启
        openid: 0,
        hasLogin: false,
        userInfo: '',
        familyId: '',
        deviceData: {},
        deviceAuth: {},
        shareData: {
            // 卡片分享信息
            inventoryId: '',
            shareUid: '',
            type: '',
        },
        systemInfo: {},
        capsule: {},
        vx_avoid: 0,
        appType: 1, // APP、小程序分类
        showGzh: false,
        isGuide: uni.getStorageSync('isGuide') || false, // 是否展示导航
        inputStyle: {
            color: '#333333',
            fontSize: '32rpx',
        },
        callStatus: undefined, // 四博主动呼叫
        callRotate: 0, // 四博旋转角度
        lockImg: '',
        // 文字大小
        page_font_size: uni.getStorageSync('page_font_size') || 'page_font_size',
        // 导航栏高度
        StatusBar: {
            statusBar: 0,
            customBar: 0,
        },
        apiHost: 'https://gaoshi.wdaoyun.cn',
        videoStatus: false,
        swiperVisibality: false,
        devUpdateInfo: {},
        isValidMember: false,
    },

    mutations: {
        setSkeletonLoading(state, provider) {
            setTimeout(() => {
                state.skeletonLoading = provider;
            }, 100);
        },
        setDevUpdateInfo(state, provider) {
            state.devUpdateInfo = provider;
        },
        setVideoStatus(state, provider) {
            state.videoStatus = provider;
        },

        //更新登录状态
        setHasLogin(state, provider) {
            state.hasLogin = provider;
        },
        // 设置导航栏高度
        SET_STATUSBAR(state, value) {
            state.StatusBar = value;
        },
        SET_page_font_size(state, value) {
            state.page_font_size = value;
            uni.setStorage({
                key: 'page_font_size',
                data: value,
                success: function () {},
            });
        },
        //更新指引状态
        setGuide(state, provider) {
            state.isGuide = provider;
        },
        setSwiperVisibal(state, provider) {
            state.swiperVisibality = provider;
            uni.setStorageSync('swiperVisibality', provider);
        },
        setValidMember(state, provider) {
            if (!state.userInfo || !state.userInfo.member_time) {
                state.isValidMember = false;
            }

            // 如果member_time为0或空字符串，表示非会员
            if (state.userInfo.member_time === '0' || state.userInfo.member_time === 0 || state.userInfo.member_time === '') {
                state.isValidMember = false;
            }

            // 将member_time转换为时间戳进行比较
            let memberExpireTime;

            // 如果member_time是时间戳格式（数字）
            if (typeof state.userInfo.member_time === 'number' || /^\d+$/.test(state.userInfo.member_time)) {
                memberExpireTime = parseInt(state.userInfo.member_time) * 1000; // 转换为毫秒
            }
            // 如果member_time是日期字符串格式
            else if (typeof state.userInfo.member_time === 'string') {
                memberExpireTime = new Date(state.userInfo.member_time.replace(/-/g, '/')).getTime();
            } else {
                state.isValidMember = false;
            }

            // 比较当前时间和会员到期时间
            const currentTime = new Date().getTime();
            state.isValidMember = memberExpireTime > currentTime;
        },
        // 设置四博主动呼叫状态
        setCallStatus(state, provider) {
            state.callStatus = provider;
        },
        // 设置四博图像旋转状态状态
        setCallRotate(state, provider) {
            uni.setStorageSync('callRotate', provider);
            state.callRotate = provider;
        },
        //更新用户信息，写入信息
        updateUserInfo(state, provider) {
            state.userInfo = provider;
            uni.setStorageSync('userInfo', provider);
            if (provider.fwh_openid == '') {
                state.showGzh = true;
            } else {
                state.showGzh = false;
            }
        },

        //更新用户审核状态
        setUserIdentity(state, provider) {
            state.userIdentity = provider;
            uni.setStorageSync('userIdentity', provider);
        },

        setCartNum(state, provider) {
            state.cartNum = provider;
        },

        // 设置搜索值
        setSearchVal(state, provider) {
            state.searchVal = provider;
        },

        // 设置搜索记录
        setSearch(state, provider) {
            if (!provider) {
                uni.removeStorageSync('search');
                state.searchList = [];
                return;
            }
            state.searchList = provider;
        },

        // 更新搜索记录
        updateSearch(state, provider) {
            var has = false;
            var index = 0;
            state.searchList.map((item, i) => {
                if (item == provider) {
                    has = true;
                    index = i;
                }
            });
            if (has) {
                state.searchList.splice(index, 1);
                state.searchList.unshift(provider);
            } else {
                if (state.searchList.length <= 15) {
                    state.searchList.unshift(provider);
                } else {
                    state.searchList.splice(state.searchList.length - 1, 1);
                    state.searchList.unshift(provider);
                }
            }

            uni.setStorageSync('search', state.searchList);
        },
        setFamilyId(state, provider) {
            state.familyId = provider;
        },
        setShareData(state, provider) {
            let shareData = JSON.parse(provider);
            if (shareData.shareUid != state.userInfo.uid) {
                state.shareData = shareData;
            }
        },
        setSystemInfo(state, provider) {
            state.systemInfo = provider;
        },
        setCapsule(state, provider) {
            state.capsule = provider;
        },
        setDeviceData(state, provider) {
            state.deviceData = provider;
        },
        setDeviceAuth(state, provider) {
            state.deviceAuth = provider;
        },
        setAvoid(state, provider) {
            state.vx_avoid = provider;
        },
        //退出登录
        logout(state) {
            state.hasLogin = false;
            state.userInfo = {};
            uni.removeStorage({
                key: 'userInfo',
            });
            uni.reLaunch({
                url: '/pages/index/index',
            });
        },
    },

    actions: {
        Logout({ commit }) {
            return new Promise(resolve => {
                uni.clearStorage();
                commit('logout', {});
                resolve();
            });
        },

        // 获取用户信息并更新
        VAgetUser({ commit }, payload) {
            return new Promise((resolve, reject) => {
                getUserInfo({
                    access_token: uni.getStorageSync('token'),
                })
                    .then(res => {
                        commit('updateUserInfo', res.data);
                        commit('setHasLogin', true);
                        commit('setValidMember', true);
                        resolve(res);
                    })
                    .catch(err => {
                        uni.showToast({
                            title: err.message,
                            icon: 'none',
                        });
                        reject(err);
                    });
            });
        },

        // 授权手机
        VAbindtPhoneNumber({ rootState, commit, dispatch }, payload) {
            return new Promise((resolve, reject) => {
                let e = payload.detail;
                console.log(e, 'e');
                let userInfo = rootState.userInfo;
                if (!e.hasOwnProperty('iv') || !e.hasOwnProperty('encryptedData')) {
                    commit('updateUserInfo', userInfo);
                    resolve(false);
                } else {
                    getphone({
                        encryptedData: e.encryptedData,
                        iv: e.iv,
                        access_token: uni.getStorageSync('token'),
                    }).then(res => {
                        if (res.status == 0) {
                            dispatch('VAgetUser');
                            resolve(true);
                        } else {
                            uni.showToast({
                                title: '授权失败',
                                icon: 'none',
                            });
                            // setTimeout(() => {
                            //     dispatch('Logout');
                            // }, 500);
                            resolve(false);
                        }
                    });
                }
            });
        },
    },
});

export default store;
