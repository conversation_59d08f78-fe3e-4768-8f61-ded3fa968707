{"designSystem": {"name": "GradientGlassmorphism", "description": "A design system inspired by the provided screenshots, featuring a blend of gradients and glassmorphism for a modern and vibrant aesthetic.", "colors": {"primaryGradient": {"start": "#8A2BE2", "end": "#4682B4"}, "secondaryGradient": {"start": "#FF6347", "end": "#FFD700"}, "tertiaryGradient": {"start": "#32CD32", "end": "#00CED1"}, "backgroundGradient": {"start": "#87CEEB", "end": "#BA55D3"}, "textPrimary": "#FFFFFF", "textSecondary": "#E0E0E0", "iconColor": "#FFFFFF", "dividerColor": "rgba(255, 255, 255, 0.2)"}, "typography": {"fontFamily": "San Francisco Pro, system-ui, sans-serif", "heading1": {"fontSize": "28px", "fontWeight": "600", "color": "{colors.textPrimary}"}, "heading2": {"fontSize": "22px", "fontWeight": "500", "color": "{colors.textPrimary}"}, "bodyText": {"fontSize": "16px", "fontWeight": "400", "color": "{colors.textSecondary}"}, "smallText": {"fontSize": "14px", "fontWeight": "300", "color": "{colors.textSecondary}"}}, "components": {"card": {"backgroundColor": "rgba(255, 255, 255, 0.15)", "borderRadius": "20px", "padding": "15px", "backdropFilter": "blur(10px)", "border": "1px solid rgba(255, 255, 255, 0.2)"}, "button": {"default": {"backgroundColor": "{colors.primaryGradient}", "borderRadius": "15px", "padding": "12px 20px", "color": "{colors.textPrimary}", "fontSize": "16px", "fontWeight": "500", "boxShadow": "0 4px 10px rgba(0, 0, 0, 0.2)"}, "iconButton": {"backgroundColor": "rgba(255, 255, 255, 0.1)", "borderRadius": "50%", "width": "48px", "height": "48px", "display": "flex", "alignItems": "center", "justifyContent": "center", "backdropFilter": "blur(8px)", "border": "1px solid rgba(255, 255, 255, 0.1)"}, "circularAction": {"backgroundColor": "linear-gradient(135deg, #FF69B4, #8A2BE2)", "borderRadius": "50%", "width": "60px", "height": "60px", "display": "flex", "alignItems": "center", "justifyContent": "center", "boxShadow": "0 6px 15px rgba(0, 0, 0, 0.3)"}}, "navigationBar": {"backgroundColor": "rgba(255, 255, 255, 0.1)", "borderRadius": "30px", "padding": "10px 20px", "display": "flex", "justifyContent": "space-around", "alignItems": "center", "backdropFilter": "blur(15px)", "border": "1px solid rgba(255, 255, 255, 0.2)", "iconSize": "24px", "activeIconColor": "{colors.textPrimary}", "inactiveIconColor": "rgba(255, 255, 255, 0.6)"}, "progressBar": {"height": "8px", "borderRadius": "4px", "trackColor": "rgba(255, 255, 255, 0.3)", "fillColor": "linear-gradient(90deg, {colors.primaryGradient.start}, {colors.primaryGradient.end})", "segmentColors": {"segment1": "linear-gradient(90deg, #6A5ACD, #4682B4)", "segment2": "linear-gradient(90deg, #FF6347, #FFD700)", "segment3": "linear-gradient(90deg, #32CD32, #00CED1)"}}, "profileAvatar": {"size": "50px", "borderRadius": "50%", "backgroundColor": "rgba(255, 255, 255, 0.2)", "border": "2px solid rgba(255, 255, 255, 0.6)"}, "listItem": {"backgroundColor": "transparent", "padding": "10px 0", "borderBottom": "1px solid {colors.dividerColor}"}, "toggleSwitch": {"trackColor": "rgba(255, 255, 255, 0.3)", "thumbColor": "{colors.textPrimary}", "activeTrackColor": "{colors.primaryGradient}"}, "soundWaveDisplay": {"lineColor": "rgba(255, 255, 255, 0.8)", "lineWidth": "2px", "gradientFill": "linear-gradient(to right, rgba(138, 43, 226, 0.4), rgba(70, 130, 180, 0.4))"}}, "effects": {"glassmorphism": {"backdropFilter": "blur(15px)", "backgroundColor": "rgba(255, 255, 255, 0.15)", "border": "1px solid rgba(255, 255, 255, 0.2)", "boxShadow": "0 4px 20px rgba(0, 0, 0, 0.1)"}, "shadows": {"small": "0 2px 5px rgba(0, 0, 0, 0.1)", "medium": "0 8px 15px rgba(0, 0, 0, 0.2)", "large": "0 12px 25px rgba(0, 0, 0, 0.3)"}}, "spacing": {"small": "8px", "medium": "16px", "large": "24px", "xLarge": "32px"}}}