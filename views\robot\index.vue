<template>
    <view class="robot-container">
        <!-- 页面内容 -->
        <view class="page-content">
            <!-- 机器人状态卡片 -->
            <view class="status-card">
                <view class="robot-avatar">
                    <!-- <u-icon name="robot" size="60" color="#8966ef"></u-icon> -->
                    <image src="https://static.wdaoyun.com/friend/2025-07-03/cbae8210920f7bc7f5e311f5fd2f3287.png" mode="widthFix"></image>
                </view>
                <view class="status-info">
                    <view class="status-title">智能回复机器人</view>
                    <view class="status-desc">{{ robotData.status ? '已开启自动回复' : '自动回复已关闭' }}</view>
                    <view class="status-badge" :class="{ active: robotData.status }">
                        <view class="status-dot"></view>
                        <text>{{ robotData.status ? '运行中' : '已停止' }}</text>
                    </view>
                </view>
                <view class="switch-area">
                    <u-switch v-model="robotData.status" active-color="#8966ef" inactive-color="#e0e0e0" size="50" @change="onSwitchChange"></u-switch>
                </view>
            </view>

            <!-- 基本设置区域 -->
            <view class="setting-section">
                <view class="section-header">
                    <u-icon name="setting" size="32" color="#8966ef"></u-icon>
                    <text class="section-title">基本设置</text>
                </view>
                <view class="form-container">
                    <u-form :model="robotData" ref="uForm">
                        <view class="form-item">
                            <view class="form-label">
                                <u-icon name="account" size="28" color="#8966ef"></u-icon>
                                <text>机器人名称</text>
                            </view>
                            <view class="input-wrapper">
                                <u-input v-model="robotData.name" placeholder="请输入机器人名称" :custom-style="inputStyle" />
                            </view>
                        </view>
                        <view class="form-item">
                            <view class="form-label">
                                <u-icon name="man" size="28" color="#8966ef"></u-icon>
                                <text>机器人性别</text>
                            </view>
                            <view class="radio-wrapper">
                                <u-radio-group v-model="robotData.sex" placement="row">
                                    <u-radio :name="1" active-color="#8966ef">男</u-radio>
                                    <u-radio :name="2" active-color="#8966ef">女</u-radio>
                                </u-radio-group>
                            </view>
                        </view>
                        <view class="form-item">
                            <view class="form-label">
                                <u-icon name="clock" size="28" color="#8966ef"></u-icon>
                                <text>回复延迟</text>
                            </view>
                            <view class="input-with-unit">
                                <view class="input-wrapper">
                                    <u-input v-model="robotData.interval" type="number" placeholder="延迟时间" :custom-style="inputStyle" />
                                </view>
                                <text class="unit-text">秒</text>
                            </view>
                        </view>
                    </u-form>
                </view>
            </view>

            <!-- 机器人自定义配置区域 -->
            <view class="config-section">
                <view class="section-header">
                    <u-icon name="edit-pen" size="32" color="#8966ef"></u-icon>
                    <text class="section-title">个性化配置</text>
                </view>
                <view class="config-desc">
                    <u-icon name="info-circle" size="24" color="#f0cc6c"></u-icon>
                    <text>设置机器人的性格、爱好、特长等，让回复更加个性化</text>
                </view>

                <!-- 配置列表 -->
                <view class="config-list">
                    <view class="config-item" v-for="(_, index) in replyOptions" :key="index">
                        <view class="config-input">
                            <view class="input-wrapper">
                                <u-input v-model="replyOptions[index]" placeholder="例如：我喜欢音乐、我的爱好是运动..." :custom-style="inputStyle" />
                            </view>
                        </view>
                        <view class="config-actions" v-if="replyOptions.length > 1">
                            <u-icon name="close-circle" size="32" color="#ff6b6b" @click="removeReplyOption(index)"></u-icon>
                        </view>
                    </view>
                </view>

                <!-- 添加配置按钮 -->
                <view class="add-config-btn" @click="addReplyOption">
                    <u-icon name="plus-circle" size="32" color="#8966ef"></u-icon>
                    <text>添加新的个性配置</text>
                </view>
            </view>

            <!-- 保存按钮 -->
            <view class="save-section">
                <u-button :custom-style="saveButtonStyle" :loading="isLoading" loading-text="保存中..." @click="saveSettings">
                    保存设置
                </u-button>
            </view>
        </view>
    </view>
</template>

<script>
import { getAutoReplyRobot, setAutoReplyRobot } from '@/utils/vmeitime-http/msg';

export default {
    data() {
        return {
            robotData: {
                name: '',
                sex: 1,
                interval: 10,
                status: false,
                option_json: '',
            },
            replyOptions: ['我的星座是白羊座', '我的爱好是打篮球', '我的特长是唱歌'],
            isLoading: false,
        };
    },
    computed: {
        // 将status转换为API需要的格式
        statusValue() {
            return this.robotData.status ? 1 : 2;
        },
        // 输入框样式
        inputStyle() {
            return {
                backgroundColor: '#f8f9fa',
                borderRadius: '12rpx',
                padding: '0 20rpx',
                border: 'none',
            };
        },
        // 保存按钮样式
        saveButtonStyle() {
            return {
                background: 'linear-gradient(135deg, #8966ef, #a584f2)',
                borderRadius: '50rpx',
                height: '88rpx',
                fontSize: '32rpx',
                fontWeight: 'bold',
                boxShadow: '0 8rpx 24rpx rgba(137, 102, 239, 0.3)',
                border: 'none',
                color: '#ffffff',
            };
        },
    },
    onLoad() {
        this.getRobotSettings();
    },
    methods: {
        // 获取机器人设置
        async getRobotSettings() {
            uni.showLoading({
                title: '加载中',
            });

            try {
                const res = await getAutoReplyRobot({
                    access_token: uni.getStorageSync('token'),
                });
                const robot = res.data.robot;
                // const { name, sex, interval, status, option_json } =;
                // 更新表单数据
                this.robotData.name = robot?.name || '';
                this.robotData.sex = parseInt(robot?.sex) || 1;
                this.robotData.interval = robot?.interval || 10;
                this.robotData.status = robot?.status == 1;
                this.replyOptions = robot?.option_json || ['我的星座是白羊座', '我的爱好是打篮球', '我的特长是唱歌'];
                // this.$set(this.replyOptions, option_json);
            } catch (error) {
                console.error('获取机器人设置失败', error);
                uni.showToast({
                    title: '获取设置失败',
                    icon: 'none',
                });
            } finally {
                uni.hideLoading();
            }
        },

        // 开关状态变更
        onSwitchChange(value) {
            console.log('机器人状态变更:', value);
        },

        // 添加回复选项
        addReplyOption() {
            this.replyOptions.push('');
        },

        // 移除回复选项
        removeReplyOption(index) {
            if (this.replyOptions.length > 1) {
                this.replyOptions.splice(index, 1);
            } else {
                uni.showToast({
                    title: '至少保留一个配置项',
                    icon: 'none',
                });
            }
        },

        // 保存设置
        async saveSettings() {
            // 验证表单
            if (!this.robotData.name) {
                uni.showToast({
                    title: '请输入机器人名称',
                    icon: 'none',
                });
                return;
            }

            // 验证回复选项不为空
            const validOptions = this.replyOptions.filter(item => item.trim() !== '');
            if (validOptions.length === 0) {
                uni.showToast({
                    title: '请至少添加一条机器人自定义配置',
                    icon: 'none',
                });
                return;
            }

            this.isLoading = true;
            uni.showLoading({
                title: '保存中',
            });

            try {
                // 准备提交数据
                const params = {
                    name: this.robotData.name,
                    sex: this.robotData.sex,
                    interval: this.robotData.interval,
                    status: this.statusValue,
                    option_json: JSON.stringify(validOptions),
                    access_token: uni.getStorageSync('token'),
                };

                await setAutoReplyRobot(params);

                uni.showToast({
                    title: '设置成功',
                    icon: 'success',
                });

                // 延迟返回
                setTimeout(() => {
                    uni.navigateBack();
                }, 1500);
            } catch (error) {
                console.error('保存设置失败', error);
                uni.showToast({
                    title: '保存失败，请重试',
                    icon: 'none',
                });
            } finally {
                this.isLoading = false;
                uni.hideLoading();
            }
        },
    },
};
</script>

<style lang="scss">
/* 定义全局变量 */
$primary-gradient: linear-gradient(135deg, #8966ef, #a584f2, #f0cc6c);
$primary-color: #8966ef;
$secondary-color: #f0cc6c;
$primary-light: #a584f2;
$text-dark: #333333;
$text-medium: #666666;
$text-light: #999999;
$bg-light: #f5f7fa;
$card-bg: #ffffff;
$border-radius: 20rpx;
$shadow-light: 0 8rpx 24rpx rgba(137, 102, 239, 0.1);

.robot-container {
    background-color: $bg-light;
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.page-content {
    padding: 24rpx;
    padding-bottom: 120rpx;
}

/* 机器人状态卡片 */
.status-card {
    background: linear-gradient(135deg, #ffffff 0%, #f9fbff 100%);
    border-radius: $border-radius;
    padding: 32rpx 24rpx;
    margin-bottom: 24rpx;
    box-shadow: $shadow-light;
    display: flex;
    align-items: center;
    animation: slideInUp 0.6s ease-out;

    .robot-avatar {
        width: 100rpx;
        height: 100rpx;
        background: linear-gradient(135deg, rgba(137, 102, 239, 0.1), rgba(240, 204, 108, 0.1));
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 24rpx;
        border: 3rpx solid rgba(137, 102, 239, 0.2);
        image{
            width: 64rpx;
        }
    }

    .status-info {
        flex: 1;

        .status-title {
            font-size: 32rpx;
            font-weight: bold;
            color: $text-dark;
            margin-bottom: 8rpx;
        }

        .status-desc {
            font-size: 26rpx;
            color: $text-medium;
            margin-bottom: 12rpx;
        }

        .status-badge {
            display: flex;
            align-items: center;
            padding: 6rpx 16rpx;
            background-color: rgba(255, 0, 0, 0.1);
            border-radius: 20rpx;
            width: fit-content;
            font-size: 24rpx;
            color: #ff4757;

            &.active {
                background-color: rgba(137, 102, 239, 0.1);
                color: $primary-color;

                .status-dot {
                    background-color: $primary-color;
                    animation: pulse 1.5s infinite;
                }
            }

            .status-dot {
                width: 12rpx;
                height: 12rpx;
                border-radius: 50%;
                background-color: #ff4757;
                margin-right: 8rpx;
            }
        }
    }

    .switch-area {
        margin-left: 16rpx;
    }
}

/* 设置区域 */
.setting-section,
.config-section {
    background-color: $card-bg;
    border-radius: $border-radius;
    padding: 32rpx 24rpx;
    margin-bottom: 24rpx;
    box-shadow: $shadow-light;
    animation: slideInUp 0.6s ease-out;

    .section-header {
        display: flex;
        align-items: center;
        margin-bottom: 32rpx;

        .section-title {
            font-size: 32rpx;
            font-weight: bold;
            color: $text-dark;
            margin-left: 12rpx;
        }
    }
}

/* 表单容器 */
.form-container {
    .form-item {
        margin-bottom: 40rpx;

        &:last-child {
            margin-bottom: 0;
        }

        .form-label {
            display: flex;
            align-items: center;
            margin-bottom: 20rpx;
            font-size: 28rpx;
            color: $text-dark;
            font-weight: 500;

            text {
                margin-left: 12rpx;
            }
        }

        .input-wrapper {
            background-color: #f8f9fa;
            border-radius: 12rpx;
            padding: 4rpx 20rpx;
            border: 2rpx solid transparent;
            transition: all 0.3s ease;

            &:focus-within {
                border-color: rgba(137, 102, 239, 0.3);
                background-color: rgba(137, 102, 239, 0.05);
            }
        }

        .radio-wrapper {
            background-color: #f8f9fa;
            border-radius: 12rpx;
            padding: 20rpx;
            border: 2rpx solid transparent;
        }

        .input-with-unit {
            display: flex;
            align-items: center;
            gap: 20rpx;

            .input-wrapper {
                flex: 1;
            }

            .unit-text {
                font-size: 28rpx;
                color: $text-medium;
                font-weight: 500;
                background-color: rgba(137, 102, 239, 0.1);
                padding: 16rpx 20rpx;
                border-radius: 12rpx;
                color: $primary-color;
            }
        }
    }
}

/* 配置区域 */
.config-desc {
    display: flex;
    align-items: center;
    background-color: rgba(240, 204, 108, 0.1);
    padding: 16rpx 20rpx;
    border-radius: 12rpx;
    margin-bottom: 24rpx;

    text {
        margin-left: 12rpx;
        font-size: 26rpx;
        color: $text-medium;
        line-height: 1.4;
    }
}

.config-list {
    .config-item {
        display: flex;
        align-items: center;
        margin-bottom: 24rpx;
        gap: 20rpx;

        &:last-child {
            margin-bottom: 0;
        }

        .config-input {
            flex: 1;

            .input-wrapper {
                background-color: #f8f9fa;
                border-radius: 12rpx;
                padding: 4rpx 20rpx;
                border: 2rpx solid transparent;
                transition: all 0.3s ease;

                &:focus-within {
                    border-color: rgba(137, 102, 239, 0.3);
                    background-color: rgba(137, 102, 239, 0.05);
                }
            }
        }

        .config-actions {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 60rpx;
            height: 60rpx;
            border-radius: 50%;
            background-color: rgba(255, 107, 107, 0.1);
            transition: all 0.3s ease;

            &:active {
                transform: scale(0.95);
                background-color: rgba(255, 107, 107, 0.2);
            }
        }
    }
}

.add-config-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24rpx;
    border: 2rpx dashed rgba(137, 102, 239, 0.3);
    border-radius: 12rpx;
    background-color: rgba(137, 102, 239, 0.05);
    color: $primary-color;
    font-size: 28rpx;
    font-weight: 500;
    transition: all 0.3s ease;
    margin-top: 30rpx;
    &:active {
        transform: scale(0.98);
        background-color: rgba(137, 102, 239, 0.1);
    }

    text {
        margin-left: 12rpx;
    }
}

/* 保存按钮区域 */
.save-section {
    padding: 0 24rpx;
    margin-top: 40rpx;
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30rpx);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%,
    100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
}

/* 响应式优化 */
@media (max-width: 750rpx) {
    .status-card {
        .robot-avatar {
            width: 80rpx;
            height: 80rpx;
            margin-right: 20rpx;
        }

        .status-info .status-title {
            font-size: 28rpx;
        }
    }
}
</style>
