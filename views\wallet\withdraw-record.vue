<template>
    <view class="withdraw-record-container">
        <!-- 统计信息 -->
        <view class="stats-card">
            <view class="stats-item">
                <view class="stats-value">{{ totalWithdrawAmount }}</view>
                <view class="stats-label">累计提现(元)</view>
            </view>
            <view class="stats-divider"></view>
            <view class="stats-item">
                <view class="stats-value">{{ totalWithdrawCount }}</view>
                <view class="stats-label">提现次数</view>
            </view>
        </view>

        <!-- 提现记录列表 -->
        <view class="record-section">
            <view class="section-header">
                <text class="section-title">提现记录</text>
            </view>

            <view class="record-list" v-if="recordList.length > 0">
                <view class="record-item" v-for="(item, index) in recordList" :key="index">
                    <view class="record-info">
                        <view class="record-amount">-¥{{ item.amount }}</view>
                        <view class="record-time">{{ item.create_time }}</view>
                        <view class="record-desc">提现到微信零钱</view>
                    </view>
                    <!-- <view class="record-status" :class="getStatusClass(item.status)">
                        {{ getStatusText(item.status) }}
                    </view> -->
                </view>
            </view>

            <view class="empty-state" v-else-if="!loading">
                <u-empty mode="data" text="暂无提现记录"></u-empty>
            </view>

            <view class="loading-more" v-if="recordList.length > 0">
                <text v-if="loading">正在加载更多...</text>
                <text v-else-if="finished">没有更多数据了</text>
                <text v-else>上拉加载更多</text>
            </view>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
import { getWithdrawRecord } from '../../utils/vmeitime-http/order.js';

export default {
    data() {
        return {
            recordList: [],
            page: 1,
            pagesize: 10,
            loading: false,
            finished: false,
            totalWithdrawAmount: '0.00',
            totalWithdrawCount: 0
        };
    },
    computed: {
        ...mapState(['hasLogin', 'userInfo'])
    },
    onLoad() {
        this.init();
    },
    onPullDownRefresh() {
        this.init();
        setTimeout(() => {
            uni.stopPullDownRefresh();
        }, 1000);
    },
    onReachBottom() {
        if (!this.loading && !this.finished) {
            this.page++;
            this.loadRecordList();
        }
    },
    methods: {
        init() {
            this.page = 1;
            this.finished = false;
            this.loadRecordList();
        },

        // 加载提现记录
        async loadRecordList() {
            if (!this.hasLogin || this.loading || this.finished) {
                return;
            }

            this.loading = true;

            try {
                const res = await getWithdrawRecord({
                    access_token: uni.getStorageSync('token'),
                    page: this.page,
                    pagesize: this.pagesize
                });

                if (res.status === 0) {
                    const list = res.data.list || [];
                    
                    if (this.page === 1) {
                        this.recordList = list;
                        // 更新统计信息
                        this.totalWithdrawAmount = res.data.total_amount || '0.00';
                        this.totalWithdrawCount = res.data.total_count || 0;
                    } else {
                        this.recordList = [...this.recordList, ...list];
                    }

                    // 判断是否加载完毕
                    if (list.length < this.pagesize) {
                        this.finished = true;
                    }
                } else {
                    uni.showToast({
                        title: res.msg || '获取记录失败',
                        icon: 'none'
                    });
                }
            } catch (error) {
                console.error('获取提现记录失败', error);
                uni.showToast({
                    title: '网络异常，请稍后重试',
                    icon: 'none'
                });
            } finally {
                this.loading = false;
            }
        },

        // 获取状态样式类
        getStatusClass(status) {
            switch (status) {
                case 0:
                    return 'pending';
                case 1:
                    return 'success';
                case 2:
                    return 'failed';
                default:
                    return 'pending';
            }
        },

        // 获取状态文字
        getStatusText(status) {
            switch (status) {
                case 0:
                    return '处理中';
                case 1:
                    return '已到账';
                case 2:
                    return '失败';
                default:
                    return '处理中';
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.withdraw-record-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);
    padding-bottom: 30rpx;
}

.stats-card {
    display: flex;
    background: linear-gradient(135deg, #8966ef, #a584f2);
    margin: 20rpx 30rpx;
    padding: 40rpx 30rpx;
    border-radius: 12rpx;
    color: #fff;

    .stats-item {
        flex: 1;
        text-align: center;

        .stats-value {
            font-size: 48rpx;
            font-weight: bold;
            margin-bottom: 10rpx;
        }

        .stats-label {
            font-size: 26rpx;
            opacity: 0.8;
        }
    }

    .stats-divider {
        width: 2rpx;
        background-color: rgba(255, 255, 255, 0.3);
        margin: 0 30rpx;
    }
}

.record-section {
    margin: 20rpx 30rpx;
    background-color: #fff;
    border-radius: 12rpx;

    .section-header {
        padding: 30rpx;
        border-bottom: 1px solid #f0f0f0;

        .section-title {
            font-size: 32rpx;
            font-weight: 500;
            color: #333;
        }
    }

    .record-list {
        .record-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 30rpx;
            border-bottom: 1px solid #f5f5f5;

            &:last-child {
                border-bottom: none;
            }

            .record-info {
                flex: 1;

                .record-amount {
                    font-size: 32rpx;
                    font-weight: 500;
                    color: #333;
                    margin-bottom: 8rpx;
                }

                .record-time {
                    font-size: 24rpx;
                    color: #999;
                    margin-bottom: 4rpx;
                }

                .record-desc {
                    font-size: 24rpx;
                    color: #666;
                }
            }

            .record-status {
                padding: 8rpx 16rpx;
                border-radius: 20rpx;
                font-size: 24rpx;

                &.pending {
                    background-color: rgba(255, 193, 7, 0.1);
                    color: #ffc107;
                }

                &.success {
                    background-color: rgba(40, 167, 69, 0.1);
                    color: #28a745;
                }

                &.failed {
                    background-color: rgba(220, 53, 69, 0.1);
                    color: #dc3545;
                }
            }
        }
    }

    .empty-state {
        padding: 60rpx 0;
    }

    .loading-more {
        padding: 30rpx;
        text-align: center;
        font-size: 26rpx;
        color: #999;
    }
}
</style>
