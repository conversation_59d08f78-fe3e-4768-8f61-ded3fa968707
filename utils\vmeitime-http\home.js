const http = uni.$u.http;
/**
 * 首页获取列表
 * @param {Object} d 请求参数
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
 * @param {access_token} d.access_token 用户token
*/
export const getHomeList = d => http.post('?c=Index&a=home_tj_work_list', d);

/**
 * 获取作品详情
 * @param {Object} d 请求参数
 * @param {Number} d.work_id 作品id
 * @param {access_token} d.access_token 用户token
 * 
*/
export const getWorkDetail = d => http.post('?c=Index&a=get_work_info', d);

/**
 * 关注用户
 * @param {Object} d 请求参数
 * @param {Number} d.follow_user_id 用户id
 * @param {access_token} d.access_token 用户token
*/
export const followUser = d => http.post('?c=Index&a=follow_user', d);

/**
 * 点赞收藏
 * @param {Object} d 请求参数
 * @param {work_id} d.work_id 作品id
 * @param {access_token} d.access_token 用户token
*/
export const likeWork = d => http.post('?c=Index&a=like_work', d);

/**
 * 评价作品
 * @param {Object} d 请求参数
 * @param {Number} d.work_id 作品id
 * @param {access_token} d.access_token 用户token
 * @param {String} d.content 评论内容
 * @param {Number} d.parent_id 父级评论id
*/
export const addWorkComment = d => http.post('?c=Index&a=work_comment', d);

/**
 * 获取评价列表
 * @param {Object} d 请求参数
 * @param {Number} d.work_id 作品id
 * @param {access_token} d.access_token 用户token
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
 * @param {Number} d.parent_id 父级评论id
*/
export const getWorkCommentList = d => http.post('?c=Index&a=work_comment_list', d);

/**
 * 删除作品
 * @param {Object} d 请求参数
 * @param {Number} d.work_id 作品id
 * @param {access_token} d.access_token 用户token
*/
export const deleteWork = d => http.post('?c=user&a=del_work', d);

/**
 * aite用户
 * @param {Object} d 请求参数
 * @param {access_token} d.access_token 用户token
 * @param {Number} d.keyword 被@用户id
*/
export const aiteUser = d => http.post('?c=Index&a=ai_user_list', d);

/**
 * 获取我的关注列表
 * @param {String} d.access_token 用户token
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
*/
export const getFollowList = d => http.post('?c=index&a=get_follow_list', d);

/**
 * 获取我的粉丝列表
 * @param {String} d.access_token 用户token
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
*/
export const getFansList = d => http.post('?c=index&a=get_follow_passive_list', d);

/**
 * 获取我收藏的作品
 * @param {String} d.access_token 用户token
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
*/
export const getLikeWorkList = d => http.post('?c=index&a=like_work_list', d);

/**
 * 隐藏下架作品
 * @param {Object} d 请求参数
 * @param {Number} d.work_id 作品id
 * @param {access_token} d.access_token 用户token
*/
export const hideWork = d => http.post('?c=index&a=work_display', d);

/**
 * 获取活跃用户作品
 * @param {Object} d 请求参数
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
*/
export const getActiveUserWork = d => http.post('?c=index&a=home_huoyue_list', d);

/**
 * 获取附近用户作品
 * @param {Object} d 请求参数
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
 * @param {Number} d.longitude 经度
 * @param {access_token} d.access_token 用户token
 * @param {Number} d.latitude 纬度  
*/
export const getNearbyUserWork = d => http.post('?c=index&a=nearby_list', d);

/**
 * 获取分类作品列表
 * @param {Object} d 请求参数
 * @param {Number} d.page 页码
 * @param {Number} d.pagesize 每页条数
 * @param {access_token} d.access_token 用户token
 * @param {Number} d.work_type_id 分类id
*/
export const getCategoryWorkList = d => http.post('?c=index&a=work_type_work_list', d);

export default {
    getHomeList,
    likeWork,
    getWorkDetail,
    followUser,
    addWorkComment,
    getWorkCommentList,
    deleteWork,
    aiteUser,
    getFollowList,
    getFansList,
    getLikeWorkList,
    hideWork,
    getActiveUserWork,
    getNearbyUserWork,
    getCategoryWorkList
};
