# UserCard 组件合并测试文档

## 合并完成情况

✅ **已完成的工作：**

1. **组件合并**：将 UserCard.vue、UserCardMine.vue、UserCardProfile.vue 三个组件合并为一个统一的 UserCard.vue 组件

2. **功能整合**：
   - 基础展示功能（所有组件共有）
   - 点赞功能（所有组件共有）
   - 地址格式化（所有组件共有，代码完全相同）
   - 状态切换功能（Mine/Profile特有）
   - 浏览量显示（Mine/Profile特有）
   - 隐藏遮罩（Mine/Profile特有）

3. **Props 设计**：
   - `info/workInfo`: 作品信息对象（兼容两种命名）
   - `index`: 索引（UserCard需要）
   - `showView`: 显示模式控制（UserCardMine/Profile需要）
   - `hideUserInfo`: 隐藏用户信息（UserCardMine/Profile需要）
   - `mode`: 新增模式参数，可选值：'basic'、'mine'、'profile'、'auto'

4. **自动模式检测**：
   - 如果有 workInfo 属性，自动判断为 mine 或 profile 模式
   - 如果只有 info 属性，判断为 basic 模式
   - 支持手动指定 mode 参数

5. **页面更新**：
   - ✅ pages/index/mine.vue - 更新为使用统一的 UserCard 组件
   - ✅ views/mine/userProfile.vue - 更新为使用统一的 UserCard 组件
   - ✅ pages/index/index.vue - 保持原有使用方式（向后兼容）
   - ✅ pages/search/index.vue - 保持原有使用方式（向后兼容）

6. **文件清理**：
   - ✅ 删除 components/UserCardMine.vue
   - ✅ 删除 components/UserCardProfile.vue

## 使用方式

### 基础模式（原 UserCard）
```vue
<user-card :info="item" :index="index" @like="handleLike"></user-card>
```

### 我的作品模式（原 UserCardMine）
```vue
<user-card :work-info="user" mode="mine" @statusChanged="handleStatusChanged"></user-card>
<user-card :showView="false" :work-info="user.class_info" mode="mine" @like="handleLike"></user-card>
```

### 用户资料模式（原 UserCardProfile）
```vue
<user-card :work-info="work" :hide-user-info="true" mode="profile"></user-card>
```

## 向后兼容性

- ✅ 保持原有的 props 接口
- ✅ 保持原有的事件接口
- ✅ 自动检测使用模式
- ✅ 现有页面无需修改即可正常工作

## 优势

1. **代码复用**：消除了大量重复代码，特别是地址格式化逻辑
2. **维护性**：只需维护一个组件，修改更容易
3. **一致性**：确保所有用户卡片的行为和样式保持一致
4. **扩展性**：新增功能只需在一个地方添加
5. **向后兼容**：现有代码无需修改

## 修复的问题

### ✅ 浏览量显示问题
- **问题**：mine作品页面的浏览量不显示
- **原因**：浏览量显示逻辑只针对profile模式，遗漏了mine模式
- **修复**：调整浏览量显示逻辑，在showView模式下的mine和profile模式都显示浏览量
- **布局**：将浏览量放在底部信息区域内，与点赞按钮并排显示

### ✅ 位置信息对齐问题
- **问题**：位置信息默认左对齐，需要右对齐
- **修复**：
  - 默认情况下位置信息右对齐（`justify-content: flex-end`）
  - 当有用户信息显示时，使用两端对齐（`justify-content: space-between`）
  - 通过 `has-user-info` CSS类动态控制布局

### ✅ 点赞列表红心显示问题
- **问题**：点赞列表中的小红心不是红色填充状态
- **原因**：点赞列表中的作品都是已点赞的，但组件仍依赖原始数据的 `is_like` 状态
- **修复**：
  - 添加 `isInLikeList` 计算属性判断是否在点赞列表模式
  - 添加 `likeStatus` 计算属性，在点赞列表中强制返回 `true`
  - 更新模板使用 `likeStatus` 而不是 `workData.is_like`

### ✅ 用户资料页作品点赞右对齐问题
- **问题**：userProfile.vue页面中作品的点赞按钮没有右对齐
- **原因**：profile模式的布局逻辑与mine模式混合，没有针对profile模式优化
- **修复**：
  - 分离mine模式和profile模式的布局逻辑
  - 在profile模式下，当没有标签时，添加 `profile-mode-no-tags` CSS类
  - 该CSS类设置 `justify-content: flex-end` 使点赞按钮右对齐

### 🔧 布局逻辑优化
- **showView模式**（我的作品页面）：显示浏览量 + 点赞按钮
- **非showView模式**（点赞收藏列表）：显示标签 + 点赞按钮
- **basic模式**（首页/搜索）：显示标签 + 点赞按钮

## 测试建议

1. 测试基础模式：首页和搜索页的用户卡片显示
2. 测试我的作品模式：我的页面的作品展示和状态切换
3. 测试用户资料模式：用户资料页的作品展示
4. 测试点赞功能在各种模式下的正确性
5. 测试地址格式化在各种地址格式下的正确性
6. **重点测试**：mine页面的浏览量显示和位置信息右对齐
