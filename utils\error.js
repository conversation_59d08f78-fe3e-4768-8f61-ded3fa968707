export const ErrorType = {
    12000: '初始化失败',
    12001: '一般的错误',
    12002: '密码错误',
    12003: '连接超时',
    12004: '重复连接 WiFi',
    12005: '未打开WiFi开关',
    12006: '未打开 GPS 定位开关',
    12007: '用户拒绝授权链接WiFi',
    12008: '无效 SSID',
    12009: '系统运营商配置拒绝连接 WiFi',
    12010: '系统其他错误，需要在 errmsg 打印具体的错误原因',
    12011: '应用在后台无法配置 WiFi',
    12013: '系统保存的 WiFi 配置过期，建议忘记 WiFi 后重试',
    12014: '无效的 WEP / WPA 密码',
    10000: '未初始化蓝牙适配器',
    10001: '当前蓝牙适配器不可用，请确保手机蓝牙已正确打开',
    10002: '没有找到指定设备',
    10003: '连接失败',
    10004: '没有找到指定服务',
    10005: '没有找到指定特征',
    10006: '当前连接已断开',
    10007: '当前特征不支持此操作',
    10009: 'Android 系统特有，系统版本低于 4.3 不支持 BLE',
    10012: '连接超时',
    10013: '连接 deviceId 为空或者是格式不正确',
};
export const playCode = {
    2030: '音频设备发生改变，即当前的输入输出设备发生改变，比如耳机被拔出',
    2101: '拉流：当前视频帧解码失败',
    2102: '拉流：当前音频帧解码失败',
    2103: '拉流：网络断连, 已启动自动重连',
    2104: '拉流：网络来包不稳：可能是下行带宽不足，或由于主播端出流不均匀 ',
    2105: '拉流：当前视频播放出现卡顿',
    2106: '拉流：硬解启动失败，采用软解',
    2107: '拉流：当前视频帧不连续，可能丢帧',
    2108: '拉流：当前流硬解第一个I帧失败，SDK自动切软解',
    3001: '拉流：RTMP -DNS解析失败',
    3002: '拉流：RTMP服务器连接失败',
    3003: '拉流：RTMP服务器握手失败',
    3005: '拉流：RTMP 读/写失败，之后会发起网络重试',
    '-2301': '拉流：网络断连，且经多次重连无效，请自行重启拉流',
    '-2302': '拉流：获取拉流地址失败',
};
export const pushCode = {
    1101: '推流：网络状况不佳：上行带宽太小，上传数据受阻',
    1102: '推流：网络断连, 已启动自动重连',
    1103: '推流：硬编码启动失败, 采用软编码',
    1104: '推流：视频编码失败, 内部会重启编码器',
    3001: '推流：RTMP DNS解析失败',
    3002: '推流：RTMP服务器连接失败',
    3003: '推流：RTMP服务器握手失败',
    3004: '推流：RTMP服务器主动断开，请检查推流地址的合法性或防盗链有效期',
    3005: '推流：RTMP 读/写失败',
    5001: '系统电话打断或者微信音视频电话打断',
    10001: '用户禁止使用摄像头',
    10002: '用户禁止使用录音',
    10003: '背景音资源（BGM）加载失败',
    10004: '等待画面资源（waiting-image）加载失败',
    '-1301': '推流：打开摄像头失败',
    '-1302': '推流：打开麦克风失败',
    '-1303': '推流：视频编码失败',
    '-1304': '推流：音频编码失败',
    '-1305': '推流：不支持的视频分辨率',
    '-1306': '推流：不支持的音频采样率',
    '-1307': '推流：网络断连，且经多次重连抢救无效，更多重试请自行重启推流',
    '-1308': '推流：开始录屏失败，可能是被用户拒绝',
    '-1309': '推流：录屏失败，不支持的Android系统版本，需要5.0以上的系统',
    '-1310': '推流：录屏被其他应用打断了',
    '-1311': '推流：Android Mic打开成功，但是录不到音频数据',
    '-1312': '推流：录屏动态切横竖屏失败',
};
