<template>
    <view class="container">
        <view class="tabs-gap"></view>
        <!-- 标签页区域 -->
        <view class="tabs-section">
            <u-tabs
                :list="tabList"
                :current="current"
                @change="tabsChange"
                :is-scroll="true"
                active-color="#8966ef"
                bg-color="#fff5e6"
                inactive-color="#333"
                :bar-style="{ background: 'linear-gradient(90deg, #8966ef, #a584f2)', height: '6rpx', borderRadius: '3rpx' }"
            ></u-tabs>
        </view>
        <!-- <swiper class="swiper-box" :current="swiperCurrent" @transition="transition" @animationfinish="animationfinish" @change="swiperChange"> -->
        <!-- <swiper-item class="swiper-item" v-for="(item, index) in tabList" :key="index"> -->
        <scroll-view scroll-y class="scroll-view" @scrolltolower="onLoadMore" :style="{ height: scrollViewHeight }">
            <empty-state v-if="!loading && homeList.length === 0" :show="!loading && homeList.length === 0" type="default" tip="暂无数据"></empty-state>

            <view v-else class="user-grid">
                <view class="user-grid-item" v-for="(user, userIndex) in homeList" :key="userIndex">
                    <user-card :info="user" :index="userIndex" @like="handleLike"></user-card>
                </view>
            </view>

            <view class="loading-more" v-if="homeList.length > 0">
                <text v-if="loading">正在加载更多...</text>
                <text v-else-if="finished">没有更多数据了</text>
                <text v-else>上拉加载更多</text>
            </view>
        </scroll-view>
        <!-- </swiper-item> -->
        <!-- </swiper> -->
        <login-popup v-model="showLoginPopup" @close="handleLoginPopupClose" @login="handleLoginAction"></login-popup>
    </view>
</template>

<script>
import UserCard from '../../components/UserCard';
import authorization from '../../components/popup/authorization.vue';
import { mapState, mapActions } from 'vuex';
import loginPopup from '@/components/login-popup.vue';
import EmptyState from '@/components/empty-state.vue';

export default {
    components: {
        UserCard,
        authorization,
        loginPopup,
        EmptyState,
    },
    computed: {
        ...mapState(['userInfo', 'hasLogin']),
    },
    data() {
        return {
            // 标签页数据
            tabList: [
                { name: '全部', id: 'all' },
                { name: '活跃', id: 'active' },
                { name: '附近', id: 'nearby' },
                // { name: '商城', id: 'shop' },
            ],
            current: 0, // 当前选中的标签索引
            swiperCurrent: 0,
            showLoginPopup: false,
            // 模拟用户数据
            userDataList: [],
            page: 1,
            pagesize: 10,
            homeList: [],
            total: 0,
            loading: false, // 加载状态
            finished: false, // 是否已加载全部数据
            refresherTriggered: false, // 下拉刷新状态
            // 位置信息
            longitude: null, // 经度
            latitude: null, // 纬度
            // scroll-view 高度
            scrollViewHeight: 'calc(100vh - 120rpx)', // 调整高度以适应优化后的标签页
        };
    },
    onLoad() {
        console.log(uni.getStorageSync('token'));
        // 页面加载时的初始化逻辑
        this.getLocation(); // 先获取位置信息
        this.init();
        this.getHomeCategory();
        this.calculateScrollViewHeight();
        uni.$on('refreshPublish', () => {
            this.getHomeList();
        });
        uni.$on('refreshLike', () => {
            this.getHomeList();
        });
        // this.$api.getAllShopGoodList({
        //     access_token: uni.getStorageSync('token'),
        //     page: 1,
        //     pagesize: 10,
        // });
        // this.$api.mobileLogin({
        //     login_type:1,
        //     login_name:'17722280950',
        //     password:'123456',
        // })
    },
    onUnload() {
        uni.$off('refreshPublish');
        uni.$off('refreshLike');
    },
    onShow() {
        // this.init();
    },
    onPullDownRefresh: function () {
        setTimeout(() => {
            uni.stopPullDownRefresh();
        }, 500);
        this.init();
    },
    methods: {
        ...mapActions(['VAgetUser']),
        // 登录成功回调
        loginSuccess(type) {},
        // 标签切换事件
        tabsChange(index) {
            this.homeList = []
            // 更新当前选中状态
            this.current = index;
            this.swiperCurrent = index;

            // 延迟执行数据加载，确保UI切换流畅
            this.$nextTick(() => {
                this.init();
            });
        },

        transition(e) {
            // let dx = e.detail.dx;
            // this.$refs.uTabs.setDx(dx);
        },
        // 由于swiper的内部机制问题，快速切换swiper不会触发dx的连续变化，需要在结束时重置状态
        // swiper滑动结束，分别设置tabs和swiper的状态
        animationfinish(e) {
            let current = e.detail.current;
            // console.log(current)
            // 跳过商城标签（索引3）
            if (current == 3) {
                uni.navigateTo({
                    url: `/views/shop/index`,
                });
                return;
            }

            // 先更新tabs组件状态
            this.$refs.uTabs.setFinishCurrent(current);
            this.swiperCurrent = current;
            this.current = current;

            // 延迟执行数据加载，避免影响滑动动画
            // this.$nextTick(() => {
            //     this.init();
            // });
        },

        // 初始化
        init() {
            this.page = 1;
            this.finished = false;
            this.getHomeList();
            if (this.hasLogin) {
                this.VAgetUser();
            }
        },

        // 获取首页列表
        async getHomeList(isLoadMore = false) {
            // 如果正在加载或者已经加载完成，则不再请求
            if (this.loading || (isLoadMore && this.finished)) {
                console.log('请求被阻止', { loading: this.loading, finished: this.finished, isLoadMore });
                return;
            }

            this.loading = true;

            try {
                let res;
                const currentTab = this.tabList[this.current];
                const baseParams = {
                    page: this.page,
                    pagesize: this.pagesize,
                    access_token: uni.getStorageSync('token'),
                };

                // 根据当前标签页调用不同的接口
                switch (currentTab.id) {
                    case 'all':
                        res = await this.$api.getHomeList(baseParams);
                        break;
                    case 'active':
                        res = await this.$api.getActiveUserWork(baseParams);
                        break;
                    case 'nearby':
                        // 附近需要传递经纬度参数
                        if (this.longitude && this.latitude) {
                            res = await this.$api.getNearbyUserWork({
                                ...baseParams,
                                longitude: this.longitude,
                                latitude: this.latitude,
                            });
                        } else {
                            // 如果没有位置信息，先获取位置
                            await this.getLocation();
                            if (this.longitude && this.latitude) {
                                res = await this.$api.getNearbyUserWork({
                                    ...baseParams,
                                    longitude: this.longitude,
                                    latitude: this.latitude,
                                });
                            } else {
                                // 获取位置失败，使用全部接口
                                res = await this.$api.getHomeList(baseParams);
                            }
                        }
                        break;
                    default:
                        // 检查是否为动态分类（数字字符串ID）
                        if (/^\d+$/.test(currentTab.id)) {
                            // 调用分类作品接口
                            res = await this.$api.getCategoryWorkList({
                                ...baseParams,
                                work_type_id: currentTab.id,
                            });
                        } else {
                            // 其他情况使用全部接口
                            res = await this.$api.getHomeList(baseParams);
                        }
                        break;
                }

                // 获取数据总数
                const total = Number(res.data.total || 0);
                this.total = total;

                // 获取当前数据列表
                const list = res.data.list || res.data.work_type_list || [];

                // 根据是否为加载更多，决定是替换还是追加数据
                if (isLoadMore) {
                    this.homeList = [...this.homeList, ...list];
                } else {
                    this.homeList = list;
                }

                // 判断是否加载完毕 - 修复判断逻辑
                if (list.length === 0 || list.length < this.pagesize || this.homeList.length >= total) {
                    this.finished = true;
                } else {
                    this.finished = false;
                }
            } catch (error) {
                console.error('获取首页列表失败', error);
                uni.showToast({
                    title: '获取数据失败',
                    icon: 'none',
                });
                // 加载失败时回退页码
                if (isLoadMore && this.page > 1) {
                    this.page--;
                }
            } finally {
                this.loading = false;
                // 如果是下拉刷新，重置刷新状态
                if (this.refresherTriggered) {
                    this.refresherTriggered = false;
                }
            }
        },

        async getHomeCategory() {
            const res = await this.$api.getHomeCategory({
                access_token: uni.getStorageSync('token'),
            });
            const list = res.data.work_type_list.filter(item => {
                return Number(item.is_tj) === 1;
            });
            console.log(list);
            this.tabList = this.tabList.concat(list);
        },

        // 获取位置信息
        async getLocation() {
            return new Promise(resolve => {
                uni.getLocation({
                    type: 'wgs84',
                    success: res => {
                        console.log('获取位置成功:', res);
                        this.longitude = res.longitude;
                        this.latitude = res.latitude;
                        resolve(true);
                    },
                    fail: err => {
                        console.error('获取位置失败:', err);
                        // 位置获取失败时的处理
                        uni.showModal({
                            title: '位置权限',
                            content: '获取位置信息失败，将无法使用附近功能，是否重新授权？',
                            success: modalRes => {
                                if (modalRes.confirm) {
                                    // 引导用户去设置页面开启位置权限
                                    uni.openSetting({
                                        success: settingRes => {
                                            if (settingRes.authSetting['scope.userLocation']) {
                                                // 重新获取位置
                                                this.getLocation().then(resolve);
                                            } else {
                                                resolve(false);
                                            }
                                        },
                                    });
                                } else {
                                    resolve(false);
                                }
                            },
                        });
                    },
                });
            });
        },

        handleLike(like, index) {
            if (!this.hasLogin) {
                this.showLoginPopup = true;
                return;
            }
            this.$set(this.homeList[index], 'is_like', like);
            this.$set(this.homeList[index], 'like_num', like ? Number(this.homeList[index].like_num || 0) + 1 : Number(this.homeList[index].like_num || 0) - 1);
            console.log(index);
        },

        // swiper切换事件
        swiperChange(e) {
            // 这个方法在滑动过程中会被调用，只更新current状态
            // 数据加载在animationfinish中处理，避免滑动过程中的卡顿
            this.current = e.detail.current;
        },

        // 判断是否授权
        touchLogin() {
            // this.$refs.auth.show();
            uni.navigateTo({
                url: '/pages/index/login',
            });
            return;
        },

        // 下拉刷新处理
        onRefresh() {
            this.refresherTriggered = true;
            // 重置页码和加载状态
            this.page = 1;
            this.finished = false;
            // 刷新数据
            this.getHomeList();
        },

        // 触底加载更多
        onLoadMore() {
            if (!this.loading && !this.finished) {
                this.page++;
                console.log('开始加载第', this.page, '页');
                this.getHomeList(true);
            }
        },
        // 处理登录弹窗关闭
        handleLoginPopupClose() {
            this.showLoginPopup = false;
        },

        // 处理登录操作
        handleLoginAction() {
            // 登录弹窗已经会导航到登录页面，这里可以添加额外的逻辑
            console.log('用户点击了登录按钮');
        },

        // 计算scroll-view高度
        calculateScrollViewHeight() {
            // 获取系统信息
            const systemInfo = uni.getSystemInfoSync();
            // 获取tabs组件高度（大约120rpx，包含新的样式）
            const tabsHeight = uni.upx2px(120);
            // 计算scroll-view可用高度
            this.scrollViewHeight = `${systemInfo.windowHeight - tabsHeight}px`;
        },
    },
};
</script>

<style lang="scss" scoped>
.container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);
}
.tabs-gap {
    height: 20rpx;
}
// 标签页区域
.tabs-section {
    background: #fff;
    margin: 0rpx 30rpx 20rpx;
    border-radius: 30rpx;
    // padding: 10rpx 0;
    box-shadow: 0 4rpx 20rpx rgba(137, 102, 239, 0.08);
    border: 1rpx solid rgba(137, 102, 239, 0.1);
}

.content-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    background-color: transparent;
    z-index: 1071;
}

.swiper-box {
    height: calc(100vh - 120rpx);
}

.swiper-item {
    height: 100%;
}

.scroll-view {
    width: 100%;
}

.user-grid {
    display: flex;
    flex-wrap: wrap;
    padding: 0 20rpx 20rpx;

    .user-grid-item {
        width: 50%;
        padding: 5rpx 10rpx;
        box-sizing: border-box;
    }
}

.empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400rpx;
    color: #999;
    font-size: 28rpx;
}

.loading-more {
    text-align: center;
    padding: 0 0 20rpx;
    color: #999;
    font-size: 24rpx;
    font-weight: 500;
}
</style>
