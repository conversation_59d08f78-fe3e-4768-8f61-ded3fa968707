function NumFormat(value) {
    if (!value) return '0.00';
    var intPart = Number(value) | 0; //获取整数部分
    var intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,'); //将整数部分逢三一断
    var floatPart = '.00'; //预定义小数部分

    var value2Array = value.toString().split('.');
    var isNegtiveNo = '';
    if (intPartFormat < 0) {
        if (value.toString().indexOf('-') != 1) isNegtiveNo = '-'; //修复小于0负数丢失的问题
    }
    //=2表示数据有小数位
    if (value2Array.length == 2) {
        floatPart = value2Array[1].toString(); //拿到小数部分
        if (floatPart.length == 1) {
            //补0,实际上用不着
            return isNegtiveNo + intPartFormat + '.' + floatPart + '0';
        } else {
            return isNegtiveNo + intPartFormat + '.' + floatPart;
        }
    } else {
        return isNegtiveNo + intPartFormat + '.00';
    }
}
function formatPrice(priceStr) {
    let priceFen = Number(priceStr) * 100;
    return (priceFen / 100).toFixed(2);
}
// 获取当前时间
function getCurrentDate(connectType) {
    let year, month, day, h, m, s, date;
    date = new Date();
    if (connectType) {
        date = new Date(connectType);
    }
    year = date.getFullYear();
    month = Number(date.getMonth()) + 1 >= 10 ? Number(date.getMonth()) + 1 : '0' + (Number(date.getMonth()) + 1);
    day = date.getDate() >= 10 ? date.getDate() : '0' + date.getDate();
    h = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
    m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
    s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
    // if (connectType) {
    // 	return year + connectType + month + connectType + day
    // } else {
    // 	return year + "-" + month + "-" + day
    // }
    return year + '-' + month + '-' + day + ' ' + h + ':' + m + ':' + s;
}
function FormatHours(value) {
    value = value.replace(/-/g, '/');
    var date = new Date(value);
    var h = date.getHours();
    h = h < 10 ? '0' + h : h;
    var minute = date.getMinutes();
    minute = minute < 10 ? '0' + minute : minute;
    return h + ':' + minute; //年月日时分秒
}

function getTimeFormat(connectType, type) {
    let year, month, day, h, m, s;
    let str = connectType.replace(/-/g, '/');
    let date = new Date(str);
    year = date.getFullYear();
    month = Number(date.getMonth()) + 1 >= 10 ? Number(date.getMonth()) + 1 : '0' + (Number(date.getMonth()) + 1);
    day = date.getDate() >= 10 ? date.getDate() : '0' + date.getDate();
    h = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
    m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
    // s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
    // if (connectType) {
    // 	return year + connectType + month + connectType + day
    // } else {
    // 	return year + "-" + month + "-" + day
    // }
    return year + '-' + month + '-' + day + ' ' + h + ':' + m;
}
function getCurrentFormat(connectType, type) {
    let year, month, day, h, m, s;
    let date = new Date(connectType);
    year = date.getFullYear();
    month = Number(date.getMonth()) + 1 >= 10 ? Number(date.getMonth()) + 1 : '0' + (Number(date.getMonth()) + 1);
    day = date.getDate() >= 10 ? date.getDate() : '0' + date.getDate();
    h = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
    m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
    // s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
    // if (connectType) {
    // 	return year + connectType + month + connectType + day
    // } else {
    // 	return year + "-" + month + "-" + day
    // }
    if (type == 1) {
        return year + '年' + month + '月' + day + '日';
    } else if (type == 2) {
        return year + '-' + month + '-' + day;
    }
}
function getCurrentHours(connectType) {
    let year, month, day, h, m, s;
    let date = new Date(connectType);
    year = date.getFullYear();
    month = Number(date.getMonth()) + 1 >= 10 ? Number(date.getMonth()) + 1 : '0' + (Number(date.getMonth()) + 1);
    day = date.getDate() >= 10 ? date.getDate() : '0' + date.getDate();
    h = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
    m = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
    // s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
    // if (connectType) {
    // 	return year + connectType + month + connectType + day
    // } else {
    // 	return year + "-" + month + "-" + day
    // }
    return h + ':' + m + ':00';
}

// 将时间字符串格式化为时间戳
function formatTimeStamp(dateStr) {
    let str = dateStr.replace(/-/g, '/');
    let timeStamp = new Date(str).getTime();
    return timeStamp;
}

// 时间戳转化为时间
function timeStampToDate(timeStamp) {
    var date = new Date(timeStamp); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
    var Y, M, D, h, m, s, ms;
    Y = date.getFullYear() + '-';
    M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
    D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
    h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
    m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
    s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
    // ms = date.getMilliseconds() < 10 ? '0' + date.getMilliseconds() : date.getMilliseconds();
    return Y + M + D + h + m + s;
    // return Y + M + D + h + m;
    // m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
    // s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds())
    // return Y + M + D + h + m + s
}

// 时间戳转化为时间
function formatDate(timeStamp) {
    var date = new Date(timeStamp); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
    var Y, M, D, h, m, s;
    Y = date.getFullYear() + '-';
    M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
    D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
    // return Y + M + D + h + m + ':00'
    return Y + M + D;
    // m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
    // s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds())
    // return Y + M + D + h + m + s
}

// 将时间字符串转换为多久前
function formatTimeString(timeStr) {
    let reg = new RegExp('-', 'g');
    let str = timeStr.replace(reg, '/');
    let dateTimeStamp = new Date(str).getTime();

    var minute = 1000 * 60; //把分，时，天，周，半个月，一个月用毫秒表示
    var hour = minute * 60;
    var day = hour * 24;
    var week = day * 7;
    var halfamonth = day * 15;
    var month = day * 30;
    var year = day * 365;
    var now = new Date().getTime(); //获取当前时间毫秒
    var diffValue = now - dateTimeStamp; //时间差
    if (diffValue < 0) {
        return;
    }
    var minC = diffValue / minute; //计算时间差的分，时，天，周，月
    var hourC = diffValue / hour;
    var dayC = diffValue / day;
    var weekC = diffValue / week;
    var monthC = diffValue / month;
    var yearC = diffValue / year;
    var result;
    if (yearC >= 1) {
        result = ' ' + parseInt(yearC) + '年前';
    } else if (monthC >= 1 && monthC <= 12) {
        result = ' ' + parseInt(monthC) + '月前';
    } else if (weekC >= 1 && weekC <= 4.285714) {
        result = ' ' + parseInt(weekC) + '周前';
    } else if (dayC >= 1 && dayC <= 7) {
        result = ' ' + parseInt(dayC) + '天前';
    } else if (hourC >= 1 && hourC <= 24) {
        result = ' ' + parseInt(hourC) + '小时前';
    } else if (minC >= 1 && minC <= 60) {
        result = ' ' + parseInt(minC) + '分钟前';
    } else if (diffValue >= 0 && diffValue <= minute) {
        result = '刚刚';
    } else {
        var datetime = new Date();
        datetime.setTime(dateTimeStamp);
        var Nyear = datetime.getFullYear();
        var Nmonth = datetime.getMonth() + 1 < 10 ? '0' + (datetime.getMonth() + 1) : datetime.getMonth() + 1;
        var Ndate = datetime.getDate() < 10 ? '0' + datetime.getDate() : datetime.getDate();
        var Nhour = datetime.getHours() < 10 ? '0' + datetime.getHours() : datetime.getHours();
        var Nminute = datetime.getMinutes() < 10 ? '0' + datetime.getMinutes() : datetime.getMinutes();
        var Nsecond = datetime.getSeconds() < 10 ? '0' + datetime.getSeconds() : datetime.getSeconds();
        result = Nyear + '-' + Nmonth + '-' + Ndate;
    }
    return result;
}

//时间戳转日期
function timeStamp(time) {
    const dates = new Date(time);
    const year = dates.getFullYear();
    const month = dates.getMonth() + 1;
    const date = dates.getDate();
    const day = dates.getDay();
    const hour = dates.getHours();
    const min = dates.getMinutes();
    const days = ['日', '一', '二', '三', '四', '五', '六'];
    return {
        allDate: `${year}-${strFormat(month)}-${strFormat(date)}`, //注:此处ios系统如"-"分割无法显示,只能用"/"分割符
        date: `${strFormat(month)}-${strFormat(date)}`, //返回的日期 07-01
        days: `${days[day]}`, //返回的礼拜天数  星期一
        day: `${strFormat(date)}`, //返回的礼拜天数  星期一
        hour: strFormat(hour) + ':' + strFormat(min), //返回的时钟 08:00
        format: `${strFormat(month)}月${strFormat(date)}日`,
        start: `${year}-${strFormat(month)}-${strFormat(date)} 00:00:00`,
        end: `${year}-${strFormat(month)}-${strFormat(date)} 23:59:59`,
        mounth: `${month}月`,
    };
}

//字符串拼接
function strFormat(str) {
    return str < 10 ? `0${str}` : str;
}
//获取最近7天的日期和礼拜天数
function dateData(num) {
    const time = [];
    const date = new Date();
    const now = date.getTime(); //获取当前日期的时间戳
    let timeStr = 3600 * 24 * 1000; //一天的时间戳
    for (let i = 0; i < num; i++) {
        const timeObj = {};
        timeObj.date = timeStamp(now - timeStr * i).date; //保存日期
        timeObj.timeStamp = now - timeStr * i; //保存时间戳
        timeObj.format = timeStamp(now - timeStr * i).format;
        timeObj.day = timeStamp(now - timeStr * i).day;
        timeObj.days = timeStamp(now - timeStr * i).days;
        timeObj.start = timeStamp(now - timeStr * i).start;
        timeObj.end = timeStamp(now - timeStr * i).end;
        timeObj.allDate = timeStamp(now - timeStr * i).allDate;
        timeObj.mounth = timeStamp(now - timeStr * i).mounth;

        if (i == 0) {
            timeObj.week = '今天';
        } else if (i == 1) {
            timeObj.week = '昨天';
        } else if (i == 2) {
            timeObj.week = '前天';
        } else {
            timeObj.week = timeStamp(now - timeStr * i).days;
        }
        time.unshift(timeObj);
    }
    return time;
}

//获取最小天数
function getMinData(num) {
    const date = new Date();
    const now = date.getTime(); //获取当前日期的时间戳
    let timeStr = 3600 * 24 * 1000; //一天的时间戳
    const timeObj = {};
    timeObj.date = timeStamp(now - timeStr * num).date; //保存日期
    timeObj.timeStamp = now - timeStr * num; //保存时间戳
    timeObj.format = timeStamp(now - timeStr * num).format;
    timeObj.day = timeStamp(now - timeStr * num).day;
    timeObj.days = timeStamp(now - timeStr * num).days;
    timeObj.start = timeStamp(now - timeStr * num).start;
    timeObj.end = timeStamp(now - timeStr * num).end;
    timeObj.allDate = timeStamp(now - timeStr * num).allDate;
    timeObj.mounth = timeStamp(now - timeStr * num).mounth;
    timeObj.week = timeStamp(now - timeStr * num).days;
    return timeObj;
}

// oss缩略图
function getOssCompressImg(url, width, height) {
    if (width && height) {
        return `${url}?x-oss-process=image/resize,m_lfit,w_${width},h_${height},limit_0`;
    } else {
        return `${url}?x-oss-process=image/resize,m_fill,h_200,w_200,limit_0`;
    }
}

// 分割出字符串中的手机号
function mobilePhoneArray(strContent) {
    if (strContent == '') {
        return;
    }
    var list = [];
    var mobileReg = /[1][3,4,5,7,6,8,9][0-9]{9}|\d{3,4}-\d{7,8}/g;
    var phoneList = strContent.match(mobileReg) || [];
    if (phoneList.length == 0) {
        list.push({ type: 'text', val: strContent });
        return list;
    }
    var textList = strContent.split(/[1][3,4,5,7,6,8,9][0-9]{9}|\d{3,4}-\d{7,8}/);
    for (var i in textList) {
        if (textList[i] == '') {
            i == 0 && list.push({ type: 'phone', val: phoneList[0] || '' });
        } else {
            list.push({ type: 'text', val: textList[i] });
            list.push({ type: 'phone', val: phoneList[i] || '' });
        }
    }
    return list;
}

function getDifferenceDay(startTime, endTime) {
    // 定义两个日期对象
    var startDate = new Date(startTime);
    var endDate = new Date(endTime);

    // 计算两个日期之间的毫秒差异
    var timeDifference = endDate.getTime() - startDate.getTime();

    // 将毫秒差异转换为天数
    var daysDifference = Math.ceil(timeDifference / (1000 * 60 * 60 * 24));

    // 输出结果
    console.log('相差的天数:', daysDifference);

    return daysDifference;
}

// 获取当前日期的上一个月
function getlastMonth() {
    let now = new Date();
    // 当前月的日期
    let nowDate = now.getDate();
    let lastMonth = new Date(now.getTime());
    // 设置上一个月（这里不需要减1）
    lastMonth.setMonth(lastMonth.getMonth());
    // 设置为0，默认为当前月的最后一天
    lastMonth.setDate(0);
    // 上一个月的天数
    let daysOflastMonth = lastMonth.getDate();
    // 设置上一个月的日期，如果当前月的日期大于上个月的总天数，则为最后一天
    lastMonth.setDate(nowDate > daysOflastMonth ? daysOflastMonth : nowDate);
    var lastTime = timeStamp(lastMonth).allDate;
    return lastTime;
}
// 上传文件
function uploadFile(path, name = 'upload', type = '') {
    uni.showLoading({
        title: '上传中',
    });
    const uploadFileUrl = 'https://friend.wdaoyun.cn/upload/uploadfile.php';
    // uni.compressImage({
    //     src: path,
    //     quality: 80,
    //     success: res => {
    //         console.log(res.tempFilePath);
    //     },
    // });
    return new Promise((resolve, reject) => {
        uni.uploadFile({
            url: uploadFileUrl,
            filePath: path,
            name: name,
            header: {
                'Content-Type': 'multipart/form-data',
                Authorization: '67947632acd5ac684574488b140b40400c74b695',
            },
            formData: {
                access_token: uni.getStorageSync('token'),
                type: type,
            },
            success: res => {
                if (res.statusCode == 200) {
                    // return JSON.parse(res.data).img_url
                    resolve(JSON.parse(res.data).data.img_url);
                } else {
                    uni.showToast({ title: '上传图片失败', icon: 'none' });
                    reject(false);
                }
            },
            complete(res) {
                console.log('uploadFileRes--complete----', JSON.parse(res.data));
                uni.hideLoading();
            },
        });
    });
}
// UTF8解码
function utf8Decode(str_data) {
    var tmp_arr = [],
        i = 0,
        ac = 0,
        c1 = 0,
        c2 = 0,
        c3 = 0;
    str_data += '';
    while (i < str_data.length) {
        c1 = str_data.charCodeAt(i);
        if (c1 < 128) {
            tmp_arr[ac++] = String.fromCharCode(c1);
            i++;
        } else if (c1 > 191 && c1 < 224) {
            c2 = str_data.charCodeAt(i + 1);
            tmp_arr[ac++] = String.fromCharCode(((c1 & 31) << 6) | (c2 & 63));
            i += 2;
        } else {
            c2 = str_data.charCodeAt(i + 1);
            c3 = str_data.charCodeAt(i + 2);
            tmp_arr[ac++] = String.fromCharCode(((c1 & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));
            i += 3;
        }
    }
    return tmp_arr.join('');
}
// ArrayBuffer转16进度字符串示例
function ab2hex(buffer) {
    const hexArr = Array.prototype.map.call(new Uint8Array(buffer), function (bit) {
        return ('00' + bit.toString(16)).slice(-2);
    });
    return hexArr.join('');
}
/**
 * 字符串转16进制
 * @param str 字符串
 */
function stringToHex(str) {
    var val = '';
    for (var i = 0; i < str.length; i++) {
        if (val == '') val = str.charCodeAt(i).toString(16);
        else val += str.charCodeAt(i).toString(16);
    }
    return val;
}
/**
 * 16进制转字符串
 * @param hex 16进制数
 */
function hexToStr(hex) {
    hex = hex.toString();
    let b = [];
    if (hex.length % 2 != 0) {
        return b;
    }
    for (let i = 0; i < hex.length; i += 2) {
        b.push(Math.floor(hex.substring(i, i + 2), 16));
    }
    return b;
}
// 判断时间大小
function compareDate(date1, date2) {
    var oDate1 = new Date(date1);
    var oDate2 = new Date(date2);
    if (oDate1.getTime() > oDate2.getTime()) {
        return true; //第一个大
    } else {
        return false; //第二个大
    }
}
// 判断版本号
function compareVersion(v1, v2) {
    v1 = v1.split('.');
    v2 = v2.split('.');
    const len = Math.max(v1.length, v2.length);

    while (v1.length < len) {
        v1.push('0');
    }
    while (v2.length < len) {
        v2.push('0');
    }

    for (let i = 0; i < len; i++) {
        const num1 = parseInt(v1[i]);
        const num2 = parseInt(v2[i]);

        if (num1 > num2) {
            return 1;
        } else if (num1 < num2) {
            return -1;
        }
    }

    return 0;
}

// 生成6个随机字节的16进制字符串秘钥
function generateRandomHexBytes(length) {
    // const bytes = new Uint8Array(length);
    // crypto.getRandomValues(bytes);
    // return Array.from(bytes, byte => byte.toString(16).padStart(2, '0')).join('');
    const characters = '0123456789ABCDEF';
    let result = '';

    for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length);
        result += characters.charAt(randomIndex);
    }

    return result;
}

// base64转换字符串
function decodeBase64(base64) {
    // console.log(base64)
    if (!base64) return '';
    const base64Data = base64; // 你的 Base64 字符串
    const arrayBuffer = wx.base64ToArrayBuffer(base64Data);
    const uint8Array = new Uint8Array(arrayBuffer);
    let str = '';
    for (let i = 0; i < uint8Array.length; i++) {
        str += String.fromCharCode(uint8Array[i]);
    }
    return str;
}

function getStartDate() {
    // 获取当前时间
    const currentDate = new Date();
    // 提取年、月、日、小时、分钟和秒
    const year = parseInt(currentDate.getFullYear().toString().slice(-2)).toString(16).padStart(2, '0');
    const month = parseInt((currentDate.getMonth() + 1).toString())
        .toString(16)
        .padStart(2, '0');
    const day = parseInt(currentDate.getDate().toString()).toString(16).padStart(2, '0');
    const hours = parseInt(currentDate.getHours().toString()).toString(16).padStart(2, '0');
    const minutes = parseInt(currentDate.getMinutes().toString()).toString(16).padStart(2, '0');
    const seconds = parseInt(currentDate.getSeconds().toString()).toString(16).padStart(2, '0');

    // 格式化为 "YY-MM-DD HH:MM:SS" 格式
    const formattedDate = `${year}${month}${day}${hours}${minutes}${seconds}`;
    return formattedDate;
}
function formatHexTime(value) {
    const currentDate = new Date(value);
    // 提取年、月、日、小时、分钟和秒
    const year = parseInt(currentDate.getFullYear().toString().slice(-2)).toString(16).padStart(2, '0');
    const month = parseInt((currentDate.getMonth() + 1).toString())
        .toString(16)
        .padStart(2, '0');
    const day = parseInt(currentDate.getDate().toString()).toString(16).padStart(2, '0');
    const hours = parseInt(currentDate.getHours().toString()).toString(16).padStart(2, '0');
    const minutes = parseInt(currentDate.getMinutes().toString()).toString(16).padStart(2, '0');
    const seconds = parseInt(currentDate.getSeconds().toString()).toString(16).padStart(2, '0');

    // 格式化为 "YY-MM-DD HH:MM:SS" 格式
    const formattedDate = `${year}${month}${day}${hours}${minutes}${seconds}`;
    return formattedDate;
}
function formatHexPsd(value) {
    var formattedDate = '';
    for (var i = 0; i < value.length; i++) {
        formattedDate =
            formattedDate +
            parseInt(value.substring(i, i + 1))
                .toString(16)
                .padStart(2, '0');
    }
    // 格式化为 "YY-MM-DD HH:MM:SS" 格式
    return formattedDate;
}
// 十进制数字转换为16进制
function decimalToHex(decimalNumber) {
    // 使用toString方法将十进制数转换为十六进制字符串
    let hexString = decimalNumber.toString(16);

    // 如果十六进制字符串长度小于2，就在前面补零
    if (hexString.length < 2) {
        hexString = '0' + hexString;
    }

    return hexString;
}

module.exports = {
    NumFormat: NumFormat,
    uploadFile: uploadFile,
    getCurrentDate: getCurrentDate,
    getCurrentFormat: getCurrentFormat,
    getCurrentHours: getCurrentHours,
    formatTimeStamp: formatTimeStamp,
    timeStampToDate: timeStampToDate,
    formatTimeString: formatTimeString,
    getOssCompressImg: getOssCompressImg,
    mobilePhoneArray: mobilePhoneArray,
    timeStamp: timeStamp,
    strFormat: strFormat,
    dateData: dateData,
    formatDate: formatDate,
    getMinData: getMinData,
    getlastMonth: getlastMonth,
    utf8Decode: utf8Decode,
    ab2hex: ab2hex,
    stringToHex: stringToHex,
    hexToStr: hexToStr,
    compareVersion: compareVersion,
    compareDate: compareDate,
    getTimeFormat: getTimeFormat,
    generateRandomHexBytes: generateRandomHexBytes,
    decimalToHex: decimalToHex,
    getStartDate: getStartDate,
    formatHexTime: formatHexTime,
    formatHexPsd: formatHexPsd,
    getDifferenceDay: getDifferenceDay,
    formatPrice: formatPrice,
    FormatHours: FormatHours,
    decodeBase64,
};
