<template>
    <view class="logistics-page">
        <!-- 订单信息 -->
        <view class="order-info-card" v-if="orderInfo.order_no">
            <view class="card-title">
                <u-icon name="file-text" size="28" color="#8966ef"></u-icon>
                <text>相关订单</text>
            </view>
            <view class="order-summary">
                <view class="order-no">订单号：{{ orderInfo.order_no }}</view>
                <view class="goods-info">
                    <view class="goods-image">
                        <u-image width="80rpx" height="80rpx" :src="orderInfo.good.cover_img || orderInfo.sku.image" mode="aspectFill" border-radius="8rpx"></u-image>
                    </view>
                    <view class="goods-detail">
                        <view class="goods-title">{{ orderInfo.good.title || '商品已删除' }}</view>
                        <view class="goods-price">¥{{ orderInfo.amount }} × {{ orderInfo.num }}</view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 物流基本信息 -->
        <view class="logistics-info-card">
            <view class="logistics-header">
                <view class="tracking-info">
                    <view class="tracking-no">快递单号：{{ trackingNo }}</view>
                    <view class="logistics-company">{{ logisticsInfo.expName || '快递公司' }}</view>
                </view>
                <view class="status-badge" :class="[getStatusClass(logisticsInfo.deliverystatus, logisticsInfo.issign)]">
                    {{ getStatusText(logisticsInfo.deliverystatus, logisticsInfo.issign) }}
                </view>
            </view>

            <view class="current-status" v-if="logisticsInfo.currentStatus">
                <view class="status-icon">
                    <u-icon :name="getStatusIcon(logisticsInfo.deliverystatus, logisticsInfo.issign)" size="40" :color="getStatusColor(logisticsInfo.deliverystatus, logisticsInfo.issign)"></u-icon>
                </view>
                <view class="status-detail">
                    <view class="status-text">{{ logisticsInfo.currentStatus }}</view>
                    <view class="status-time">{{ logisticsInfo.updateTime }}</view>
                    <view class="take-time" v-if="logisticsInfo.takeTime">预计送达：{{ logisticsInfo.takeTime }}</view>
                </view>
            </view>
        </view>

        <!-- 物流轨迹 -->
        <view class="logistics-timeline">
            <view class="timeline-title">
                <u-icon name="clock" size="28" color="#8966ef"></u-icon>
                <text>物流轨迹</text>
            </view>

            <view class="timeline-list">
                <view class="timeline-item" v-for="(item, index) in logisticsTrack" :key="index" :class="{ first: index === 0 }">
                    <view class="timeline-dot"></view>
                    <view class="timeline-content">
                        <view class="timeline-text">{{ item.status }}</view>
                        <view class="timeline-time">{{ item.time }}</view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="!loading && logisticsTrack.length === 0">
            <u-empty mode="car" text="暂无物流信息"></u-empty>
        </view>

        <!-- 操作按钮 -->
        <view class="action-buttons">
            <view class="action-btn secondary" @click="refreshLogistics">刷新物流</view>
            <view class="action-btn primary" @click="contactCustomer">联系客服</view>
        </view>
    </view>
</template>

<script>
import { getShopGoodOrderDetail, checkWuNo } from '@/utils/vmeitime-http/shop.js';

export default {
    data() {
        return {
            orderId: '',
            trackingNo: '',
            orderInfo: {},
            loading: false,

            // 物流信息
            logisticsInfo: {
                expName: '',
                deliverystatus: '1',
                issign: '0',
                currentStatus: '',
                updateTime: '',
                takeTime: '',
                expPhone: '',
                logo: '',
                type: ''
            },

            // 物流轨迹
            logisticsTrack: [],
        };
    },
    onLoad(options) {
        this.orderId = options.orderId;
        this.trackingNo = options.trackingNo;

        if (this.orderId) {
            this.loadOrderDetail();
        }

        if (this.trackingNo) {
            this.loadLogisticsInfo();
        }
    },
    methods: {
        // 加载订单详情
        async loadOrderDetail() {
            try {
                const res = await getShopGoodOrderDetail({
                    access_token: uni.getStorageSync('token'),
                    order_id: this.orderId,
                });

                if (res.status === 0) {
                    this.orderInfo = res.data.info || {};
                }
            } catch (error) {
                console.error('加载订单详情失败:', error);
            }
        },

        // 加载物流信息
        async loadLogisticsInfo() {
            if (!this.trackingNo || !this.orderId) {
                console.error('缺少必要参数：trackingNo 或 orderId');
                return;
            }

            try {
                this.loading = true;
                const res = await checkWuNo({
                    access_token: uni.getStorageSync('token'),
                    order_id: this.orderId,
                    wu_no: this.trackingNo,
                });

                if (res.status === 0 && res.data && res.data.info) {
                    const info = res.data.info;
                    this.logisticsInfo = {
                        expName: info.expName || '',
                        deliverystatus: info.deliverystatus || '1',
                        issign: info.issign || '0',
                        currentStatus: info.list && info.list.length > 0 ? info.list[0].status : '',
                        updateTime: info.updateTime || '',
                        takeTime: info.takeTime || '',
                        expPhone: info.expPhone || '',
                        logo: info.logo || '',
                        type: info.type || ''
                    };

                    // 设置物流轨迹
                    this.logisticsTrack = info.list || [];
                } else {
                    uni.showToast({
                        title: res.msg || '获取物流信息失败',
                        icon: 'none'
                    });
                }
            } catch (error) {
                console.error('加载物流信息失败:', error);
                uni.showToast({
                    title: '网络错误，请稍后重试',
                    icon: 'none'
                });
            } finally {
                this.loading = false;
            }
        },

        // 获取状态样式类
        getStatusClass(deliverystatus, issign) {
            if (issign === '1') {
                return 'status-delivered';
            } else if (deliverystatus === '1') {
                return 'status-shipping';
            } else {
                return 'status-exception';
            }
        },

        // 获取状态文本
        getStatusText(deliverystatus, issign) {
            if (issign === '1') {
                return '已签收';
            } else if (deliverystatus === '1') {
                return '运输中';
            } else {
                return '异常';
            }
        },

        // 获取状态图标
        getStatusIcon(deliverystatus, issign) {
            if (issign === '1') {
                return 'checkmark-circle';
            } else if (deliverystatus === '1') {
                return 'car';
            } else {
                return 'close-circle';
            }
        },

        // 获取状态颜色
        getStatusColor(deliverystatus, issign) {
            if (issign === '1') {
                return '#4cd964';
            } else if (deliverystatus === '1') {
                return '#8966ef';
            } else {
                return '#dc3545';
            }
        },

        // 刷新物流信息
        refreshLogistics() {
            uni.showLoading({ title: '刷新中...' });

            setTimeout(() => {
                this.loadLogisticsInfo();
                uni.hideLoading();
                uni.showToast({
                    title: '刷新成功',
                    icon: 'success',
                });
            }, 1000);
        },

        // 联系客服
        contactCustomer() {
            uni.showModal({
                title: '联系客服',
                content: '如有物流问题，请联系客服处理',
                showCancel: false,
            });
        },
    },
};
</script>

<style lang="scss" scoped>
// 引入项目主题色
$theme-primary: #8966ef;
$theme-primary-light: #a584f2;
$theme-secondary: #f0cc6c;
$bg-grey: #f8f8f8;
$border-color: #f0f0f0;
$text-color: #333;
$text-color-light: #666;
$text-color-placeholder: #999;

.logistics-page {
    min-height: 100vh;
    background-color: $bg-grey;
    padding-bottom: 120rpx;
}

.page-header {
    background: linear-gradient(135deg, #8966ef, #a584f2, #f0cc6c);
    padding: 40rpx 30rpx 30rpx;
    color: white;

    .header-title {
        font-size: 36rpx;
        font-weight: 600;
        margin-bottom: 8rpx;
    }

    .header-subtitle {
        font-size: 26rpx;
        opacity: 0.9;
    }
}

.logistics-info-card,
.logistics-timeline,
.order-info-card {
    background: white;
    border-radius: 16rpx;
    padding: 30rpx;
    margin: 20rpx;
    border: 1rpx solid $border-color;
}

.logistics-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24rpx;

    .tracking-info {
        flex: 1;

        .tracking-no {
            font-size: 28rpx;
            color: $text-color;
            font-weight: 500;
            margin-bottom: 8rpx;
        }

        .logistics-company {
            font-size: 26rpx;
            color: $text-color-light;
        }
    }

    .status-badge {
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        font-weight: 500;

        &.status-shipping {
            background: rgba(137, 102, 239, 0.1);
            color: $theme-primary;
        }

        &.status-delivered {
            background: rgba(76, 217, 100, 0.1);
            color: #4cd964;
        }

        &.status-exception {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }
    }
}

.current-status {
    display: flex;
    align-items: center;
    padding: 20rpx;
    background: #f8f9fa;
    border-radius: 12rpx;

    .status-icon {
        margin-right: 20rpx;
    }

    .status-detail {
        flex: 1;

        .status-text {
            font-size: 28rpx;
            color: $text-color;
            font-weight: 500;
            margin-bottom: 8rpx;
        }

        .status-time {
            font-size: 24rpx;
            color: $text-color-light;
        }

        .take-time {
            font-size: 22rpx;
            color: $text-color-placeholder;
            margin-top: 4rpx;
        }
    }
}

.timeline-title {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid #f5f5f5;

    text {
        margin-left: 12rpx;
        font-size: 30rpx;
        font-weight: 600;
        color: $text-color;
    }
}

.timeline-list {
    position: relative;

    &::before {
        content: '';
        position: absolute;
        left: 20rpx;
        top: 0;
        bottom: 0;
        width: 2rpx;
        background: #e9ecef;
    }
}

.timeline-item {
    position: relative;
    padding-left: 60rpx;
    margin-bottom: 30rpx;

    &:last-child {
        margin-bottom: 0;
    }

    &.first .timeline-dot {
        background: $theme-primary;
        border-color: $theme-primary;

        &::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 8rpx;
            height: 8rpx;
            background: white;
            border-radius: 50%;
        }
    }

    .timeline-dot {
        position: absolute;
        left: -20rpx;
        top: 8rpx;
        width: 20rpx;
        height: 20rpx;
        background: white;
        border: 2rpx solid #e9ecef;
        border-radius: 50%;
        z-index: 1;
    }

    .timeline-content {
        .timeline-text {
            font-size: 28rpx;
            color: $text-color;
            line-height: 1.5;
            margin-bottom: 8rpx;
        }

        .timeline-time {
            font-size: 24rpx;
            color: $text-color-light;
            margin-bottom: 4rpx;
        }

        .timeline-location {
            font-size: 22rpx;
            color: $text-color-placeholder;
        }
    }
}

.card-title {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid #f5f5f5;

    text {
        margin-left: 12rpx;
        font-size: 30rpx;
        font-weight: 600;
        color: $text-color;
    }
}

.order-summary {
    .order-no {
        font-size: 26rpx;
        color: $text-color-light;
        margin-bottom: 16rpx;
    }

    .goods-info {
        display: flex;
        align-items: center;

        .goods-image {
            margin-right: 20rpx;
        }

        .goods-detail {
            flex: 1;

            .goods-title {
                font-size: 28rpx;
                color: $text-color;
                font-weight: 500;
                margin-bottom: 8rpx;
                line-height: 1.4;
            }

            .goods-price {
                font-size: 26rpx;
                color: #ff4757;
                font-weight: 500;
            }
        }
    }
}

.empty-state {
    padding: 100rpx 0;
}

.action-buttons {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    padding: 20rpx 30rpx;
    border-top: 1rpx solid $border-color;
    box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 20rpx;

    .action-btn {
        flex: 1;
        padding: 20rpx 0;
        border-radius: 24rpx;
        font-size: 30rpx;
        font-weight: 500;
        text-align: center;
        border: 1rpx solid;
        transition: all 0.3s ease;

        &.primary {
            background: $theme-primary;
            color: white;
            border-color: $theme-primary;

            &:active {
                background: darken($theme-primary, 10%);
            }
        }

        &.secondary {
            background: white;
            color: $theme-primary;
            border-color: $theme-primary;

            &:active {
                background: rgba(137, 102, 239, 0.1);
            }
        }
    }
}
</style>
