<template>
    <view class="content">
        <navigator class="address-box white-box" :url="`/views/mine/addressList?id=${addressMsg.id}`" v-if="addressMsg.id">
            <view class="box-left">
                <view class="title">
                    <text class="name">{{ addressMsg.name }}</text>
                    <text class="phone">{{ addressMsg.phone }}</text>
                </view>
                <view class="address-text u-line-2">
                    {{ (addressMsg.province_txt || '') + (addressMsg.city_txt || '') + (addressMsg.area_txt || '') + (addressMsg.address || '') }}
                </view>
            </view>
            <view class="box-right"><text class="iconfont">&#xe74a;</text></view>
        </navigator>
        <navigator class="box-style add-address" url="/views/mine/addressList" v-else>
            <view class="left">
                <text class="iconfont">&#xe606;</text>
                <text>添加收货地址</text>
            </view>
            <view class="right iconfont">&#xe74a;</view>
        </navigator>

        <view class="main-box">
            <view class="box-style main-item">
                <view class="goods-list">
                    <view class="goods-item">
                        <view class="goods-info">
                            <view class="goods-info-l">
                                <image v-if="goodImg" :src="goodImg"></image>
                            </view>
                            <view class="goods-info-r">
                                <view class="goods-info-r-name">{{ goodsDetail.name || '商品名称' }}</view>
                                <view class="goods-info-r-detail">
                                    <view class="goods-info-r-detail-price" v-if="goodsDetail.child && goodsDetail.child[childIndex] && goodsDetail.child[childIndex].price">
                                        ¥{{ goodsDetail.child[childIndex].price }}
                                    </view>
                                    <view class="goods-info-r-detail-num">×{{ num }}</view>
                                </view>
                            </view>
                        </view>
                        <view class="cell-box">
                            <view class="cell-item">
                                <view class="ci-left">型号</view>
                                <view class="ci-right">{{ goodsDetail.child && goodsDetail.child[childIndex] ? goodsDetail.child[childIndex].name : '默认规格' }}</view>
                            </view>
                            <view class="cell-item">
                                <view class="ci-left">邮费</view>
                                <view class="ci-right" :style="isPostage ? 'color: #ed3f14' : ''">{{ isPostage ? '¥' + postage : '包邮' }}</view>
                            </view>
                            <view class="cell-item">
                                <view class="ci-left">优惠卷</view>
                                <view class="ci-right" v-if="couponPrice == 0">暂无商品优惠卷</view>
                                <view class="ci-right" style="color: #ed3f14" v-else>-¥{{ couponPrice }}</view>
                            </view>
                        </view>
                        <view class="cell-box bt">
                            <view class="cell-item">
                                <view class="ci-left">订单总金额</view>
                                <view class="ci-right">¥{{ totalPrice }}</view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <view class="title">订单备注</view>
            <view class="remark-input">
                <textarea class="textarea" v-model="note" placeholder="选填，可备注商品偏好" maxlength="100" />
            </view>
        </view>

        <view style="height: 240rpx"></view>
        <view class="pay-btn" @click="commitOrder">
            <view class="pay-btn-l">
                <text>共{{ num }}件，合计</text>
                <text class="pay-btn-l-price">¥{{ totalPrice }}</text>
            </view>
            <view class="pay-btn-r">下单</view>
        </view>
    </view>
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex';
export default {
    components: {},
    computed: {
        ...mapState(['userInfo', 'shareData']),
        isPostage() {
            if (this.addressMsg && this.postageList && this.postageList.length > 0) {
                // 构建地区字符串用于判断
                const areaString = this.addressMsg.areacode || (this.addressMsg.province_name || '') + (this.addressMsg.city_name || '') + (this.addressMsg.area_name || '');

                if (areaString) {
                    return this.postageList.some(area => area && areaString.includes(area));
                }
            }
            return false;
        },
        totalPrice() {
            let total = 0;
            if (this.goodsDetail.child && this.goodsDetail.child.length > 0 && this.goodsDetail.child[this.childIndex]) {
                // 计算商品价格 × 数量
                const price = Number(this.goodsDetail.child[this.childIndex].price || 0);
                total = this.num * price;
                // 减去优惠券金额
                total = total - Number(this.couponPrice || 0);
                // 如果需要邮费，加上邮费
                if (this.isPostage) {
                    total = total + Number(this.postage || 0);
                }
                // 确保总价不为负数
                total = Math.max(0, total);
            }
            return total.toFixed(2);
        },
    },
    data() {
        return {
            title: '',
            addressMsg: {},
            goodsDetail: {
                id: '',
                name: '',
                images: '',
                content: '',
                postage: 0,
                address_keword: '',
                child: [],
            },
            goodImg: '',
            isCommit: true,
            orderRemark: '', // 订单备注
            posterSrc: '',
            num: 1,
            couponPrice: 0,
            childIndex: 0,
            id: '',
            channelCode: 'inside',
            note: '',
            postage: 0,
            postageList: [],
        };
    },
    onLoad(option) {
        this.id = option.id || '';
        this.childIndex = parseInt(option.child || 0);
        this.num = parseInt(option.num || 1);
        console.log(option);
        // 确保参数有效
        if (!this.id) {
            uni.showToast({
                title: '商品ID不能为空',
                icon: 'none',
            });
            setTimeout(() => {
                uni.navigateBack();
            }, 800);
            return;
        }

        this.getGoodsInfo(this.id, this.childIndex);
        this.getAddress();
        uni.$on('updateAddressMsg', res => {
            console.log(res);
            this.addressMsg = res;
        });
    },

    onShareAppMessage: function (option) {
        this.showSharePanel = false;
        let shareObj = {};
        return shareObj;
    },

    onUnload() {
        uni.$off('updateAddressList');
    },

    methods: {
        ...mapMutations([]),
        ...mapActions(['getOpenIdForCode']),

        // 操作数量
        compressGoodsNum(item, num) {
            if (num == -1 && item == 1) {
                uni.showToast({
                    title: '数量至少为1',
                    icon: 'none',
                });
                return;
            }
            this.num += num;
            // 不需要调用countPrice，因为totalPrice是computed属性会自动计算
        },
        // 获取商品列表
        async getGoodsInfo(id, index) {
            let res = await this.$api.getUserGoodDetail({
                access_token: uni.getStorageSync('token'),
                good_id: id,
            });

            if (res.status === 0 && res.data && res.data.good) {
                // 处理新的数据结构
                const goodData = res.data.good;
                // 解析图片数据
                let images = [];
                try {
                    images = JSON.parse(goodData.images || '[]');
                } catch (e) {
                    console.error('解析图片数据失败:', e);
                    images = [];
                }

                // 构建兼容的商品数据结构
                this.goodsDetail = {
                    id: goodData.id,
                    name: goodData.title,
                    images: goodData.cover_img,
                    content: goodData.content,
                    postage: goodData.postage || 0,
                    address_keword: goodData.address_keword || '',
                    child: goodData.sku_list.map((sku, idx) => ({
                        id: sku.id || idx,
                        name: sku.sku_name,
                        price: sku.price,
                        org_price: sku.org_price,
                        image: sku.image,
                    })),
                };

                console.log(this.goodsDetail);

                // 设置商品图片
                if (this.goodsDetail.child[this.childIndex] && this.goodsDetail.child[this.childIndex].image) {
                    this.goodImg = this.goodsDetail.child[this.childIndex].image;
                } else if (images.length > 0) {
                    this.goodImg = images[0];
                } else {
                    this.goodImg = this.goodsDetail.images;
                }

                // 处理邮费相关
                this.postage = this.goodsDetail.postage;
                if (this.goodsDetail.address_keword) {
                    this.postageList = this.goodsDetail.address_keword.split('|');
                } else {
                    this.postageList = [];
                }
            } else {
                uni.showToast({
                    title: '商品不存在',
                    icon: 'none',
                });
                setTimeout(() => {
                    uni.navigateBack();
                }, 800);
            }
        },
        async getAddress() {
            const res = await this.$api.getUserAddressList({
                access_token: uni.getStorageSync('token'),
                page: 1,
                pagesize: 10,
            });

            // 查找默认地址或使用第一个地址
            const defaultAddress = res.data.list.find(addr => addr.is_def == 1);

            this.addressMsg = defaultAddress || res.data.list[0];

            // 构建地区代码字符串用于邮费判断
            if (this.addressMsg.province_name && this.addressMsg.city_name && this.addressMsg.area_name) {
                this.addressMsg.areacode = this.addressMsg.province_name + this.addressMsg.city_name + this.addressMsg.area_name;
            }
            console.log(this.addressMsg);
        },

        // 提交订单
        async commitOrder() {
            if (typeof this.addressMsg.id == 'undefined') {
                uni.showModal({
                    title: '提示',
                    content: '请添加地址后在进行购买',
                    showCancel: false,
                });
                return;
            }

            // 检查SKU是否存在
            if (!this.goodsDetail.child || !this.goodsDetail.child[this.childIndex]) {
                uni.showToast({
                    title: '商品规格不存在',
                    icon: 'none',
                });
                return;
            }

            uni.showLoading({
                title: '购买中',
            });
            try {
                // 使用新的API创建用户商品订单
                const order = await this.$api.createUserGoodOrder({
                    access_token: uni.getStorageSync('token'),
                    tj_user_id: this.shareData.shareUid || '', // 推荐人ID，如果有的话
                    good_sku_id: this.goodsDetail.child[this.childIndex].id,
                    user_address_id: this.addressMsg.id,
                    num: this.num,
                    buy_note: this.note,
                });
                // 调用支付接口
                const payRes = await this.$api.createMiniPay({
                    openid: this.userInfo.openid,
                    orderid: order.data.order_no,
                    access_token: uni.getStorageSync('token'),
                });
                const paymentData = payRes.data;
                // 调用微信支付
                wx.requestPayment({
                    timeStamp: paymentData.timeStamp,
                    nonceStr: paymentData.nonceStr,
                    package: paymentData.package,
                    signType: paymentData.signType,
                    paySign: paymentData.paySign,
                    success: res => {
                        uni.hideLoading();
                        uni.showToast({
                            title: '支付成功',
                            icon: 'success',
                        });
                        uni.redirectTo({
                            url: `/views/shop/myOrders`,
                        });
                    },
                    fail: err => {
                        uni.redirectTo({
                            url: `/views/shop/myOrders`,
                        });
                        uni.hideLoading();
                        uni.showToast({
                            title: '支付失败，请重试',
                            icon: 'none',
                        });
                    },
                });
            } catch (error) {
                uni.hideLoading();
                uni.showToast({
                    title: '支付失败，请重试',
                    icon: 'none',
                });
            }
        },
        // 调用微信支付
        async requestPayment(paymentData) {
            return new Promise((resolve, reject) => {
                uni.requestPayment({
                    provider: 'wxpay',
                    timeStamp: paymentData.timeStamp,
                    nonceStr: paymentData.nonceStr,
                    package: paymentData.package,
                    signType: paymentData.signType,
                    paySign: paymentData.paySign,
                    success: res => {
                        console.log('支付成功:', res);
                        resolve(res);
                    },
                    fail: err => {
                        console.error('支付失败:', err);
                        reject(new Error('支付失败'));
                    },
                });
            });
        },
    },
};
</script>

<style lang="scss" scoped>
// 引入项目主题色
$theme-primary: #8966ef;
$theme-primary-light: #a584f2;
$theme-secondary: #f0cc6c;
$bg-grey: #f8f8f8;
$border-color: #f0f0f0;
$text-color: #333;
$text-color-light: #666;
$text-color-placeholder: #999;

.content {
    padding: 0 20rpx;
    box-sizing: border-box;
    background: $bg-grey;
    min-height: 100vh;
}

.box-style {
    width: 100%;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 0 20rpx;
    box-sizing: border-box;
    border: 1rpx solid $border-color;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;

    &:active {
        transform: translateY(2rpx);
        box-shadow: 0 1rpx 8rpx rgba(0, 0, 0, 0.06);
    }
}

.name-box {
    height: 120rpx;
    line-height: 120rpx;
    margin: 20rpx 0;
    font-size: 34rpx;
    color: $text-color;
    font-weight: 600;
}

.add-address {
    height: 120rpx;
    margin: 20rpx 0;
    font-size: 28rpx;
    color: $text-color;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;

    &::before {
        content: '';
        position: absolute;
        left: 20rpx;
        top: 50%;
        transform: translateY(-50%);
        width: 6rpx;
        height: 40rpx;
        background: linear-gradient(135deg, $theme-primary, $theme-primary-light);
        border-radius: 3rpx;
    }

    .iconfont {
        font-size: 28rpx;
        color: $theme-primary;
    }

    .left {
        display: flex;
        align-items: center;
        padding-left: 30rpx;

        .iconfont {
            margin-right: 12rpx;
            font-size: 32rpx;
        }
    }

    .right {
        color: $text-color-light;
    }
}

.address-box {
    height: 150rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #ffffff;
    padding: 0 30rpx;
    margin: 20rpx 0;
    position: relative;

    .title .name {
        font-size: 32rpx;
        font-weight: 600;
        margin-right: 20rpx;
        color: $text-color;
    }

    .title .phone {
        color: $text-color-light;
        font-size: 28rpx;
    }

    .address-text {
        max-width: 620rpx;
        margin-top: 10rpx;
        color: $text-color-light;
        font-size: 26rpx;
        line-height: 1.4;
    }

    .box-right {
        .iconfont {
            color: $text-color-light;
            font-size: 24rpx;
        }
    }
}

.main-box {
    .title {
        margin: 20rpx 0;
        font-size: 32rpx;
        font-weight: 600;
        color: $text-color;
        position: relative;
        padding-left: 20rpx;

        &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4rpx;
            height: 28rpx;
            background: linear-gradient(135deg, $theme-primary, $theme-primary-light);
            border-radius: 2rpx;
        }
    }

    .main-item {
        padding: 30rpx;
        overflow: hidden;

        .top-hint {
            width: 100%;
            height: 66rpx;
            color: $theme-secondary;
            background: linear-gradient(135deg, rgba(240, 204, 108, 0.1), rgba(240, 204, 108, 0.05));
            border: 1rpx solid rgba(240, 204, 108, 0.3);
            border-radius: 12rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20rpx;
            box-sizing: border-box;
            margin-bottom: 20rpx;
            font-size: 28rpx;

            .iconfont {
                font-size: 28rpx;
            }

            .left .iconfont {
                margin-right: 8rpx;
            }
        }

        .goods-item {
            overflow: hidden;

            .goods-info {
                display: flex;
                margin-bottom: 20rpx;

                &-l {
                    width: 172rpx;
                    height: 172rpx;
                    border-radius: 16rpx;
                    overflow: hidden;
                    border: 1rpx solid $border-color;

                    image {
                        width: 100%;
                        height: 100%;
                        border-radius: 16rpx;
                    }
                }

                &-r {
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    margin-left: 30rpx;
                    flex: 1;

                    &-name {
                        font-size: 30rpx;
                        font-weight: 500;
                        color: $text-color;
                        line-height: 1.4;
                        margin-bottom: 20rpx;
                    }

                    &-detail {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        &-price {
                            color: $theme-primary;
                            font-size: 36rpx;
                            font-weight: 600;
                        }

                        &-num {
                            font-size: 28rpx;
                            font-weight: 400;
                            color: $text-color-light;
                        }
                    }
                }
            }

            .cell-box {
                margin-top: 30rpx;
                background: rgba(137, 102, 239, 0.02);
                border-radius: 12rpx;
                padding: 20rpx;

                &.bt {
                    border: 1rpx solid $theme-primary;
                    background: linear-gradient(135deg, rgba(137, 102, 239, 0.05), rgba(137, 102, 239, 0.02));
                    margin-top: 40rpx;
                }

                .cell-item {
                    height: 80rpx;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    font-size: 30rpx;
                    color: $text-color;
                    border-bottom: 1rpx solid rgba(240, 240, 240, 0.5);

                    &:last-child {
                        border-bottom: none;
                    }

                    .ci-left {
                        font-weight: 500;
                    }

                    .ci-right {
                        max-width: 75%;
                        color: $text-color-light;
                        font-weight: 400;

                        &[style*='color: #ed3f14'] {
                            color: $theme-primary !important;
                            font-weight: 600;
                        }
                    }
                }
            }

            .gi-bottom {
                display: flex;
                flex-direction: row-reverse;
            }

            .compress-box {
                width: 190rpx;
                height: 50rpx;
                border: 2rpx solid $border-color;
                border-radius: 25rpx;
                line-height: 50rpx;
                display: flex;
                justify-content: space-between;
                margin-top: 20rpx;
                background: #ffffff;

                .compress-btn {
                    color: $theme-primary;
                    width: 50rpx;
                    height: 50rpx;
                    text-align: center;
                    font-weight: 600;

                    &:active {
                        background: rgba(137, 102, 239, 0.1);
                        border-radius: 50%;
                    }
                }

                .compress-num {
                    width: 90rpx;
                    text-align: center;
                    border-right: 1rpx solid $border-color;
                    border-left: 1rpx solid $border-color;
                    box-sizing: border-box;
                    color: $text-color;
                    font-weight: 500;
                }
            }
        }
    }

    .remark-input {
        background: #ffffff;
        border-radius: 16rpx;
        border: 1rpx solid $border-color;
        box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);

        .textarea {
            width: 100%;
            height: 200rpx;
            border-radius: 16rpx;
            padding: 30rpx;
            box-sizing: border-box;
            font-size: 28rpx;
            color: $text-color;
            line-height: 1.5;

            &::placeholder {
                color: $text-color-placeholder;
            }
        }
    }
}

.pay-btn {
    width: 750rpx;
    height: 150rpx;
    background: #ffffff;
    font-size: 32rpx;
    color: $text-color;
    position: fixed;
    bottom: 0rpx;
    left: 0rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 30rpx 30rpx;
    border-top: 1rpx solid $border-color;
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
    z-index: 999;

    // 安全区域适配
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);

    &-l {
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        text {
            font-size: 26rpx;
            color: $text-color-light;
            margin-bottom: 4rpx;
        }

        &-price {
            color: $theme-primary;
            font-size: 42rpx;
            font-weight: 700;
        }
    }

    &-r {
        width: 200rpx;
        height: 88rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, $theme-primary, $theme-primary-light);
        border-radius: 44rpx;
        color: #ffffff;
        font-weight: 600;
        font-size: 32rpx;
        box-shadow: 0 4rpx 16rpx rgba(137, 102, 239, 0.3);
        transition: all 0.3s ease;

        &:active {
            transform: translateY(2rpx);
            box-shadow: 0 2rpx 12rpx rgba(137, 102, 239, 0.4);
            background: linear-gradient(135deg, darken($theme-primary, 5%), $theme-primary);
        }
    }
}
</style>
