<template>
    <view class="follow-list-container">
        <view class="empty-state" v-if="followList.length === 0">
            <view class="empty-content">
                <image class="empty-image" src="https://static.wdaoyun.com/friend/2025-07-02/ef0fac060cb6f1abfde647fe1a55776d.png" mode="aspectFit"></image>
                <view class="empty-tip">暂无关注记录~</view>
            </view>
        </view>

        <view class="follow-item" v-for="(item, index) in followList" :key="index">
            <view class="avatar-wrapper">
                <u-avatar :src="getUserAvatar(item)" size="108"></u-avatar>
            </view>
            <view class="follow-content">
                <view class="top-info">
                    <text class="username">{{ getUserName(item) }}</text>
                </view>
                <view class="follow-description">
                    <text class="description">开始关注你 {{ formatFollowTime(item.addtime) }}</text>
                </view>
            </view>
            <view class="action-area">
                <view class="action-btns">
                    <button class="btn follow-back" v-if="item.follow_type === 1" @tap="handleFollowBack(item)">回关</button>
                    <button class="btn chat" @tap="gotoChat(item)">私聊</button>
                </view>
            </view>
        </view>
        <vip-popup v-model="showVip" @single-pay="handleSinglePay" @member-pay="handleMemberPay" @close="handleClose"></vip-popup>
        <wechat-info-popup v-model="showWechatInfoPopup" :user-info="userInfo" @close="handleWechatInfoClose"></wechat-info-popup>
    </view>
</template>

<script>
import { mapState } from 'vuex';
import vipPopup from '@/components/vip-popup.vue';

export default {
    components: {
        vipPopup,
    },
    data() {
        return {
            showVip: false,
            page: 1,
            pagesize: 10,
            total: 0,
            followList: [],
            currentUid: '', // 当前用户ID
        };
    },
    computed: {
        ...mapState(['userInfo']),
        // 判断是否为有效会员
        isValidMember() {
            if (!this.userInfo || !this.userInfo.member_time) {
                return false;
            }

            // 如果member_time为0或空字符串，表示非会员
            if (this.userInfo.member_time === '0' || this.userInfo.member_time === 0 || this.userInfo.member_time === '') {
                return false;
            }

            // 将member_time转换为时间戳进行比较
            let memberExpireTime;

            // 如果member_time是时间戳格式（数字）
            if (typeof this.userInfo.member_time === 'number' || /^\d+$/.test(this.userInfo.member_time)) {
                memberExpireTime = parseInt(this.userInfo.member_time) * 1000; // 转换为毫秒
            }
            // 如果member_time是日期字符串格式
            else if (typeof this.userInfo.member_time === 'string') {
                memberExpireTime = new Date(this.userInfo.member_time.replace(/-/g, '/')).getTime();
            } else {
                return false;
            }

            // 比较当前时间和会员到期时间
            const currentTime = new Date().getTime();
            return memberExpireTime > currentTime;
        },
    },
    onLoad() {
        // 获取当前用户ID
        this.currentUid = this.userInfo.uid || '';
        this.getFollowList();
    },
    onReachBottom() {
        if (this.followList.length < this.total) {
            this.page++;
            this.getFollowList();
        }
    },
    onPullDownRefresh() {
        this.page = 1;
        this.getFollowList();
        uni.stopPullDownRefresh();
    },
    methods: {
        async getFollowList() {
            try {
                const res = await this.$api.getFollowMsgList({
                    page: this.page,
                    pagesize: this.pagesize,
                    access_token: uni.getStorageSync('token'),
                });

                if (res && res.data && res.data.list) {
                    if (this.page === 1) {
                        this.followList = res.data.list;
                    } else {
                        this.followList = [...this.followList, ...res.data.list];
                    }
                    this.total = parseInt(res.data.total || 0);
                }
            } catch (error) {
                console.error('获取关注列表失败', error);
                uni.showToast({
                    title: '获取关注列表失败',
                    icon: 'none',
                });
            }
        },
        async getCommentMsg() {
            this.$api.getCommentMsg({
                page: this.page,
                pagesize: this.pagesize,
                access_token: uni.getStorageSync('token'),
            });
            this.$api.getLikeMsg({
                page: this.page,
                pagesize: this.pagesize,
                access_token: uni.getStorageSync('token'),
            });
        },
        // 获取用户头像
        getUserAvatar(item) {
            return item.user && item.user.headimgurl ? item.user.headimgurl : '/static/images/we.png';
        },
        // 获取用户名称
        getUserName(item) {
            return item.user && item.user.nickname ? item.user.nickname : '未知用户';
        },
        handleLike() {
            // 处理点赞收藏事件
            uni.showToast({
                title: '点击了点赞收藏',
                icon: 'none',
            });
        },
        handleFollow() {
            // 处理新增关注事件
            uni.showToast({
                title: '点击了新增关注',
                icon: 'none',
            });
        },
        handleComment() {
            // 处理评论和@事件
            uni.showToast({
                title: '点击了评论和@',
                icon: 'none',
            });
        },
        formatFollowTime(timestamp) {
            if (!timestamp) return '';

            // 处理时间戳或日期字符串
            let followDate;
            if (typeof timestamp === 'string') {
                followDate = new Date(timestamp.replace(/-/g, '/'));
            } else {
                followDate = new Date(timestamp);
            }

            const now = new Date();

            // 格式化时间显示
            const year = followDate.getFullYear();
            const month = (followDate.getMonth() + 1).toString().padStart(2, '0');
            const day = followDate.getDate().toString().padStart(2, '0');
            const hours = followDate.getHours().toString().padStart(2, '0');
            const minutes = followDate.getMinutes().toString().padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}`;
        },
        // 处理回关操作
        async handleFollowBack(item) {
            try {
                const res = await this.$api.followUser({
                    follow_user_id: item.user.uid,
                    access_token: uni.getStorageSync('token'),
                });

                if (res && res.status === 0) {
                    // 更新本地数据
                    item.follow_type = 2; // 更新为互相关注
                    uni.showToast({
                        title: '回关成功',
                        icon: 'success',
                    });
                }
            } catch (error) {
                console.error('回关失败', error);
                uni.showToast({
                    title: '回关失败',
                    icon: 'none',
                });
            }
        },
        // 跳转到聊天页面
        async gotoChat(item) {
            const chatTarget = item.user;

            if (!chatTarget) {
                uni.showToast({
                    title: '聊天对象信息不完整',
                    icon: 'none',
                });
                return;
            }
            if (!this.isValidMember) {
                this.showVip = true;
                return;
            }
            const res = await this.$api.initSession({
                access_token: uni.getStorageSync('token'),
                to_uid: chatTarget.uid,
            });
            const sessionId = res.data.msg_session_id;
            // 导航到聊天页面
            uni.navigateTo({
                url: `/views/moments/chat?sessionId=${sessionId}&userId=${chatTarget.uid}&username=${encodeURIComponent(chatTarget.nickname)}&avatar=${encodeURIComponent(
                    chatTarget.headimgurl || ''
                )}`,
            });
        },

        // 处理单次支付
        async handleSinglePay(paymentData) {
            console.log('单次支付:', paymentData);

            try {
                uni.showLoading({
                    title: '处理中...',
                });

                // 这里应该调用单次付费的API
                // 由于没有具体的单次付费API，暂时模拟成功
                await new Promise(resolve => setTimeout(resolve, 2000));

                uni.hideLoading();
                uni.showToast({
                    title: '支付成功',
                    icon: 'success',
                });

                // 支付成功后显示微信号
                setTimeout(() => {
                    this.showWechatInfo();
                }, 1500);
            } catch (error) {
                console.error('单次支付失败:', error);
                uni.hideLoading();
                uni.showToast({
                    title: '支付失败，请重试',
                    icon: 'none',
                });
            }
        },

        // 处理会员支付
        async handleMemberPay(paymentData) {
            console.log('会员支付:', paymentData);

            try {
                uni.showLoading({
                    title: '处理中...',
                });

                // 创建会员订单
                const orderRes = await this.$api.createMemberOrder({
                    good_id: paymentData.product.id,
                    num: 1,
                    access_token: uni.getStorageSync('token'),
                });

                if (orderRes.status !== 0) {
                    throw new Error(orderRes.msg || '创建订单失败');
                }

                // 获取用户openid（小程序支付需要）
                const openid = this.userInfo.openid;
                if (!openid) {
                    throw new Error('获取用户信息失败，请重新登录');
                }

                // 调用支付接口
                const payRes = await this.$api.createMiniPay({
                    openid: openid,
                    orderid: orderRes.data.order_no,
                    access_token: uni.getStorageSync('token'),
                });

                if (payRes.status !== 0) {
                    throw new Error(payRes.msg || '支付失败');
                }

                // 调用微信支付
                await this.requestPayment(payRes.data);

                uni.hideLoading();
                uni.showToast({
                    title: '开通成功',
                    icon: 'success',
                });

                // 更新用户信息
                await this.VAgetUser();

                // 支付成功后显示微信号
                setTimeout(() => {
                    this.showWechatInfo();
                }, 1500);
            } catch (error) {
                console.error('会员支付失败:', error);
                uni.hideLoading();
                uni.showToast({
                    title: error.message || '支付失败，请重试',
                    icon: 'none',
                });
            }
        },

        // 调用微信支付
        async requestPayment(paymentData) {
            return new Promise((resolve, reject) => {
                uni.requestPayment({
                    provider: 'wxpay',
                    timeStamp: paymentData.timeStamp,
                    nonceStr: paymentData.nonceStr,
                    package: paymentData.package,
                    signType: paymentData.signType,
                    paySign: paymentData.paySign,
                    success: res => {
                        console.log('支付成功:', res);
                        resolve(res);
                    },
                    fail: err => {
                        console.error('支付失败:', err);
                        reject(new Error('支付失败'));
                    },
                });
            });
        },

        handleClose() {
            this.showVip = false;
        },

    },
};
</script>

<style lang="scss" scoped>
.follow-list-container {
    min-height: 100vh;
    overflow: hidden;
   background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);
    padding-bottom: 30rpx;
    .action-buttons {
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding: 15px;
        background-color: #ffffff;
        border-bottom: 1px solid #f2f2f2;

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .icon-wrapper {
                width: 108rpx;
                height: 108rpx;
                border-radius: 16rpx;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .btn-text {
                margin-top: 10px;
                font-size: 14px;
                color: #666666;
            }
        }
    }

    .no-data {
        height: 300rpx;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .follow-item {
        display: flex;
        padding: 24rpx;
        align-items: center;
        background-color: #ffffff;
        border-bottom: 1px solid #f2f2f2;
        margin: 20rpx 20rpx 0;
        .avatar-wrapper {
            position: relative;
            margin-right: 24rpx;
        }

        .follow-content {
            flex: 1;
            overflow: hidden;

            .top-info {
                display: flex;
                justify-content: space-between;
                margin-bottom: 12rpx;

                .username {
                    font-size: 32rpx;
                    color: #333333;
                    font-weight: 400;
                }
            }

            .follow-description {
                .description {
                    font-size: 26rpx;
                    color: #666666;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                }
            }
        }

        .action-area {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            justify-content: center;

            .action-btns {
                display: flex;
                flex-direction: column;

                .btn {
                    margin: 5rpx;
                    padding: 10rpx 20rpx;
                    font-size: 24rpx;
                    border-radius: 30rpx;
                    line-height: 1.5;
                    text-align: center;
                    min-width: 100rpx;
                    height: auto;
                }

                .follow-back {
                    background: linear-gradient(90deg, #3498db, #3498db);
                    color: #ffffff;
                }

                .chat {
                    background: linear-gradient(90deg, #ff5733, #ff5733);
                    color: #ffffff;
                }
            }
        }
    }
}

.empty-state {
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    text-align: center;

    .empty-content {
        // margin: 100rpx 30rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .empty-image {
            width: 400rpx;
            height: 314rpx;
            margin-bottom: 40rpx;
        }

        .empty-text {
            color: #999;
            font-size: 32rpx;
            margin-bottom: 16rpx;
            text-align: center;
        }

        .empty-tip {
            color: #8966ef;
            font-size: 28rpx;
            text-align: center;
        }
    }
}
</style>
