/**
 * 会员相关工具函数
 */

/**
 * 检查用户是否为有效会员
 * @param {Object} userInfo - 用户信息对象
 * @returns {boolean} 是否为有效会员
 */
export function isValidMember(userInfo) {
    if (!userInfo || !userInfo.member_time) {
        return false;
    }
    
    // 如果member_time为0或空字符串，表示非会员
    if (userInfo.member_time === '0' || userInfo.member_time === 0 || userInfo.member_time === '') {
        return false;
    }
    
    // 将member_time转换为时间戳进行比较
    let memberExpireTime;
    
    // 如果member_time是时间戳格式（数字）
    if (typeof userInfo.member_time === 'number' || /^\d+$/.test(userInfo.member_time)) {
        memberExpireTime = parseInt(userInfo.member_time) * 1000; // 转换为毫秒
    } 
    // 如果member_time是日期字符串格式
    else if (typeof userInfo.member_time === 'string') {
        memberExpireTime = new Date(userInfo.member_time.replace(/-/g, '/')).getTime();
    } 
    else {
        return false;
    }
    
    // 比较当前时间和会员到期时间
    const currentTime = new Date().getTime();
    return memberExpireTime > currentTime;
}

/**
 * 获取会员到期时间的格式化字符串
 * @param {Object} userInfo - 用户信息对象
 * @returns {string} 格式化的到期时间字符串
 */
export function getMemberExpireDate(userInfo) {
    if (!userInfo || !userInfo.member_time) {
        return '';
    }
    
    // 如果member_time为0或空字符串，表示非会员
    if (userInfo.member_time === '0' || userInfo.member_time === 0 || userInfo.member_time === '') {
        return '';
    }
    
    let memberExpireTime;
    
    // 如果member_time是时间戳格式（数字）
    if (typeof userInfo.member_time === 'number' || /^\d+$/.test(userInfo.member_time)) {
        memberExpireTime = new Date(parseInt(userInfo.member_time) * 1000);
    } 
    // 如果member_time是日期字符串格式
    else if (typeof userInfo.member_time === 'string') {
        memberExpireTime = new Date(userInfo.member_time.replace(/-/g, '/'));
    } 
    else {
        return '';
    }
    
    // 格式化为 YYYY-MM-DD
    const year = memberExpireTime.getFullYear();
    const month = String(memberExpireTime.getMonth() + 1).padStart(2, '0');
    const day = String(memberExpireTime.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
}

/**
 * 获取会员剩余天数
 * @param {Object} userInfo - 用户信息对象
 * @returns {number} 剩余天数，如果已过期或非会员返回0
 */
export function getMemberRemainingDays(userInfo) {
    if (!isValidMember(userInfo)) {
        return 0;
    }
    
    let memberExpireTime;
    
    // 如果member_time是时间戳格式（数字）
    if (typeof userInfo.member_time === 'number' || /^\d+$/.test(userInfo.member_time)) {
        memberExpireTime = parseInt(userInfo.member_time) * 1000;
    } 
    // 如果member_time是日期字符串格式
    else if (typeof userInfo.member_time === 'string') {
        memberExpireTime = new Date(userInfo.member_time.replace(/-/g, '/')).getTime();
    } 
    else {
        return 0;
    }
    
    const currentTime = new Date().getTime();
    const diffTime = memberExpireTime - currentTime;
    
    if (diffTime <= 0) {
        return 0;
    }
    
    // 转换为天数
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * 获取会员状态描述
 * @param {Object} userInfo - 用户信息对象
 * @returns {Object} 包含状态信息的对象
 */
export function getMemberStatus(userInfo) {
    const isValid = isValidMember(userInfo);
    const expireDate = getMemberExpireDate(userInfo);
    const remainingDays = getMemberRemainingDays(userInfo);
    
    if (!isValid) {
        return {
            isValid: false,
            status: 'expired',
            statusText: '非会员',
            expireDate: '',
            remainingDays: 0,
            statusColor: '#999999'
        };
    }
    
    let status = 'active';
    let statusText = '会员有效';
    let statusColor = '#4CAF50';
    
    // 如果剩余天数少于7天，显示即将到期
    if (remainingDays <= 7) {
        status = 'expiring';
        statusText = `即将到期(${remainingDays}天)`;
        statusColor = '#FF9800';
    }
    
    return {
        isValid: true,
        status,
        statusText,
        expireDate,
        remainingDays,
        statusColor
    };
}

/**
 * 检查是否可以使用会员功能
 * @param {Object} userInfo - 用户信息对象
 * @param {string} feature - 功能名称（可选，用于日志）
 * @returns {boolean} 是否可以使用
 */
export function canUseMemberFeature(userInfo, feature = '') {
    const isValid = isValidMember(userInfo);
    
    if (!isValid && feature) {
        console.log(`用户尝试使用会员功能: ${feature}，但会员已过期或未开通`);
    }
    
    return isValid;
}

/**
 * 格式化会员类型显示文本
 * @param {string} memberType - 会员类型
 * @returns {string} 格式化的会员类型文本
 */
export function formatMemberType(memberType) {
    const typeMap = {
        'month': '月度会员',
        'quarter': '季度会员', 
        'year': '年度会员',
        'lifetime': '终身会员'
    };
    
    return typeMap[memberType] || '普通会员';
}

/**
 * 计算会员价格的优惠信息
 * @param {number} originalPrice - 原价
 * @param {number} currentPrice - 现价
 * @returns {Object} 优惠信息
 */
export function calculateDiscount(originalPrice, currentPrice) {
    if (!originalPrice || !currentPrice || originalPrice <= currentPrice) {
        return {
            hasDiscount: false,
            discountAmount: 0,
            discountPercent: 0,
            discountText: ''
        };
    }
    
    const discountAmount = originalPrice - currentPrice;
    const discountPercent = Math.round((discountAmount / originalPrice) * 100);
    
    return {
        hasDiscount: true,
        discountAmount,
        discountPercent,
        discountText: `省¥${discountAmount.toFixed(2)}`
    };
}

export default {
    isValidMember,
    getMemberExpireDate,
    getMemberRemainingDays,
    getMemberStatus,
    canUseMemberFeature,
    formatMemberType,
    calculateDiscount
};
