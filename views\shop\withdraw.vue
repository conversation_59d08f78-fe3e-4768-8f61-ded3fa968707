<template>
    <view class="shop-withdraw-container">
        <!-- 顶部余额卡片 -->
        <view class="balance-card">
            <view class="balance-title">商家可提现余额</view>
            <view class="balance-amount">
                <text class="currency">¥</text>
                <text class="amount">{{ displayBalance }}</text>
            </view>
            <view class="balance-tips">
                <u-icon name="info-circle" color="#fff" size="24"></u-icon>
                <text>商家销售收入可提现</text>
            </view>
        </view>

        <!-- 提现金额输入 -->
        <view class="withdraw-section">
            <view class="section-title">提现金额</view>
            <view class="amount-input-wrapper">
                <text class="currency-symbol">¥</text>
                <u-input
                    v-model="withdrawAmount"
                    type="digit"
                    placeholder="请输入提现金额"
                    :border="false"
                    :clearable="false"
                    input-align="left"
                    font-size="36"
                    @input="onAmountInput"
                    @blur="onAmountBlur"
                ></u-input>
            </view>

            <!-- 快捷金额选择 -->
            <view class="quick-amounts">
                <view class="quick-amount-item" v-for="(amount, index) in quickAmounts" :key="index" @click="selectQuickAmount(amount)">{{ amount }}元</view>
                <view class="quick-amount-item all" @click="selectAllAmount">全部</view>
            </view>
        </view>

        <!-- 提现说明 -->
        <view class="withdraw-info">
            <view class="info-title">
                <u-icon name="question-circle" color="#8966ef" size="32"></u-icon>
                <text>商家提现说明</text>
            </view>
            <view class="info-list">
                <view class="info-item">
                    <text class="dot">•</text>
                    <text>仅支持提现商家销售收入，其他收入不可提现</text>
                </view>
                <view class="info-item">
                    <text class="dot">•</text>
                    <text>单次提现金额最低1元，最高{{ maxWithdrawAmount }}元</text>
                </view>
                <view class="info-item">
                    <text class="dot">•</text>
                    <text>提现将通过微信零钱到账，预计1-3个工作日</text>
                </view>
                <view class="info-item">
                    <text class="dot">•</text>
                    <text>每日提现次数限制3次</text>
                </view>
            </view>
        </view>

        <!-- 提现按钮 -->
        <view class="btn-area">
            <button class="withdraw-btn" :class="{ disabled: !canWithdraw }" :disabled="!canWithdraw" @click="handleWithdraw">
                {{ loading ? '处理中...' : '立即提现' }}
            </button>
        </view>

        <!-- 提现记录入口 -->
        <view class="record-entry" @click="goToWithdrawRecord">
            <text>查看商家提现记录</text>
            <u-icon name="arrow-right" color="#999" size="28"></u-icon>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
import { createWithdrawShopOrder } from '../../utils/vmeitime-http/shop.js';
import { withdrawApply, checkPayStatus, cancelWithdraw } from '../../utils/vmeitime-http/order.js';

export default {
    data() {
        return {
            balanceInfo: {
                good_balance: '0',
            },
            withdrawAmount: '',
            quickAmounts: [10, 50, 100, 500],
            minWithdrawAmount: 1,
            maxWithdrawAmount: 2000,
            loading: false,
        };
    },
    computed: {
        ...mapState(['hasLogin', 'userInfo']),

        // 显示的可提现余额（原始余额除以100）
        displayBalance() {
            const balance = Math.floor(this.balanceInfo.good_balance || '0');
            return (balance / 100).toFixed(2);
        },

        // 实际可提现余额（用于计算）
        actualBalance() {
            return parseFloat(this.displayBalance);
        },

        // 是否可以提现
        canWithdraw() {
            if (this.loading) return false;
            if (!this.withdrawAmount) return false;

            const amount = parseFloat(this.withdrawAmount);
            const balance = this.actualBalance;

            return amount >= this.minWithdrawAmount && amount <= balance && amount <= this.maxWithdrawAmount && !isNaN(amount) && amount > 0;
        },
    },
    onLoad() {
        this.loadBalanceInfo();
    },
    methods: {
        // 加载余额信息
        async loadBalanceInfo() {
            if (!this.hasLogin) {
                return;
            }

            try {
                const res = await this.$api.getUserWallet({
                    access_token: uni.getStorageSync('token'),
                });

                this.balanceInfo = res.data.balance;
            } catch (error) {
                console.error('获取余额失败', error);
                uni.showToast({
                    title: '网络异常，请稍后重试',
                    icon: 'none',
                });
            }
        },

        // 金额输入处理
        onAmountInput(value) {
            // 限制小数点后两位
            if (value.includes('.')) {
                const parts = value.split('.');
                if (parts[1] && parts[1].length > 2) {
                    this.withdrawAmount = parts[0] + '.' + parts[1].substring(0, 2);
                }
            }
        },

        // 金额输入失焦处理
        onAmountBlur() {
            if (!this.withdrawAmount) return;

            const amount = parseFloat(this.withdrawAmount);
            const balance = this.actualBalance;

            if (isNaN(amount) || amount <= 0) {
                uni.showToast({
                    title: '请输入有效的提现金额',
                    icon: 'none',
                });
                this.withdrawAmount = '';
                return;
            }

            if (amount < this.minWithdrawAmount) {
                uni.showToast({
                    title: `最低提现金额为${this.minWithdrawAmount}元`,
                    icon: 'none',
                });
                this.withdrawAmount = this.minWithdrawAmount.toString();
                return;
            }

            if (amount > this.maxWithdrawAmount) {
                uni.showToast({
                    title: `最高提现金额为${this.maxWithdrawAmount}元`,
                    icon: 'none',
                });
                this.withdrawAmount = this.maxWithdrawAmount.toString();
                return;
            }

            if (amount > balance) {
                uni.showToast({
                    title: '提现金额不能超过可提现余额',
                    icon: 'none',
                });
                this.withdrawAmount = balance.toFixed(2);
            }
        },

        // 选择快捷金额
        selectQuickAmount(amount) {
            const balance = this.actualBalance;
            if (amount > balance) {
                uni.showToast({
                    title: '余额不足',
                    icon: 'none',
                });
                return;
            }

            if (amount > this.maxWithdrawAmount) {
                uni.showToast({
                    title: `最高提现金额为${this.maxWithdrawAmount}元`,
                    icon: 'none',
                });
                return;
            }

            this.withdrawAmount = amount.toString();
        },

        // 选择全部金额
        selectAllAmount() {
            const balance = this.actualBalance;
            if (balance < this.minWithdrawAmount) {
                uni.showToast({
                    title: `最低提现金额为${this.minWithdrawAmount}元`,
                    icon: 'none',
                });
                return;
            }
            this.withdrawAmount = Math.min(balance, this.maxWithdrawAmount).toFixed(2);
        },

        // 处理提现
        async handleWithdraw() {
            if (!this.hasLogin) {
                uni.showToast({
                    title: '请先登录',
                    icon: 'none',
                });
                return;
            }

            if (!this.canWithdraw) {
                return;
            }

            const amount = parseFloat(this.withdrawAmount);
            console.log('商家提现金额', amount);

            // 验证提现金额
            if (!this.canWithdraw) {
                uni.showToast({
                    title: '请检查提现金额',
                    icon: 'none',
                });
                return;
            }

            // 确认提现
            const confirmResult = await new Promise(resolve => {
                uni.showModal({
                    title: '确认商家提现',
                    content: `确定要提现 ¥${amount.toFixed(2)} 到微信零钱吗？`,
                    confirmText: '确认提现',
                    cancelText: '取消',
                    success: res => {
                        resolve(res.confirm);
                    },
                    fail: () => {
                        resolve(false);
                    },
                });
            });

            if (!confirmResult) {
                return;
            }

            this.loading = true;
            uni.showLoading({
                title: '处理中...',
            });

            try {
                // 调用商户提现订单创建接口
                const order = await createWithdrawShopOrder({
                    access_token: uni.getStorageSync('token'),
                    amount: amount, // 金额以元为单位
                });

                const res = await withdrawApply({
                    orderid: order.data.order_no,
                    openid: this.userInfo.openid,
                    access_token: uni.getStorageSync('token'),
                });
                uni.hideLoading();
                // 提现成功，调用微信企业付款
                await this.processWechatTransfer(res.data, order.data.order_no);

                // if (res.status === 0) {
                //     // 提现成功
                //     uni.showToast({
                //         title: '提现申请成功',
                //         icon: 'success',
                //     });

                //     // 清空输入
                //     this.withdrawAmount = '';

                //     // 刷新余额
                //     this.loadBalanceInfo();

                //     // 延迟返回上一页
                //     setTimeout(() => {
                //         uni.navigateBack({
                //             success: () => {
                //                 // 通知商家中心页面刷新数据
                //                 uni.$emit('refreshShopData');
                //             },
                //         });
                //     }, 2000);
                // }
            } catch (error) {
                console.error('商家提现失败', error);
                uni.hideLoading();
                uni.showToast({
                    title: error.msg || '提现失败，请稍后重试',
                    icon: 'none',
                });
            } finally {
                this.loading = false;
            }
        },
        // 处理微信企业付款
        async processWechatTransfer(res, orderid) {
            try {
                const params = { access_token: uni.getStorageSync('token'), orderid: orderid };
                // 调用微信小程序的企业付款接口

                wx.requestMerchantTransfer({
                    mchId: res.pay.public.mchId,
                    package: res.pay.package_info,
                    appId: res.pay.public.appid,
                    openId: this.userInfo.openid,
                    success: transferRes => {
                        // 提现成功
                        uni.showToast({
                            title: '提现成功',
                            icon: 'success',
                        });

                        // 清空输入
                        this.withdrawAmount = '';

                        // 刷新余额
                        this.loadBalanceInfo();

                        // 延迟返回上一页
                        setTimeout(() => {
                            uni.navigateBack({
                                success: () => {
                                    // 通知钱包页面刷新数据
                                    uni.$emit('refreshBalance');
                                },
                            });
                        }, 2000);
                    },
                    fail: async err => {
                        const can = await cancelWithdraw(params);
                        console.log(can, 2000);
                        uni.showToast({
                            title: '提现失败，请稍后重试',
                            icon: 'none',
                        });
                    },
                    complete: () => {
                        checkPayStatus(params);
                    },
                });
            } catch (error) {
                console.error('微信企业付款失败', error);
                uni.showToast({
                    title: '提现失败，请稍后重试',
                    icon: 'none',
                });
            }
        },
        // 前往商家提现记录页面
        goToWithdrawRecord() {
            uni.navigateTo({
                url: '/views/shop/withdrawRecord',
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.shop-withdraw-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);
    padding-bottom: 30rpx;
}

.balance-card {
    background: linear-gradient(135deg, #8966ef, #a584f2);
    padding: 40rpx 30rpx;
    color: #fff;

    .balance-title {
        font-size: 28rpx;
        opacity: 0.9;
    }

    .balance-amount {
        margin: 20rpx 0;
        display: flex;
        align-items: baseline;

        .currency {
            font-size: 36rpx;
            margin-right: 8rpx;
        }

        .amount {
            font-size: 60rpx;
            font-weight: bold;
        }
    }

    .balance-tips {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        opacity: 0.8;

        text {
            margin-left: 8rpx;
        }
    }
}

.withdraw-section {
    margin: 20rpx 30rpx;
    padding: 30rpx;
    background-color: #fff;
    border-radius: 12rpx;
    border: 1rpx solid #e8e8e8;

    .section-title {
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 30rpx;
    }

    .amount-input-wrapper {
        display: flex;
        align-items: center;
        padding: 20rpx 0;
        border-bottom: 2rpx solid #f0f0f0;

        .currency-symbol {
            font-size: 36rpx;
            color: #333;
            margin-right: 10rpx;
        }
    }

    .quick-amounts {
        display: flex;
        flex-wrap: wrap;
        margin-top: 30rpx;

        .quick-amount-item {
            padding: 16rpx 24rpx;
            margin: 0 20rpx 20rpx 0;
            background-color: #f8f9fa;
            border-radius: 20rpx;
            font-size: 26rpx;
            color: #666;
            border: 2rpx solid transparent;

            &.all {
                background-color: rgba(137, 102, 239, 0.1);
                color: #8966ef;
            }

            &:active {
                background-color: rgba(137, 102, 239, 0.1);
                border-color: #8966ef;
                color: #8966ef;
            }
        }
    }
}

.withdraw-info {
    margin: 20rpx 30rpx;
    padding: 30rpx;
    background-color: #fff;
    border-radius: 12rpx;
    border: 1rpx solid #e8e8e8;

    .info-title {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 20rpx;

        text {
            margin-left: 8rpx;
        }
    }

    .info-list {
        .info-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 16rpx;
            font-size: 26rpx;
            color: #666;
            line-height: 1.5;

            &:last-child {
                margin-bottom: 0;
            }

            .dot {
                color: #8966ef;
                margin-right: 8rpx;
                margin-top: 2rpx;
            }
        }
    }
}

.btn-area {
    margin: 40rpx 30rpx;

    .withdraw-btn {
        height: 90rpx;
        line-height: 90rpx;
        background: linear-gradient(135deg, #8966ef, #a584f2);
        color: #fff;
        font-size: 32rpx;
        border-radius: 45rpx;
        font-weight: 500;
        border: none;

        &.disabled {
            opacity: 0.6;
            background: #ccc;
        }
    }
}

.record-entry {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 30rpx;
    padding: 30rpx;
    background-color: #fff;
    border-radius: 12rpx;
    border: 1rpx solid #e8e8e8;
    font-size: 28rpx;
    color: #333;

    &:active {
        background-color: #f8f9fa;
    }
}
</style>
