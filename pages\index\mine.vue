<template>
    <view class="content">
        <!-- 顶部用户信息区域 -->
        <view class="user-header">
            <block v-if="hasLogin">
                <view class="user-info-box">
                    <view class="user-info" @click="goMyUpdate">
                        <view class="user-avatar">
                            <image mode="aspectFill" :src="userInfo.headimgurl || 'http://static.wdaoyun.com/wdy/source/2023-10-18/132.jpg'"></image>
                            <view class="vip-badge" v-if="memberStatus.isValid">
                                <text class="vip-crown">👑</text>
                                <text>VIP</text>
                            </view>
                        </view>
                        <view class="user-detail">
                            <view class="user-name">
                                <text>{{ userInfo.nickname || '微信用户' }}</text>
                            </view>
                            <view class="user-mobile" v-if="userInfo.mobile" @click.stop="openMobilePopup">{{ userInfo.mobile }}</view>
                            <view class="bind-mobile" v-else @click.stop="openMobilePopup">绑定手机号</view>

                            <!-- VIP会员状态信息 -->
                            <view class="vip-status-info">
                                <view v-if="memberStatus.isValid" class="member-status-active">
                                    <text class="member-status-text" :style="{ color: memberStatus.statusColor }">{{ memberStatus.statusText }}</text>
                                    <text class="expire-date" v-if="memberStatus.expireDate">{{ memberStatus.expireDate }}到期</text>
                                </view>
                                <view v-else class="member-status-inactive" @click.stop="goToVipCenter">
                                    <text class="vip-invite">开通会员享更多特权</text>
                                    <!-- <text class="vip-arrow">></text> -->
                                </view>
                            </view>
                        </view>
                    </view>
                    <view class="user-info-box-right">
                        <view class="user-action-btn" @click="goToTradeCenter">
                            <text>交易中心</text>
                        </view>
                        <view class="user-action-btn" @click="goToDynamic">
                            <text>发布动态</text>
                        </view>
                        <view class="vip-action-btn" @click="goToVipCenter" v-if="!memberStatus.isValid">
                            <text class="vip-btn-text">开通VIP</text>
                        </view>
                        <view class="vip-manage-btn" @click="goToVipCenter" v-else>
                            <text class="manage-btn-text">会员中心</text>
                        </view>
                    </view>
                </view>
            </block>
            <block v-else>
                <view class="user-info" @click="touchLogin">
                    <view class="user-avatar">
                        <image mode="aspectFill" src="https://static.wdaoyun.com/wdy/source/2023-10-18/132.jpg"></image>
                    </view>
                    <view class="user-detail">
                        <view class="user-name">点击登录</view>
                    </view>
                </view>
            </block>
        </view>

        <!-- 用户数据概览 -->
        <view class="user-stats">
            <view class="stat-item" @click="goToWallet">
                <text class="stat-value">{{ balance || '0.00' }}</text>
                <text class="stat-label">余额</text>
            </view>
            <view class="stat-item" @click="goToReward">
                <text class="stat-value">{{ dsBalance || '0.00' }}</text>
                <text class="stat-label">打赏</text>
            </view>
            <view class="stat-item" @click="goToPoints">
                <text class="stat-value">{{ userIndex.gz_num || '0' }}</text>
                <text class="stat-label">关注</text>
            </view>
            <view class="stat-item" @click="goToGifts">
                <text class="stat-value">{{ userIndex.fs_num || '0' }}</text>
                <text class="stat-label">粉丝</text>
            </view>
            <view class="stat-item" @click="showLikePopup">
                <text class="stat-value">{{ userIndex.like_num || '0' }}</text>
                <text class="stat-label">点赞</text>
            </view>
        </view>

        <!--我的作品-->
        <view class="user-work-tab">
            <u-tabs :list="tabList" :is-scroll="false" :current="current" @change="tabChange" active-color="#8966ef" :bar-width="50"></u-tabs>
        </view>
        <!-- 作品列表 -->
        <view class="user-grid">
            <!-- 用户卡片网格，每行两个 -->
            <view v-if="current == 0 && homeList.length > 0" class="user-grid-item" v-for="(user, userIndex) in homeList" :key="userIndex">
                <user-card :work-info="user" mode="mine" @statusChanged="handleStatusChanged" @workDeleted="handleWorkDeleted"></user-card>
            </view>
            <view v-if="current == 1 && likeList.length > 0" class="user-grid-item" v-for="(user, userIndex) in likeList" :key="userIndex">
                <user-card :showView="false" :work-info="user.class_info" mode="mine" @like="handleLike"></user-card>
            </view>
        </view>

        <!-- 空状态展示 -->
        <view class="empty-state" v-if="!loading">
            <view class="empty-content" v-if="current == 0 && homeList.length === 0">
                <image class="empty-image" src="https://static.wdaoyun.com/friend/2025-07-02/ef0fac060cb6f1abfde647fe1a55776d.png" mode="aspectFit"></image>
                <view class="empty-text">{{ getEmptyText() }}</view>
                <view class="empty-tip">快去发布你的第一个作品吧~</view>
            </view>
            <view class="empty-content" v-else-if="current == 1 && likeList.length === 0">
                <image class="empty-image" src="https://static.wdaoyun.com/friend/2025-07-02/ef0fac060cb6f1abfde647fe1a55776d.png" mode="aspectFit"></image>
                <view class="empty-text">{{ getEmptyText() }}</view>
                <view class="empty-tip">快去收藏你的第一个作品吧~</view>
            </view>
        </view>

        <!-- 加载更多提示 -->
        <view class="load-more" v-if="homeList.length > 0">
            <text v-if="loading">加载中...</text>
            <text v-else-if="noMore">没有更多数据了</text>
        </view>
        <view v-show="!hasLogin" class="content-mask" @touchmove.stop="" @click.stop="touchLogin"></view>
        <!-- <authorization ref="auth" @loginSuccess="loginSuccess"></authorization> -->
        <mobile-popup ref="mobilePop" @bindSuccess="handleBindMobileSuccess"></mobile-popup>

        <!-- 点赞弹框 -->
        <view class="like-popup-overlay" v-if="showLikeModal" @click="hideLikePopup">
            <view class="like-popup" @click.stop>
                <view class="like-popup-content">
                    <view class="like-icon">
                        <text class="heart-icon">❤️</text>
                    </view>
                    <view class="like-text">
                        <text class="like-title">获得点赞</text>
                        <text class="like-count">{{ userIndex.like_num || '0' }}</text>
                        <text class="like-subtitle">个赞</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex';
import authorization from '../../components/popup/authorization.vue';
import UserCard from '../../components/UserCard';
import MobilePopup from '../../components/popup/mobile-popup.vue';
import { getUserWallet } from '../../utils/vmeitime-http/user.js';
import { getMemberStatus } from '../../utils/memberUtils.js';

export default {
    components: { authorization, UserCard, MobilePopup },
    computed: {
        ...mapState(['userInfo', 'hasLogin']),
        // 计算会员状态
        memberStatus() {
            return getMemberStatus(this.userInfo);
        },
    },
    // 页面配置
    onNavigationBarButtonTap() {
        uni.navigateTo({
            url: '/pages/index/myUpdate',
        });
    },
    // 启用下拉刷新
    enablePullDownRefresh: true,
    data() {
        return {
            avatar: 'https://cdn.uviewui.com/uview/album/1.jpg',
            adminList: [
                {
                    id: 7,
                    name: '联系客服',
                    url: '/views/property/question',
                    icon: '../../static/images/service.png',
                },
                {
                    id: 8,
                    name: '售后热线',
                    url: '/pages/webview/webview',
                    icon: '../../static/images/service.png',
                },
                {
                    id: 9,
                    name: '我的消息',
                    url: '/views/chat_page/chat-list',
                    icon: '../../static/images/message.png',
                },
                {
                    id: 6,
                    name: '软件版本',
                    url: '/views/mine/collectList',
                    icon: '../../static/images/version.png',
                },
            ],
            tabList: [{ name: '作品' }, { name: '点赞' }],
            current: 0,
            phoneNum: '************',
            version: uni.getStorageSync('version') || '1.0.0',
            commonFeatures: [
                {
                    id: 'edit_profile',
                    name: '编辑资料',
                    icon: '../../static/images/zl.png',
                    url: '/pages/index/myUpdate',
                },
                {
                    id: 'vip_center',
                    name: '会员中心',
                    icon: '../../static/images/hy.png',
                    url: '/views/moments/vip',
                },
                {
                    id: 'auto_reply_robot',
                    name: '自动回复',
                    icon: '../../static/images/jf.png',
                    url: '/views/robot/index',
                },
                {
                    id: 'points_mall',
                    name: '积分商城',
                    icon: '../../static/images/jf.png',
                    url: '/pages/points/mall',
                },
                {
                    id: 'my_friends',
                    name: '我的好友',
                    icon: '../../static/images/hb.png',
                    url: '/pages/social/friends',
                },
            ],
            homeList: [],
            likeList: [],
            page: 1,
            pagesize: 10,
            userIndex: {},
            balance: '0.00',
            dsBalance: '0.00',
            loading: false,
            noMore: false,
            hasNextPage: true,
            showLikeModal: false,
        };
    },
    watch: {},
    onLoad() {
        if (!this.hasLogin) {
            return;
        }
        this.init();
        uni.$on('refreshPublish', () => {
            this.refreshList();
        });
        uni.$on('refreshBalance', () => {
            this.getUserWallet();
        });
    },
    onUnload() {
        uni.$off('refreshPublish');
        uni.$off('refreshBalance');
    },
    onShow() {
        if (!this.hasLogin) return;
        this.init();
    },
    onPullDownRefresh: function () {
        setTimeout(() => {
            uni.stopPullDownRefresh();
        }, 500);
        if (!this.hasLogin) return;
        this.init();
    },

    onReachBottom: function () {
        if (this.current == 0) {
            this.getUserWorkList();
        } else if (this.current == 1) {
            this.getLikeWorkList();
        }
    },

    methods: {
        ...mapMutations(['setUserAuth', 'setHasLogin', 'updateUserInfo']),
        ...mapActions(['VAgetUser']),
        // 刷新列表
        refreshList() {
            this.page = 1;
            this.hasNextPage = true;
            this.noMore = false;
            if (this.current == 0) {
                this.getUserWorkList();
            } else if (this.current == 1) {
                this.getLikeWorkList();
            }
        },
        init() {
            this.page = 1;
            this.hasNextPage = true;
            this.noMore = false;
            this.VAgetUser();
            if (this.current == 0) {
                this.getUserWorkList();
            } else if (this.current == 1) {
                this.getLikeWorkList();
            }
            this.getUserIndex();
            this.getUserWallet();
        },
        // 登录成功回调
        loginSuccess(type) {
            this.init();
        },
        async getUserIndex() {
            const res = await this.$api.getUserIndex({
                access_token: uni.getStorageSync('token'),
            });
            this.userIndex = res.data;
        },
        // 标签切换事件
        tabChange(index) {
            this.current = index;
            this.refreshList();
        },
        async getUserWorkList() {
            if (this.loading || !this.hasNextPage) return;

            this.loading = true;
            try {
                const res = await this.$api.getUserWorkList({
                    page: this.page,
                    pagesize: this.pagesize,
                    access_token: uni.getStorageSync('token'),
                });

                if (res.data && res.data.list) {
                    // 首次加载或刷新时直接赋值，加载更多时追加数据
                    if (this.page === 1) {
                        this.homeList = res.data.list;
                    } else {
                        this.homeList = [...this.homeList, ...res.data.list];
                    }

                    // 判断是否还有下一页
                    this.hasNextPage = res.data.list.length >= this.pagesize;
                    this.noMore = !this.hasNextPage;

                    // 页码自增，为下次加载做准备
                    if (this.hasNextPage) {
                        this.page++;
                    }
                } else {
                    this.hasNextPage = false;
                    this.noMore = true;
                }
            } catch (error) {
                console.error('获取作品列表失败', error);
                uni.showToast({
                    title: '获取作品列表失败',
                    icon: 'none',
                });
            } finally {
                this.loading = false;
            }
        },
        async getLikeWorkList() {
            if (this.loading || !this.hasNextPage) return;

            this.loading = true;
            try {
                const res = await this.$api.getLikeWorkList({
                    page: this.page,
                    pagesize: this.pagesize,
                    access_token: uni.getStorageSync('token'),
                });

                if (res.data && res.data.list) {
                    // 首次加载或刷新时直接赋值，加载更多时追加数据
                    if (this.page === 1) {
                        this.likeList = res.data.list;
                    } else {
                        this.likeList = [...this.likeList, ...res.data.list];
                    }

                    // 判断是否还有下一页
                    this.hasNextPage = res.data.list.length >= this.pagesize;
                    this.noMore = !this.hasNextPage;

                    // 页码自增，为下次加载做准备
                    if (this.hasNextPage) {
                        this.page++;
                    }
                } else {
                    this.hasNextPage = false;
                    this.noMore = true;
                }
            } catch (error) {
                console.error('获取作品列表失败', error);
                uni.showToast({
                    title: '获取作品列表失败',
                    icon: 'none',
                });
            } finally {
                this.loading = false;
            }
        },
        // 前往我的详情
        goMyUpdate() {
            uni.navigateTo({
                url: '/views/mine/myUpdate',
            });
        },
        exitLogin() {
            uni.showModal({
                title: '登出',
                content: '确定退出账号',
                success: res => {
                    if (res.confirm) {
                        uni.removeStorageSync('token');
                        uni.removeStorageSync('userInfo');
                        this.setHasLogin(false);
                        this.updateUserInfo({});
                        uni.reLaunch({
                            url: '/pages/index/index',
                        });
                    }
                },
            });
        },
        async openMobilePopup() {
            this.$refs.mobilePop.show();
        },
        // 处理手机绑定成功
        handleBindMobileSuccess(mobile) {
            // 更新用户信息中的手机号
            const userInfo = { ...this.userInfo, mobile };
            this.updateUserInfo(userInfo);
            this.$u.toast('手机号绑定成功', 2000);
        },
        // 处理作品状态变更
        handleStatusChanged(data) {
            const { workId, newStatus } = data;
            // 更新本地列表中的状态
            const workIndex = this.homeList.findIndex(work => work.id === workId);
            if (workIndex !== -1) {
                this.homeList[workIndex].status = newStatus;
                // // 如果是隐藏，从列表中移除
                // if (newStatus === 2) {
                //     this.homeList.splice(workIndex, 1);
                // }
            }
        },
        // 处理作品删除
        handleWorkDeleted(workId) {
            // 从列表中移除已删除的作品
            const workIndex = this.homeList.findIndex(work => work.id === workId);
            if (workIndex !== -1) {
                this.homeList.splice(workIndex, 1);
            }
        },
        handleLike(isLike,workId) {
            console.log(workId);
            const workIndex = this.likeList.findIndex(work => work.class_id === workId);
            console.log(workIndex);

            if (workIndex !== -1) {
                this.likeList.splice(workIndex, 1);
            }
        },
        // 判断是否授权
        touchLogin() {
            // this.$refs.auth.show();
            uni.navigateTo({
                url: '/pages/index/login',
            });
            return;
        },
        handleFeatureClick(item) {
            uni.navigateTo({
                url: item.url,
            });
        },
        goToWallet() {
            uni.navigateTo({
                url: '/views/wallet/index?type=1',
            });
        },
        goToReward() {
            uni.navigateTo({
                url: '/views/wallet/index?type=2',
            });
        },
        goToPoints() {
            uni.navigateTo({
                url: '/views/mine/follow',
            });
        },
        goToGifts() {
            uni.navigateTo({
                url: '/views/mine/fans',
            });
        },
        goToFriends() {
            uni.navigateTo({
                url: '/views/mine/likes',
            });
        },
        goToVipCenter() {
            uni.navigateTo({
                url: '/views/moments/vip',
            });
        },
        goToTradeCenter() {
            uni.navigateTo({
                url: '/views/shop/center',
            });
        },
        goToDynamic() {
            if (!this.userInfo.province_code) {
                uni.showModal({
                    title: '提示',
                    content: '请先完善用户信息',
                    confirmText: '去完善',
                    success: res => {
                        if (res.confirm) {
                            uni.navigateTo({
                                url: '/views/mine/myUpdate',
                            });
                        }
                    },
                });
                return;
            }
            uni.navigateTo({
                url: '/views/moments/publish',
            });
        },
        // 获取用户余额
        async getUserWallet() {
            if (!this.hasLogin) {
                return;
            }

            try {
                const res = await getUserWallet({
                    access_token: uni.getStorageSync('token'),
                });

                if (res.status === 0 && res.data) {
                    this.balance = res.data.balance.balance || '0.00';
                    this.dsBalance = res.data.balance.ds_balance || '0.00';
                    // 更新用户信息中的余额
                    const userInfo = { ...this.userInfo, balance: this.balance, ds_balance: this.dsBalance };
                    this.updateUserInfo(userInfo);
                }
            } catch (error) {
                console.error('获取余额失败', error);
            }
        },
        // 获取空状态文案
        getEmptyText() {
            const textMap = {
                0: '暂无作品',
                1: '暂无收藏',
                2: '暂无点赞',
            };
            return textMap[this.current] || '暂无数据';
        },
        // 显示点赞弹框
        showLikePopup() {
            if (!this.hasLogin) {
                this.touchLogin();
                return;
            }
            this.showLikeModal = true;
            // 2秒后自动隐藏
            setTimeout(() => {
                this.hideLikePopup();
            }, 2000);
        },
        // 隐藏点赞弹框
        hideLikePopup() {
            this.showLikeModal = false;
        },
    },
};
</script>

<style lang="scss" scoped>
.content {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);

    .user-header {
        padding: 40rpx 30rpx 50rpx;
        background: linear-gradient(135deg, #8966ef, #a584f2, #f0cc6c);
        position: relative;
        overflow: hidden;

        // 添加装饰性背景元素
        &::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 200rpx;
            height: 200rpx;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            z-index: 0;
        }

        &::after {
            content: '';
            position: absolute;
            bottom: -30%;
            left: -10%;
            width: 150rpx;
            height: 150rpx;
            background: rgba(240, 204, 108, 0.2);
            border-radius: 50%;
            z-index: 0;
        }

        .user-info-box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            z-index: 1;
        }

        .user-info-box-right {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 16rpx;

            .user-action-btn {
                padding: 12rpx 24rpx;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 25rpx;
                color: #fff;
                font-size: 26rpx;
                backdrop-filter: blur(10rpx);
                border: 1rpx solid rgba(255, 255, 255, 0.3);
            }

            .vip-action-btn {
                padding: 12rpx 24rpx;
                background: linear-gradient(135deg, #f0cc6c, #edc353);
                border-radius: 25rpx;
                color: #8a6500;
                font-size: 26rpx;
                font-weight: 600;
                box-shadow: 0 4rpx 12rpx rgba(240, 204, 108, 0.3);

                .vip-btn-text {
                    display: flex;
                    align-items: center;

                    &::before {
                        content: '👑';
                        margin-right: 6rpx;
                        font-size: 22rpx;
                    }
                }
            }

            .vip-manage-btn {
                padding: 12rpx 24rpx;
                background: rgba(255, 255, 255, 0.15);
                border-radius: 25rpx;
                color: #fff;
                font-size: 26rpx;
                border: 1rpx solid rgba(240, 204, 108, 0.5);

                .manage-btn-text {
                    opacity: 0.9;
                }
            }
        }

        .user-info {
            display: flex;
            align-items: flex-start;
            flex: 1;

            .user-avatar {
                position: relative;

                image {
                    width: 120rpx;
                    height: 120rpx;
                    border-radius: 60rpx;
                    border: 4rpx solid rgba(255, 255, 255, 0.3);
                    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
                }

                .vip-badge {
                    position: absolute;
                    bottom: -8rpx;
                    left: 50%;
                    transform: translateX(-50%);
                    background: linear-gradient(135deg, #f0cc6c, #edc353);
                    color: #8a6500;
                    font-size: 20rpx;
                    padding: 6rpx 16rpx;
                    border-radius: 20rpx;
                    font-weight: 600;
                    box-shadow: 0 2rpx 8rpx rgba(240, 204, 108, 0.4);
                    display: flex;
                    align-items: center;
                    gap: 4rpx;

                    .vip-crown {
                        font-size: 18rpx;
                    }
                }
            }

            .user-detail {
                margin-left: 30rpx;
                flex: 1;

                .user-name {
                    display: flex;
                    align-items: center;
                    color: #fff;
                    font-size: 36rpx;
                    font-weight: 600;
                    margin-bottom: 8rpx;
                    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
                }

                .user-mobile {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 26rpx;
                    margin-bottom: 12rpx;
                }

                .bind-mobile {
                    color: #fff;
                    font-size: 26rpx;
                    margin-bottom: 12rpx;
                    padding: 6rpx 16rpx;
                    background: rgba(255, 255, 255, 0.2);
                    border-radius: 20rpx;
                    display: inline-block;
                    backdrop-filter: blur(10rpx);
                }

                .vip-status-info {
                    .member-status-active {
                        display: flex;
                        .member-status-text {
                            color: #fff !important;
                            font-size: 24rpx;
                            font-weight: 500;
                            background: rgba(255, 255, 255, 0.15);
                            padding: 4rpx 12rpx;
                            border-radius: 12rpx;
                            margin-right: 12rpx;
                            backdrop-filter: blur(10rpx);
                        }

                        .expire-date {
                            color: rgba(255, 255, 255, 0.7);
                            font-size: 22rpx;
                            display: block;
                            margin-top: 6rpx;
                        }
                    }

                    .member-status-inactive {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        padding: 8rpx 0rpx;
                        width: 260rpx;
                        background: rgba(240, 204, 108, 0.2);
                        border-radius: 20rpx;
                        border: 1rpx solid rgba(240, 204, 108, 0.3);
                        backdrop-filter: blur(10rpx);

                        .vip-invite {
                            color: #fff;
                            font-size: 24rpx;
                            font-weight: 500;
                            // flex: 1;
                        }

                        .vip-arrow {
                            color: rgba(255, 255, 255, 0.8);
                            font-size: 24rpx;
                            margin-left: 8rpx;
                        }
                    }
                }
            }
        }
    }

    .user-stats {
        margin: -30rpx 30rpx 0;
        padding: 30rpx;
        background: #fff;
        border-radius: 20rpx;
        display: flex;
        justify-content: space-around;
        box-shadow: 0 8rpx 24rpx rgba(137, 102, 239, 0.1);
        position: relative;
        z-index: 2;

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;

            .stat-value {
                color: #333;
                font-size: 36rpx;
                font-weight: 600;
                margin-bottom: 8rpx;
            }

            .stat-label {
                color: #666;
                font-size: 24rpx;
            }
        }
    }

    .feature-groups {
        margin: 30rpx;

        .feature-group {
            background: #fff;
            border-radius: 16rpx;
            padding: 30rpx;
            margin-bottom: 30rpx;

            .group-title {
                color: #333;
                font-size: 32rpx;
                font-weight: 500;
                margin-bottom: 30rpx;
            }

            .feature-list {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 30rpx;

                .feature-item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;

                    .feature-icon {
                        width: 80rpx;
                        height: 80rpx;
                        margin-bottom: 16rpx;
                    }

                    .feature-name {
                        color: #333;
                        font-size: 24rpx;
                    }
                }
            }
        }

        .menu-list {
            background: #fff;
            border-radius: 16rpx;
            overflow: hidden;

            .menu-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 30rpx;
                border-bottom: 1rpx solid #f5f6fa;

                &:last-child {
                    border-bottom: none;
                }

                .menu-item-left {
                    display: flex;
                    align-items: center;

                    .menu-icon {
                        width: 40rpx;
                        height: 40rpx;
                        margin-right: 20rpx;
                    }

                    text {
                        color: #333;
                        font-size: 28rpx;
                    }
                }

                .menu-item-right {
                    display: flex;
                    align-items: center;

                    .check-text {
                        color: #999;
                        font-size: 28rpx;
                        margin-right: 16rpx;
                    }

                    image {
                        width: 16rpx;
                        height: 26rpx;
                    }
                }
            }
        }
    }

    .content-mask {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        opacity: 0;
        background-color: transparent;
        z-index: 1071;
    }
}

.user-grid {
    display: flex;
    flex-wrap: wrap;
    padding: 20rpx;

    .user-grid-item {
        width: 50%;
        padding: 10rpx;
        box-sizing: border-box;
    }
}
.user-work-tab {
    margin: 30rpx 30rpx;
    background: #fff;
    border-radius: 16rpx;
}

.load-more {
    text-align: center;
    padding: 20rpx;
    color: #666;
    font-size: 28rpx;
}

.empty-state {
    .empty-content {
        // margin: 100rpx 30rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .empty-image {
            width: 400rpx;
            height: 314rpx;
            margin-bottom: 40rpx;
        }

        .empty-text {
            color: #999;
            font-size: 32rpx;
            margin-bottom: 16rpx;
            text-align: center;
        }

        .empty-tip {
            color: #8966ef;
            font-size: 28rpx;
            text-align: center;
        }
    }
}

// 点赞弹框样式
.like-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeIn 0.3s ease-out;
}

.like-popup {
    background: #fff;
    border-radius: 24rpx;
    padding: 60rpx 80rpx;
    box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.2);
    animation: popupScale 0.3s ease-out;
    max-width: 500rpx;
}

.like-popup-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.like-icon {
    margin-bottom: 30rpx;

    .heart-icon {
        font-size: 80rpx;
        animation: heartBeat 0.6s ease-in-out;
    }
}

.like-text {
    display: flex;
    flex-direction: column;
    align-items: center;

    .like-title {
        color: #333;
        font-size: 32rpx;
        font-weight: 500;
        margin-bottom: 16rpx;
    }

    .like-count {
        color: #8966ef;
        font-size: 48rpx;
        font-weight: 600;
        margin-bottom: 8rpx;
        text-shadow: 0 2rpx 4rpx rgba(137, 102, 239, 0.2);
    }

    .like-subtitle {
        color: #666;
        font-size: 28rpx;
    }
}

// 动画效果
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes popupScale {
    from {
        transform: scale(0.8);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes heartBeat {
    0% {
        transform: scale(1);
    }
    25% {
        transform: scale(1.2);
    }
    50% {
        transform: scale(1);
    }
    75% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}
</style>
