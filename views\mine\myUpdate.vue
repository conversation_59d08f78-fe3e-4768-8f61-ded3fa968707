<template>
    <view class="content">
        <view class="content-avatar">
            <view class="avatar-info">
                <view class="avatar-img">
                    <image mode="aspectFill" :src="headimgurl ? headimgurl : 'http://static.wdaoyun.com/wdy/source/2023-10-18/132.jpg'"></image>
                    <!-- @click.native="chooseImg" -->
                    <view class="avatar-upload">
                        <u-icon name="camera" color="#fff" size="48"></u-icon>
                    </view>
                    <button class="phone-btn" @chooseavatar="onChooseAvatar" open-type="chooseAvatar"></button>
                </view>
                <text>头像</text>
            </view>
        </view>

        <!-- 基本信息 -->
        <view class="form-section-title">基本信息</view>
        <view class="form-top">
            <view class="form-item bt-1">
                <view class="form-item-left">
                    用户名
                </view>
                <view class="form-item-right">
                    <u-input
                        :clearable="false"
                        height="40"
                        input-align="right"
                        type="nickname"
                        class="uinput"
                        placeholder="请输入用户名"
                        :custom-style="inputStyle"
                        :placeholder-style="placeStyle"
                        v-model="nickname"
                    />
                    <u-icon class="uicon" name="arrow-right" color="#7D7F82"></u-icon>
                </view>
            </view>
            <view class="form-item bt-1">
                <view class="form-item-left">
                    <text style="color: red">*</text>
                    真实姓名
                </view>
                <view class="form-item-right">
                    <u-input
                        :clearable="false"
                        height="40"
                        input-align="right"
                        class="uinput"
                        placeholder="请输入真实姓名"
                        :custom-style="inputStyle"
                        :placeholder-style="placeStyle"
                        v-model="real_name"
                    />
                    <u-icon class="uicon" name="arrow-right" color="#7D7F82"></u-icon>
                </view>
            </view>
            <view class="form-item bt-1">
                <view class="form-item-left">
                    <text style="color: red">*</text>
                    性别
                </view>
                <view class="form-item-right gender-radio-group">
                    <u-radio-group v-model="sex">
                        <u-radio v-for="(item, index) in sexList" active-color="#8966ef" :key="index" :name="item.value" :disabled="item.disabled">
                            {{ item.name }}
                        </u-radio>
                    </u-radio-group>
                </view>
            </view>
            <view class="form-item bt-1">
                <view class="form-item-left">生日</view>
                <view class="form-item-right" @click="showBirthdayPicker = true">
                    <text :class="birthday ? 'value-text' : 'none'">{{ birthday || '请选择' }}</text>
                    <u-icon class="uicon" name="arrow-right" color="#7D7F82"></u-icon>
                </view>
            </view>
            <view class="form-item bt-1">
                <view class="form-item-left">身高</view>
                <view class="form-item-right">
                    <u-input
                        :clearable="false"
                        height="40"
                        input-align="right"
                        type="number"
                        class="uinput"
                        placeholder="请输入身高(cm)"
                        :custom-style="inputStyle"
                        :placeholder-style="placeStyle"
                        v-model="sf_cm"
                    />
                    <u-icon class="uicon" name="arrow-right" color="#7D7F82"></u-icon>
                </view>
            </view>
            <view class="form-item">
                <view class="form-item-left">体重</view>
                <view class="form-item-right">
                    <u-input
                        :clearable="false"
                        height="40"
                        input-align="right"
                        type="number"
                        class="uinput"
                        placeholder="请输入体重(kg)"
                        :custom-style="inputStyle"
                        :placeholder-style="placeStyle"
                        v-model="tz_kg"
                    />
                    <u-icon class="uicon" name="arrow-right" color="#7D7F82"></u-icon>
                </view>
            </view>
        </view>

        <!-- 联系方式 -->
        <view class="form-section-title">联系方式</view>
        <view class="form-top">
            <!-- <view class="form-item bt-1">
                <view class="form-item-left">手机号码</view>
                <view class="form-item-right">
                    <u-input
                        :clearable="false"
                        height="40"
                        input-align="right"
                        class="uinput"
                        placeholder="请输入手机号码"
                        :custom-style="inputStyle"
                        :placeholder-style="placeStyle"
                        v-model="mobile"
                    />
                    <u-icon class="uicon" name="arrow-right" color="#7D7F82"></u-icon>
                </view>
            </view> -->
            <view class="form-item bt-1">
                <view class="form-item-left">
                    <text style="color: red">*</text>
                    微信号
                </view>
                <view class="form-item-right">
                    <u-input :clearable="false" height="40" input-align="right" class="uinput" placeholder="请输入微信号" :custom-style="inputStyle" :placeholder-style="placeStyle" v-model="weixin" />
                    <u-icon class="uicon" name="arrow-right" color="#7D7F82"></u-icon>
                </view>
            </view>
            <view class="form-item bt-1" @click="openAddressPop">
                <view class="form-item-left">
                    <text style="color: red">*</text>
                    所在地区
                </view>
                <view class="form-item-right">
                    <text :class="address.province ? 'value-text' : 'none'">{{ comAdress }}</text>
                    <u-icon class="uicon" name="arrow-right" color="#7D7F82"></u-icon>
                </view>
            </view>
            <!-- <view class="form-item">
                <view class="form-item-left">位置信息</view>
                <view class="form-item-right">
                    <u-input :clearable="false" height="40" input-align="right" class="uinput" placeholder="请输入位置信息" :custom-style="inputStyle" :placeholder-style="placeStyle" v-model="area" />
                    <view class="iconfont address-icon" @click="openMap">&#xe618;</view>
                </view>
            </view> -->
        </view>

        <!-- 个人详情 -->
        <view class="form-section-title">个人详情</view>
        <view class="form-top">
            <view class="form-item bt-1">
                <view class="form-item-left">个人简介</view>
                <view class="form-item-right form-item-column">
                    <u-input
                        type="textarea"
                        :clearable="false"
                        height="100"
                        input-align="left"
                        class="uinput bio-input"
                        placeholder="请输入个人简介"
                        :custom-style="inputStyle"
                        :placeholder-style="placeStyle"
                        v-model="note"
                    />
                </view>
            </view>
            <view class="form-item">
                <view class="form-item-left">标签</view>
                <view class="form-item-right form-item-column">
                    <view class="tags-container">
                        <view v-for="(tag, index) in tagList" :key="tag.id" class="tag-item">
                            {{ tag.name }}
                            <view class="tag-close" @click.stop="removeTag(index)">×</view>
                        </view>
                        <view class="tag-add" @click="showTagInputDialog">+添加</view>
                    </view>
                </view>
            </view>
        </view>

        <u-calendar v-model="showBirthdayPicker" mode="date" @change="confirmBirthday"></u-calendar>

        <u-picker v-model="showRegionPicker" mode="region" @confirm="confirmRegion"></u-picker>

        <!-- 添加标签弹窗 -->
        <u-popup v-model="showTagInput" mode="center" width="80%" height="auto" border-radius="16" @close="onPopupClose">
            <view class="tag-popup">
                <view class="tag-popup-title">添加标签</view>
                <u-input v-model="newTag" placeholder="请输入标签名称" :custom-style="inputStyle" :placeholder-style="placeStyle" height="80" border @confirm="confirmAddTag"></u-input>
                <view class="tag-popup-buttons">
                    <button class="cancel-btn" @click="cancelAddTag">取消</button>
                    <button class="confirm-btn" @click="confirmAddTag">确定</button>
                </view>
            </view>
        </u-popup>

        <view class="enter-btn safe-area-inset-bottom">
            <button @click="editUser">保存</button>
        </view>
        <l-address ref="address" :address="address" @change="addressChange"></l-address>
    </view>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import lAddress from '@/components/addressPop.vue';

export default {
    components: { lAddress },
    computed: {
        ...mapState(['userInfo', 'placeStyle']),
        comAdress() {
            return this.address.province ? this.address.province.name + this.address.city?.name + this.address.area?.name : '请选择省市区街道办';
        },
    },
    data() {
        return {
            headimgurl: 'https://cdn.uviewui.com/uview/album/1.jpg',
            nickname: '',
            mobile: '',
            area: '',
            password: '',
            inputStyle: {
                color: '#666666',
                fontSize: '32rpx',
            },
            // 新增字段
            note: '', // 个人简介
            sex: '1', // 性别 0-未设置 1-男 2-女
            birthday: '', // 生日
            sf_cm: '', // 身高(cm)
            tz_kg: '', // 体重(kg)
            weixin: '', // 微信号
            real_name: '', // 真实姓名
            tagList: [], // 标签对象数组，包含id和name
            region: '', // 地区
            address: { province: '', city: '', area: '' },
            province: '',
            city: '',
            area: '',
            // 选择器相关变量
            showBirthdayPicker: false,
            showRegionPicker: false,
            showTagInput: false,
            newTag: '',
            sexList: [
                {
                    name: '男',
                    disabled: false,
                    value: '1',
                },
                {
                    name: '女',
                    disabled: false,
                    value: '2',
                },
            ],
            longitude: '', // 经度
            latitude: '', // 纬度
        };
    },
    onLoad(option) {
        // 解构用户信息，包含新增字段
        const { headimgurl, nickname, mobile, area, note, sex, birthday, sf_cm, tz_kg, weixin, province_code_txt, area_code_txt, city_code_txt, real_name } = this.userInfo;

        // 设置基本信息
        this.headimgurl = headimgurl;
        this.nickname = nickname;
        this.mobile = mobile;
        this.area = area;

        // 设置新增字段
        this.note = note || '';
        this.sex = Number(sex) || 1;
        this.birthday = birthday || '';
        this.sf_cm = sf_cm || '';
        this.tz_kg = tz_kg || '';
        this.weixin = this.$util.decodeBase64(weixin) || '';
        this.real_name = real_name || '';
        this.province = province_code_txt?.code || '';
        this.city = city_code_txt?.code || '';
        this.area = area_code_txt?.code || '';
        if (province_code_txt) {
            this.address = {
                province: {
                    code: province_code_txt?.code || '',
                    name: province_code_txt?.name || '',
                },
                area: {
                    code: area_code_txt?.code || '',
                    name: area_code_txt?.name || '',
                },
                city: {
                    code: city_code_txt?.code || '',
                    name: city_code_txt?.name || '',
                },
            };
        }

        // 加载用户标签
        this.loadUserTags();
        this.getLocation();
        // 头像裁剪监听
        uni.$on('uAvatarCropper', async path => {
            this.headimgurl = await this.$util.uploadFile(path);
        });
    },
    onUnload() {
        uni.$off('uAvatarCropper');
    },
    methods: {
        ...mapActions(['VAgetUser']),
        chooseImg() {
            uni.navigateTo({
                url: `./uAvatarCropper?destWidth=300&rectWidth=300&fileType=jpg`,
            });
        },
        getLocation() {
            uni.getLocation({
                type: 'wgs84',
                success: res => {
                    this.longitude = res.longitude;
                    this.latitude = res.latitude;
                },
            });
        },
        async onChooseAvatar(e) {
            this.headimgurl = await this.$util.uploadFile(e.detail.avatarUrl);
        },

        // 生日选择确认
        confirmBirthday(e) {
            console.log('e--', e);
            this.birthday = e.result;
        },

        // 地区选择确认
        confirmRegion(e) {
            this.region = e.province.label + ' ' + e.city.label + ' ' + e.area.label;
        },

        // 加载用户标签
        loadUserTags() {
            if (this.userInfo && this.userInfo.tag_data_arr && this.userInfo.tag_data_arr.length > 0) {
                this.tagList = JSON.parse(JSON.stringify(this.userInfo.tag_data_arr));
            } else {
                this.tagList = [];
            }
        },

        // 标签相关方法
        showTagInputDialog() {
            this.showTagInput = true;
            this.newTag = '';
        },

        cancelAddTag() {
            this.showTagInput = false;
        },

        async confirmAddTag() {
            if (!this.newTag.trim()) {
                uni.showToast({
                    title: '标签不能为空',
                    icon: 'none',
                });
                return;
            }

            // 创建标签
            try {
                uni.showLoading({ title: '创建标签中...' });
                const res = await this.$api.createTag({
                    name: this.newTag.trim(),
                    access_token: uni.getStorageSync('token'),
                });

                // 添加标签到列表，使用响应式方法
                const newTag = {
                    id: res.data.tag_id,
                    name: this.newTag.trim(),
                    create_uid: this.userInfo.uid,
                };

                // 确保响应式更新
                const newTagList = [...this.tagList, newTag];
                this.tagList = newTagList;

                // 强制关闭弹窗并确保DOM更新
                this.$nextTick(() => {
                    this.showTagInput = false;

                    // 延迟显示成功提示，避免与加载提示冲突
                    setTimeout(() => {
                        uni.showToast({
                            title: '标签添加成功',
                            icon: 'none',
                        });
                    }, 100);
                });
            } catch (error) {
                console.error('创建标签失败:', error);
                uni.showToast({
                    title: '标签添加失败',
                    icon: 'none',
                });
            } finally {
                uni.hideLoading();
            }
        },

        removeTag(index) {
            console.log('删除标签, 索引:', index, '当前标签列表:', this.tagList);
            // 创建新数组确保响应式更新
            const newTagList = [...this.tagList];
            newTagList.splice(index, 1);
            this.tagList = newTagList;
            console.log('删除后的标签列表:', this.tagList);
        },

        async openMap() {
            let locationRes = await uni.chooseLocation({});
            console.log('locationRes--', locationRes);
            if (locationRes.address) {
                this.area = locationRes.address;
                var reg = /.+?(省|市|自治区|自治州|县|区)/g; // 省市区的正则
                console.log(this.area.match(reg));
            }
        },

        addressChange(value) {
            // if (value.street) {
            if (value.province) {
                this.province = value.province.code;
            }
            if (value.area) {
                this.area = value.area.code;
            }
            if (value.city) {
                this.city = value.city.code;
            }
            // this.street = value.street.code;
            // }
            console.log('value--', value);
            this.address = value;
        },

        openAddressPop() {
            // console.log(this.$refs.address)
            this.$refs.address.open();
        },

        async editUser() {
            // 基础字段验证
            // if (!this.$u.test.mobile(this.mobile)) {
            //     uni.showToast({
            //         title: '手机格式不正确',
            //         icon: 'error',
            //     });
            //     return;
            // }

            // 身高体重验证
            // if (this.height && (!this.$u.test.number(this.height) || this.height < 50 || this.height > 300)) {
            //     uni.showToast({
            //         title: '身高范围应在50-300cm',
            //         icon: 'none',
            //     });
            //     return;
            // }

            // if (this.weight && (!this.$u.test.number(this.weight) || this.weight < 20 || this.weight > 500)) {
            //     uni.showToast({
            //         title: '体重范围应在20-500kg',
            //         icon: 'none',
            //     });
            //     return;
            // }
            if (!this.real_name) {
                uni.showToast({
                    title: '真实姓名不能为空',
                    icon: 'none',
                });
                return;
            }
            if (!this.weixin) {
                uni.showToast({
                    title: '微信号不能为空',
                    icon: 'none',
                });
                return;
            }
            if(!this.province){
                uni.showToast({
                    title: '所在地区不能为空',
                    icon: 'none',
                });
                return;
            }


            // 处理标签ID列表
            const tagIds = this.tagList.map(tag => tag.id).join(',');

            // 提交所有字段，包含新增字段
            const res = await this.$api.updateUser({
                nickname: this.nickname,
                mobile: this.mobile,
                headimgurl: this.headimgurl,
                area_code: this.area,
                province_code: this.province,
                city_code: this.city,
                note: this.note,
                sex: this.sex,
                birthday: this.birthday,
                sf_cm: this.sf_cm,
                tz_kg: this.tz_kg,
                weixin: this.weixin,
                tag_name_str: tagIds, // 传递标签ID字符串
                access_token: uni.getStorageSync('token'),
                longitude: this.longitude,
                latitude: this.latitude,
                real_name: this.real_name,
            });
            uni.showToast({
                title: '修改成功',
            });
            this.VAgetUser();
            setTimeout(() => {
                uni.navigateBack();
            }, 600);
        },

        // 弹窗关闭事件处理
        onPopupClose() {
            console.log('弹窗关闭事件触发');
            this.newTag = '';
        },
    },
    watch: {
        tagList: {
            handler(newVal) {},
            deep: true,
        },
    },
};
</script>

<style lang="scss" scoped>
.content {
    padding: 0 28rpx;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    padding-bottom: 160rpx; // 为底部按钮留出空间
    min-height: 100vh;
}

.form-section-title {
    font-size: 32rpx;
    color: #8966ef;
    margin: 40rpx 10rpx 20rpx;
    font-weight: 600;
    position: relative;
    padding-left: 20rpx;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 8rpx;
        height: 32rpx;
        background: linear-gradient(135deg, #8966ef, #a584f2);
        border-radius: 4rpx;
    }
}

.content-avatar {
    margin-top: 30rpx;
    height: 320rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);
    border-radius: 24rpx;
    border: none;
    box-shadow: 0 8rpx 32rpx rgba(137, 102, 239, 0.1);
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4rpx;
        background: linear-gradient(90deg, #8966ef, #f0cc6c, #8966ef);
    }

    .avatar-info {
        display: flex;
        align-items: center;
        flex-direction: column;
        text {
            font-size: 32rpx;
            font-weight: 500;
            color: #666;
            margin-top: 24rpx;
        }
    }
    .avatar-img {
        position: relative;
        width: 160rpx;
        height: 160rpx;
        .phone-btn {
            position: absolute;
            top: 0;
            right: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: none;
            width: 100%;
            height: 100%;
            padding: 0;
            margin: 0;
            border-radius: 50%;
            z-index: 3;
        }
        image {
            border-radius: 50%;
            width: 160rpx;
            height: 160rpx;
            border: 6rpx solid #fff;
            box-shadow: 0 8rpx 24rpx rgba(137, 102, 239, 0.2);
        }
        .avatar-upload {
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;
            // background: linear-gradient(135deg, rgba(137, 102, 239, 0.8), rgba(165, 132, 242, 0.8));
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            // width: 160rpx;
            // height: 160rpx;
            // border-radius: 50%;
            z-index: 2;
            transition: all 0.3s ease;

            &:hover {
                background: linear-gradient(135deg, rgba(137, 102, 239, 0.9), rgba(165, 132, 242, 0.9));
            }
        }
    }
}

.form-top {
    padding: 0 32rpx;
    background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);
    border-radius: 24rpx;
    border: none;
    box-shadow: 0 8rpx 32rpx rgba(137, 102, 239, 0.08);
    margin-bottom: 24rpx;

    .form-item {
        padding: 32rpx 0;
        display: flex;
        align-items: center;
        position: relative;
        transition: all 0.3s ease;

        &:hover {
            background: rgba(137, 102, 239, 0.02);
            border-radius: 16rpx;
            margin: 0 -16rpx;
            padding: 32rpx 16rpx;
        }

        &.bt-1 {
            border-bottom: 1px solid rgba(137, 102, 239, 0.1);
        }

        &-left {
            font-size: 32rpx;
            font-weight: 500;
            color: #333333;
            width: 200rpx;
            position: relative;
        }

        &-right {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            width: 100%;
            color: #ffffff;

            .address-icon {
                color: #8966ef;
                padding: 0 0rpx 0 20rpx;
            }
            .none {
                color: #9a9a9a;
                font-size: 30rpx;
            }
            .value-text {
                color: #333333;
                font-size: 32rpx;
                font-weight: 400;
            }
            .uinput {
                width: 95%;
            }
            .uicon {
                margin-left: 18rpx;
                color: #8966ef !important;
            }
        }

        &-province {
            padding: 20rpx 30rpx;
            background: #ffffff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #efefef;
            &-left {
                font-size: 32rpx;
                color: #333333;
                display: flex;
                flex-direction: column;
                &-title {
                    margin-bottom: 8rpx;
                }
                &-desc {
                    font-size: 24rpx;
                    color: #999999;
                }
            }

            &-right {
                font-size: 30upx;
                color: #999999;
                display: flex;
                align-items: center;
                justify-content: flex-end;

                image {
                    margin-left: 12upx;
                    height: 24upx;
                    width: 24upx;
                }
            }
        }
    }
}
.form-box {
    background: #333333;
    margin-top: 24rpx;
    border-radius: 16rpx;
    padding: 0 25rpx;
    box-sizing: border-box;
    // .form-box-name{
    // 	text-align: right;
    // }

    .avaurl-box {
        display: flex;
        align-items: center;
        flex-direction: row-reverse;
        image {
            width: 100rpx;
            height: 100rpx;
            border-radius: 50%;
            margin-right: 10rpx;
        }
    }
    .flex-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .address-icon {
            color: #ffffff;
            padding: 0 0rpx 0 20rpx;
        }
        &-input {
            flex: 3;
        }
    }
}
.save-btn {
    position: fixed;
    bottom: 0;
    left: 28rpx;
    width: 695rpx;
    button {
        height: 98rpx;
        line-height: 98rpx;
        border-radius: 16rpx;
        color: #ffffff;
        background: #1aad4c;
    }
}

.enter-btn {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    padding: 24rpx 28rpx;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.95) 30%, rgba(255, 255, 255, 1) 100%);
    backdrop-filter: blur(10rpx);

    button {
        border-radius: 50rpx;
        width: 690rpx;
        height: 100rpx;
        background: linear-gradient(135deg, #8966ef 0%, #a584f2 50%, #f0cc6c 100%);
        line-height: 100rpx;
        font-size: 36rpx;
        font-weight: 600;
        color: #ffffff;
        box-shadow: 0 12rpx 40rpx rgba(137, 102, 239, 0.3);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        &:active {
            transform: translateY(2rpx);
            box-shadow: 0 8rpx 24rpx rgba(137, 102, 239, 0.4);

            &::before {
                left: 100%;
            }
        }
    }
}

// 添加性别单选按钮样式
.gender-radio-group {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 24rpx;
}

.gender-radio-label {
    display: flex;
    align-items: center;
    margin-left: 30rpx;
    color: #333333;

    radio {
        transform: scale(0.9);
        margin-right: 8rpx;
    }

    text {
        font-size: 32rpx;
        font-weight: 400;
    }
}

// 标签样式
.tags-container {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    padding: 16rpx 0;
    gap: 16rpx;
}

.tag-item {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #8966ef 0%, #a584f2 50%, #f0cc6c 100%);
    color: #fff;
    padding: 12rpx 20rpx;
    border-radius: 32rpx;
    font-size: 26rpx;
    font-weight: 500;
    box-shadow: 0 6rpx 20rpx rgba(137, 102, 239, 0.25);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), transparent);
        border-radius: 32rpx;
    }

    &:active {
        transform: translateY(2rpx);
        box-shadow: 0 4rpx 12rpx rgba(137, 102, 239, 0.3);
    }
}

.tag-close {
    margin-left: 12rpx;
    font-size: 28rpx;
    font-weight: bold;
    width: 32rpx;
    height: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
    z-index: 100;
    &:active {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0.9);
    }
}

.tag-add {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #8966ef 0%, #a584f2 50%, #f0cc6c 100%);
    color: #fff;
    padding: 12rpx 20rpx;
    border-radius: 32rpx;
    font-size: 26rpx;
    font-weight: 500;
    box-shadow: 0 6rpx 20rpx rgba(240, 204, 108, 0.25);
    transition: all 0.3s ease;
    border: 2rpx dashed rgba(137, 102, 239, 0.3);

    &:active {
        transform: translateY(2rpx);
        box-shadow: 0 4rpx 12rpx rgba(240, 204, 108, 0.3);
    }
}

// 标签弹窗样式
.tag-popup {
    padding: 30rpx;
    background-color: #fff;
}

.tag-popup-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
    text-align: center;
}

.tag-popup-buttons {
    display: flex;
    justify-content: space-between;
    margin-top: 30rpx;
}

.tag-popup-buttons button {
    width: 45%;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 16rpx;
    font-size: 30rpx;
}

.cancel-btn {
    background-color: #ffffff;
    color: #333333;
    border: 1px solid #ededed;
}

.confirm-btn {
    background: linear-gradient(135deg, #8966ef 0%, #a584f2 50%, #f0cc6c 100%);
    color: #fff;
}
</style>
