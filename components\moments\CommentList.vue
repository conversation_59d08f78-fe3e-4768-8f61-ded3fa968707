<template>
	<view class="comments-container">
		<view class="comments-title">
			<text>评论 {{comments.length}}</text>
		</view>
		
		<view class="comments-list" v-if="comments && comments.length > 0">
			<view class="comment-item" v-for="(comment, index) in comments" :key="comment.id">
				<image class="avatar" :src="comment.userAvatar " mode="aspectFill"></image>
				<view class="comment-content">
					<view class="comment-user">{{comment.userName}}</view>
					<view class="comment-text">{{comment.content}}</view>
					<view class="comment-time">{{comment.createTime}}</view>
					
					<!-- 删除按钮（仅自己的评论可见） -->
					<view class="delete-btn" v-if="comment.userId === currentUserId" @click="handleDelete(comment.id, index)">
						<u-icon name="trash" color="#999" size="16"></u-icon>
					</view>
				</view>
			</view>
		</view>
		
		<view v-else class="empty-comments">
			<u-empty mode="comment" text="暂无评论"></u-empty>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		comments: {
			type: Array,
			default: () => []
		},
		currentUserId: {
			type: [String, Number],
			default: ''
		}
	},
	methods: {
		// 删除评论
		handleDelete(commentId, index) {
			this.$emit('delete', commentId, index);
		}
	}
}
</script>

<style lang="scss" scoped>
.comments-container {
	background-color: #FFFFFF;
	padding: 30rpx;
	
	.comments-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}
	
	.empty-comments {
		padding: 40rpx 0;
	}
	
	.comments-list {
		.comment-item {
			display: flex;
			margin-bottom: 30rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.avatar {
				width: 60rpx;
				height: 60rpx;
				border-radius: 50%;
				margin-right: 20rpx;
			}
			
			.comment-content {
				flex: 1;
				position: relative;
				
				.comment-user {
					font-size: 26rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 6rpx;
				}
				
				.comment-text {
					font-size: 26rpx;
					color: #333;
					line-height: 1.5;
				}
				
				.comment-time {
					font-size: 22rpx;
					color: #999;
					margin-top: 6rpx;
				}
				
				.delete-btn {
					position: absolute;
					right: 0;
					top: 0;
					padding: 10rpx;
				}
			}
		}
	}
}
</style> 