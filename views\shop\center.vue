<template>
    <view class="trade-center">
        <!-- 顶部收入展示区域 -->
        <view class="income-header">
            <view class="income-bg">
                <view class="income-content">
                    <view class="income-title">我在跃汇街小程序赚了</view>
                    <view class="income-amount">
                        <text class="currency">¥</text>
                        <text class="amount">{{ totalIncome }}</text>
                    </view>
                    <view class="income-subtitle">总收入</view>
                </view>
                <view class="income-decoration">
                    <view class="decoration-circle decoration-circle-1"></view>
                    <view class="decoration-circle decoration-circle-2"></view>
                    <view class="decoration-circle decoration-circle-3"></view>
                </view>
            </view>
        </view>

        <!-- 提现入口 -->
        <view class="withdraw-section">
            <view class="withdraw-card" @click="goToWithdraw">
                <view class="withdraw-content">
                    <view class="withdraw-icon">
                        <text class="icon-text">💰</text>
                    </view>
                    <view class="withdraw-info">
                        <view class="withdraw-title">商家提现</view>
                        <view class="withdraw-desc">可提现余额：¥{{ shopWithdrawBalance }}</view>
                        <!-- <view class="withdraw-record-link" @click.stop="goToWithdrawRecord">
                            <text>查看提现记录</text>
                            <u-icon name="arrow-right" color="#8966ef" size="20"></u-icon>
                        </view> -->
                    </view>
                </view>
                <view class="withdraw-arrow">
                    <u-icon name="arrow-right" color="#8966ef" size="32"></u-icon>
                </view>
            </view>
        </view>

        <!-- 内容区域 -->
        <view class="content-area">
            <!-- 我卖出的 -->
            <view class="tab-content">
                <view class="icon-text-grid">
                    <view class="icon-text-item" v-for="(item, index) in soldList" :key="index" @click="toDetail(item)">
                        <view class="item-icon">
                            <image :src="item.image" mode="aspectFill"></image>
                        </view>
                        <view class="item-text">
                            <view class="item-title">{{ item.title }}</view>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 数据统计卡片 -->
        <view class="stats-section">
            <!-- 统计标题 -->
            <view class="stats-header">
                <view class="stats-title">
                    <text class="stats-icon">📊</text>
                    <text class="stats-text">数据统计</text>
                </view>
                <view class="header-actions">
                    <view v-if="statsLoading" class="loading-indicator">
                        <text class="loading-text">加载中...</text>
                    </view>
                    <!-- <view v-else class="refresh-btn" @tap="loadTradeStats">
                        <text class="refresh-icon">🔄</text>
                    </view> -->
                </view>
            </view>

            <!-- 发布在卖统计 -->
            <view class="stats-category">
                <view class="stats-row stats-row-2">
                    <view class="stat-card" v-for="(stat, index) in publishStats" :key="index">
                        <view class="stat-value">{{ stat.value }}</view>
                        <view class="stat-label">{{ stat.label }}</view>
                    </view>
                </view>
            </view>

            <!-- 订单统计 -->
            <view class="stats-category">
                <view class="stats-row">
                    <view class="stat-card" v-for="(stat, index) in orderStats" :key="index">
                        <view class="stat-value">{{ stat.value }}</view>
                        <view class="stat-label">{{ stat.label }}</view>
                    </view>
                </view>
            </view>

            <!-- 金额统计 -->
            <view class="stats-category">
                <view class="stats-row">
                    <view class="stat-card" v-for="(stat, index) in amountStats" :key="index" @click.stop="goToWithdrawRecord(index)">
                        <view class="stat-value">{{ stat.value }}</view>
                        <view class="stat-label">{{ stat.label }}</view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
import { getShopData } from '@/utils/vmeitime-http/shop.js';

export default {
    data() {
        return {
            totalIncome: '0.00',
            currentTab: 0,
            tabList: [{ name: '我发布的' }, { name: '我卖出的' }, { name: '我买到的' }],
            // 发布在卖统计
            publishStats: [
                { label: '发布总数', value: '0' },
                { label: '在卖总数', value: '0' },
            ],
            // 订单统计
            orderStats: [
                { label: '订单总数', value: '0' },
                { label: '未完成订单', value: '0' },
                { label: '待支付订单', value: '0' },
            ],
            // 金额统计
            amountStats: [
                { label: '总销售额', value: '¥0.00' },
                { label: '结算金额', value: '¥0.00' },
                { label: '总收入', value: '¥0.00' },
            ],
            publishedList: [],
            soldList: [
                {
                    id: 1,
                    title: '我发布的',
                    image: 'https://friend.wdaoyun.cn/static/images/index/fb.png',
                    url: '/views/shop/goodsList',
                },
                {
                    id: 2,
                    title: '我卖出的',
                    image: 'https://friend.wdaoyun.cn/static/images/index/jine.png',
                    url: '/views/shop/shopOrders',
                },
                {
                    id: 3,
                    title: '我购买的',
                    image: 'https://friend.wdaoyun.cn/static/images/index/gm.png',
                    url: '/views/shop/myOrders',
                },
            ],
            boughtList: [],
            loading: false,
            statsLoading: false,
            statsError: false,
            noMore: false,
            page: 1,
            pagesize: 10,
            defaultImage: 'https://static.wdaoyun.com/friend/2025-07-02/ef0fac060cb6f1abfde647fe1a55776d.png',
            shopWithdrawBalance: '0.00', // 商家可提现余额
        };
    },
    computed: {
        ...mapState(['hasLogin', 'userInfo']),
        hasData() {
            return this.publishedList.length > 0 || this.boughtList.length > 0;
        },
    },
    onLoad() {
        this.$api.checkWuNo({
            access_token: uni.getStorageSync('token'),
            wu_no: '777323244091203',
            order_id: 114,
        });
        if (!this.hasLogin) {
            uni.showToast({
                title: '请先登录',
                icon: 'none',
            });
            setTimeout(() => {
                uni.navigateBack();
            }, 1500);
            return;
        }
        this.init();
    },
    onShow() {
        // 页面显示时刷新数据（从提现页面返回时会触发）
        if (this.hasLogin) {
            this.loadShopWithdrawBalance();
        }
    },
    onPullDownRefresh() {
        this.init();
        setTimeout(() => {
            uni.stopPullDownRefresh();
        }, 1000);
    },
    onReachBottom() {
        this.loadMore();
    },
    methods: {
        // 初始化数据
        init() {
            this.page = 1;
            this.noMore = false;
            this.loadTradeStats();
        },

        // 加载交易统计数据
        async loadTradeStats() {
            if (this.statsLoading) return;

            this.statsLoading = true;
            this.statsError = false;

            try {
                const res = await getShopData({
                    access_token: uni.getStorageSync('token'),
                });

                const data = res.data;

                // 更新总收入显示（总收入金额除以100转换为元）
                this.totalIncome = this.formatAmount(data.withdraw_amount);

                // 更新发布在卖统计
                this.publishStats = [
                    { label: '发布总数', value: this.formatNumber(data.push_good_num || 0) },
                    { label: '在卖总数', value: this.formatNumber(data.push_good_salt_num || 0) },
                ];

                // 更新订单统计
                this.orderStats = [
                    { label: '订单总数', value: this.formatNumber(data.order_num || 0) },
                    { label: '未完成订单', value: this.formatNumber(data.no_success_order_num || 0) },
                    { label: '待支付订单', value: this.formatNumber(data.padding_pay_order_num || 0) },
                ];

                // 更新金额统计（所有金额都除以100转换为元）
                this.amountStats = [
                    { label: '总销售额', value: `¥${this.formatAmount(data.total_salt_amount)}` },
                    { label: '结算金额', value: `¥${this.formatAmount(data.js_salt_amount)}` },
                    { label: '总收入', value: `¥${this.formatAmount(data.withdraw_amount)}` },
                ];

                this.statsError = false;

                // 加载商家可提现余额
                this.loadShopWithdrawBalance();
            } catch (error) {
                console.error('加载统计数据失败:', error);
                this.statsError = true;
                uni.showToast({
                    title: '网络错误，请重试',
                    icon: 'none',
                });
            } finally {
                this.statsLoading = false;
            }
        },

        // Tab切换
        onTabChange(index) {
            this.currentTab = index;
            this.page = 1;
            this.noMore = false;
        },

        // 加载更多
        loadMore() {
            if (this.loading || this.noMore) return;
            this.page++;
        },

        // 获取状态样式类
        getStatusClass(status) {
            const classMap = {
                1: 'status-selling',
                2: 'status-sold',
                3: 'status-offline',
            };
            return classMap[status] || '';
        },

        // 获取状态文本
        getStatusText(status) {
            const textMap = {
                1: '在售',
                2: '已售',
                3: '下架',
            };
            return textMap[status] || '未知';
        },

        // 格式化金额显示
        formatAmount(amount) {
            if (amount === null || amount === undefined) return '0.00';
            const num = Number(amount);
            return num.toFixed(2);
        },

        // 格式化数字显示（添加千分位分隔符）
        formatNumber(num) {
            if (num === null || num === undefined) return '0';
            return Number(num).toLocaleString();
        },

        toDetail(item) {
            uni.navigateTo({
                url: item.url,
            });
        },

        // 加载商家可提现余额
        async loadShopWithdrawBalance() {
            try {
                const res = await this.$api.getUserWallet({
                    access_token: uni.getStorageSync('token'),
                });

                // 获取good_balance字段并转换为元
                const goodBalance = Math.floor(res.data.balance.good_balance || '0');
                this.shopWithdrawBalance = (goodBalance / 100).toFixed(2);
            } catch (error) {
                console.error('获取商家可提现余额失败:', error);
            }
        },

        // 跳转到商家提现页面
        goToWithdraw() {
            // const balance = parseFloat(this.shopWithdrawBalance);
            // if (balance <= 0) {
            //     uni.showToast({
            //         title: '暂无可提现余额',
            //         icon: 'none',
            //     });
            //     return;
            // }

            uni.navigateTo({
                url: '/views/shop/withdraw',
            });
        },

        // 跳转到商家提现记录页面
        goToWithdrawRecord(index) {
            if (index != 2) {
                return;
            }
            uni.navigateTo({
                url: '/views/shop/withdrawRecord',
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.trade-center {
    min-height: 100vh;
   background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);

    // 顶部收入展示区域
    .income-header {
        padding: 40rpx 30rpx 40rpx;

        .income-bg {
            background: linear-gradient(135deg, #8966ef, #a584f2, #f0cc6c);
            border-radius: 24rpx;
            padding: 50rpx 40rpx;
            position: relative;
            overflow: hidden;

            .income-content {
                position: relative;
                z-index: 2;
                text-align: center;

                .income-title {
                    color: rgba(255, 255, 255, 0.9);
                    font-size: 28rpx;
                    margin-bottom: 20rpx;
                }

                .income-amount {
                    display: flex;
                    align-items: baseline;
                    justify-content: center;
                    margin-bottom: 16rpx;

                    .currency {
                        color: #fff;
                        font-size: 36rpx;
                        font-weight: 500;
                        margin-right: 8rpx;
                    }

                    .amount {
                        color: #fff;
                        font-size: 72rpx;
                        font-weight: 700;
                        text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
                    }
                }

                .income-subtitle {
                    color: rgba(255, 255, 255, 0.8);
                    font-size: 24rpx;
                }
            }

            .income-decoration {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                z-index: 1;

                .decoration-circle {
                    position: absolute;
                    border-radius: 50%;
                    background: rgba(255, 255, 255, 0.1);

                    &.decoration-circle-1 {
                        width: 120rpx;
                        height: 120rpx;
                        top: -30rpx;
                        right: 50rpx;
                    }

                    &.decoration-circle-2 {
                        width: 80rpx;
                        height: 80rpx;
                        bottom: 20rpx;
                        left: 40rpx;
                        background: rgba(240, 204, 108, 0.2);
                    }

                    &.decoration-circle-3 {
                        width: 60rpx;
                        height: 60rpx;
                        top: 50%;
                        right: 20rpx;
                        background: rgba(255, 255, 255, 0.15);
                    }
                }
            }
        }
    }

    // 数据统计区域
    .stats-section {
        margin: 0rpx 30rpx 30rpx;
        position: relative;
        z-index: 3;

        .stats-header {
            margin-bottom: 30rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .stats-title {
                display: flex;
                align-items: center;

                .stats-icon {
                    font-size: 32rpx;
                    margin-right: 12rpx;
                }

                .stats-text {
                    color: #333;
                    font-size: 32rpx;
                    font-weight: 600;
                }
            }

            .header-actions {
                display: flex;
                align-items: center;

                .loading-indicator {
                    .loading-text {
                        color: #8966ef;
                        font-size: 24rpx;
                    }
                }

                .refresh-btn {
                    padding: 8rpx;
                    border-radius: 8rpx;
                    transition: background-color 0.2s ease;

                    &:active {
                        background-color: rgba(137, 102, 239, 0.1);
                    }

                    .refresh-icon {
                        font-size: 28rpx;
                        color: #8966ef;
                    }
                }
            }
        }

        .stats-category {
            margin-bottom: 30rpx;

            &:last-child {
                margin-bottom: 0;
            }

            .category-title {
                display: flex;
                align-items: center;
                margin-bottom: 16rpx;

                .category-icon {
                    font-size: 28rpx;
                    margin-right: 10rpx;
                }

                .category-text {
                    color: #666;
                    font-size: 28rpx;
                    font-weight: 500;
                }
            }

            .stats-row {
                background: #fff;
                border-radius: 20rpx;
                padding: 30rpx 20rpx;
                display: grid;
                gap: 20rpx;
                box-shadow: 0 8rpx 24rpx rgba(137, 102, 239, 0.1);

                // 默认使用3列布局，自动换行
                grid-template-columns: repeat(3, 1fr);

                &.stats-row-2 {
                    grid-template-columns: repeat(2, 1fr);
                }
                .stat-card {
                    text-align: center;

                    .stat-value {
                        color: #333;
                        font-size: 32rpx;
                        font-weight: 600;
                        margin-bottom: 8rpx;
                        display: block;
                    }

                    .stat-label {
                        color: #666;
                        font-size: 24rpx;
                        line-height: 1.2;
                    }
                }
            }
        }
    }

    // 提现区域
    .withdraw-section {
        margin: 0rpx 30rpx 30rpx;

        .withdraw-card {
            background: #fff;
            border-radius: 20rpx;
            padding: 30rpx;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 8rpx 24rpx rgba(137, 102, 239, 0.1);
            border: 1rpx solid #e8e8e8;
            transition: transform 0.2s ease;

            &:active {
                transform: scale(0.98);
            }

            .withdraw-content {
                display: flex;
                align-items: center;

                .withdraw-icon {
                    width: 80rpx;
                    height: 80rpx;
                    background: linear-gradient(135deg, #8966ef, #a584f2);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 24rpx;

                    .icon-text {
                        font-size: 36rpx;
                    }
                }

                .withdraw-info {
                    .withdraw-title {
                        color: #333;
                        font-size: 32rpx;
                        font-weight: 600;
                        margin-bottom: 8rpx;
                    }

                    .withdraw-desc {
                        color: #8966ef;
                        font-size: 26rpx;
                        font-weight: 500;
                    }

                    .withdraw-record-link {
                        display: flex;
                        align-items: center;
                        margin-top: 8rpx;
                        font-size: 22rpx;
                        color: #8966ef;
                        opacity: 0.8;

                        text {
                            margin-right: 4rpx;
                        }

                        &:active {
                            opacity: 1;
                        }
                    }
                }
            }

            .withdraw-arrow {
                opacity: 0.6;
            }
        }
    }

    // Tab区域
    .category-tabs {
        margin: 0 30rpx 20rpx;
        background: #fff;
        border-radius: 16rpx;
        overflow: hidden;
        box-shadow: 0 4rpx 12rpx rgba(137, 102, 239, 0.08);
    }

    // 内容区域
    .content-area {
        margin: 0 30rpx 30rpx;

        .tab-content {
            background: #fff;
            border-radius: 16rpx;
            overflow: hidden;
            padding: 30rpx 20rpx;

            .icon-text-grid {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 30rpx;

                .icon-text-item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    text-align: center;
                    transition: transform 0.2s ease;

                    &:active {
                        transform: scale(0.95);
                    }

                    .item-icon {
                        position: relative;
                        width: 64rpx;
                        height: 64rpx;
                        margin-bottom: 16rpx;

                        image {
                            width: 100%;
                            height: 100%;
                        }

                        .status-badge {
                            position: absolute;
                            top: -8rpx;
                            right: -8rpx;
                            padding: 4rpx 8rpx;
                            border-radius: 12rpx;
                            font-size: 18rpx;
                            color: #fff;
                            font-weight: 500;

                            &.status-selling {
                                background: #8966ef;
                            }

                            &.status-sold {
                                background: #07c160;
                            }

                            &.status-offline {
                                background: #999;
                            }
                        }
                    }

                    .item-text {
                        width: 100%;

                        .item-title {
                            color: #333;
                            font-size: 26rpx;
                            font-weight: 500;
                            margin-bottom: 8rpx;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            line-height: 1.3;
                        }

                        .item-price {
                            color: #8966ef;
                            font-size: 28rpx;
                            font-weight: 600;
                            margin-bottom: 4rpx;
                        }

                        .item-meta {
                            color: #999;
                            font-size: 22rpx;
                            line-height: 1.2;
                        }
                    }
                }
            }

            .empty-state {
                padding: 100rpx 30rpx;
                text-align: center;

                .empty-image {
                    width: 300rpx;
                    height: 236rpx;
                    margin-bottom: 40rpx;
                }

                .empty-text {
                    color: #999;
                    font-size: 32rpx;
                    margin-bottom: 16rpx;
                }

                .empty-tip {
                    color: #8966ef;
                    font-size: 28rpx;
                }
            }
        }
    }
}
</style>
