<template>
    <view class="chat-container" :style="pageHeight">
        <view class="box-1">
            <scroll-view
                class="message-list"
                scroll-y
                refresher-background="transparent"
                style="height: 100%"
                @refresherrefresh="refresherrefresh"
                :refresher-enabled="true"
                :scroll-with-animation="false"
                :refresher-triggered="scrollView.refresherTriggered"
                :scroll-into-view="scrollView.intoView"
                @touchstart="touchStart"
                @touchend="touchEnd"
                scroll-anchoring="true"
                :enhanced="true"
                :show-scrollbar="false"
                :id="scrollViewId"
            >
                <!-- 加载更多提示 -->
                <view class="load-more" v-if="isLoading">
                    <u-loading size="30" mode="circle" color="#2979ff"></u-loading>
                    <text class="load-text">加载历史消息中...</text>
                </view>
                <view class="top-space"></view>
                <!-- 没有更多消息提示 -->
                <view class="no-more" v-if="noMoreMessages && messageList.length > 0">
                    <text>没有更多消息了</text>
                </view>

                <!-- 聊天消息 -->
                <block v-for="(item, index) in messageList" :key="item.id">
                    <!-- 消息日期 -->
                    <view class="message-time" v-if="showMessageTime(item, index)">
                        {{ formatChatTime(item.addtime) }}
                    </view>

                    <!-- 消息气泡 -->
                    <view :class="['message-item', item.uid === currentUid ? 'message-mine' : 'message-other']" :id="'msg-' + item.id">
                        <!-- 头像 -->
                        <view class="avatar-wrapper">
                            <image class="avatar" :src="item.uid === currentUid ? currentUserAvatar : item.user.headimgurl" mode="aspectFill"></image>
                        </view>

                        <!-- 消息内容 -->
                        <view class="message-content">
                            <view class="nickname" v-if="item.uid !== currentUid">{{ item.user.nickname }}</view>

                            <!-- 文本消息 -->
                            <view class="message-bubble" v-if="item.message_type === '1'">
                                <text class="message-text">{{ item.content }}</text>
                            </view>

                            <!-- 图片消息 (预留) -->
                            <view class="message-bubble image-message" v-else-if="item.message_type === '2'">
                                <view class="placeholder">
                                    <u-icon name="photo" size="50"></u-icon>
                                    <text>图片消息</text>
                                </view>
                            </view>

                            <!-- 视频消息 (预留) -->
                            <view class="message-bubble video-message" v-else-if="item.message_type === '3'">
                                <view class="placeholder">
                                    <u-icon name="video" size="50"></u-icon>
                                    <text>视频消息</text>
                                </view>
                            </view>

                            <!-- 语音消息 (预留) -->
                            <view class="message-bubble audio-message" v-else-if="item.message_type === '4'">
                                <view class="placeholder">
                                    <u-icon name="mic" size="50"></u-icon>
                                    <text>语音消息 {{ item.duration }}s</text>
                                </view>
                            </view>

                            <!-- 消息状态指示器 (仅显示在自己发送的消息下方) -->
                            <view class="message-status" v-if="item.uid === currentUid && item.sendStatus !== undefined">
                                <text v-if="item.sendStatus === MESSAGE_STATUS.SENDING" class="status-text sending">发送中...</text>
                                <text v-else-if="item.sendStatus === MESSAGE_STATUS.SENT" class="status-text sent">已发送</text>
                                <text v-else-if="item.sendStatus === MESSAGE_STATUS.READ" class="status-text read">已读</text>
                            </view>
                        </view>
                    </view>
                </block>

                <!-- 底部占位区域，确保最后一条消息不被输入框遮挡 -->
                <view class="bottom-space"></view>
            </scroll-view>
        </view>

        <view class="input-area">
            <view class="input-box">
                <!-- 输入框 -->
                <input
                    class="message-input"
                    v-model="messageText"
                    type="text"
                    confirm-type="send"
                    placeholder="请输入消息..."
                    @confirm="sendTextMessage"
                    @focus="onInputFocus"
                    @blur="onInputBlur"
                    cursor-spacing="10"
                />

                <!-- 发送按钮 -->
                <view class="send-btn" :class="{ 'send-btn-active': messageText.trim().length > 0 }" @tap="sendTextMessage">发送</view>
            </view>

            <!-- 媒体按钮区域 -->
            <view class="media-actions" v-if="false">
                <view class="action-btn" @tap="onImageAction">
                    <u-icon name="photo" size="48" color="#2979ff"></u-icon>
                </view>
                <view class="action-btn" @tap="onVideoAction">
                    <u-icon name="camera" size="48" color="#2979ff"></u-icon>
                </view>
                <view class="action-btn" @tap="onAudioAction">
                    <u-icon name="mic" size="48" color="#2979ff"></u-icon>
                </view>
            </view>
        </view>

        <!-- 未读消息提示 -->
        <view class="unread-tip" v-if="showUnreadTip && unreadCount > 0" @tap="scrollToUnread">
            <view class="unread-count">{{ unreadCount > 99 ? '99+' : unreadCount }}</view>
            <text class="unread-text">新消息</text>
            <view class="unread-arrow">
                <u-icon name="arrow-down" size="18" color="#FFFFFF"></u-icon>
            </view>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
import { getMsgDetail, sendMsg } from '@/utils/vmeitime-http/msg';

export default {
    data() {
        return {
            // 消息状态常量
            MESSAGE_STATUS: {
                SENDING: 0, // 发送中
                SENT: 1, // 已发送
                READ: 2, // 已读
            },
            // 滚动容器
            scrollView: {
                refresherTriggered: false,
                intoView: '',
                safeAreaHeight: 0,
            },
            isLoading: false,
            messageList: [],
            messageText: '',
            currentUid: '',
            currentUserAvatar: '',
            targetUserId: '',
            targetUserInfo: {},
            lastId: 0,
            noMoreMessages: false,
            // 时间显示间隔（5分钟）
            timeInterval: 5 * 60 * 1000,
            // 是否允许滚动到底部
            autoScrollToBottom: true,
            // 触摸相关
            touchStartY: 0,
            // 随机ID，避免多个聊天页面ID冲突
            scrollViewId: `scroll-view-${Date.now()}`,
            pagesize: 15,
            // 滚动位置记忆
            oldScrollTop: 0,
            isLoadingMore: false,
            // 轮询相关数据
            pollTimer: null,
            isPollingStopped: false,
            isPolling: false,
            lastNewMsgId: 0,
            // 轮询间隔时间（毫秒）
            pollInterval: 3000,
            // 未读消息相关
            unreadCount: 0,
            showUnreadTip: false,
            lastReadMsgId: 0,
            // 最近一条发送中的消息ID
            sendingMessageId: null,
            // 最近发送的消息缓存，用于去重
            recentSentMessages: [],
            // 加载更多防抖
            loadMoreDebounce: true,
            // 第一条消息距离顶部的相对位置
            firstMessageOffsetTop: 0,
            touchStartTime: 0,
            sessionId: '',
        };
    },

    computed: {
        ...mapState(['userInfo']),
        // 页面高度
        pageHeight() {
            const safeAreaHeight = this.scrollView.safeAreaHeight;
            if (safeAreaHeight > 0) {
                return `height: calc(${safeAreaHeight}px - var(--window-top));`;
            }
            return '';
        },
    },

    watch: {
        // 监听消息列表变化，更新分组
        messageList: {
            handler(newVal) {
                // 不再需要更新分组逻辑
                if (newVal.length > 0) {
                    // 更新最新消息ID
                    const latestMsg = newVal[newVal.length - 1];
                    if (latestMsg && latestMsg.id) {
                        this.lastNewMsgId = latestMsg.id;
                    }
                }
            },
            deep: true,
        },
    },

    onLoad(options) {
        this.targetUserId = options.userId || '';
        this.sessionId = options.sessionId || '';
        const nickname = decodeURIComponent(options.username) || '';
        const avatar = decodeURIComponent(options.avatar) || '';
        this.targetUserInfo = {
            uid: options.userId || '',
            nickname: nickname || '',
            headimgurl: avatar || '',
        };

        // 设置导航栏标题
        uni.setNavigationBarTitle({
            title: this.targetUserInfo.nickname,
        });

        // 设置当前用户信息
        this.currentUid = this.userInfo.uid || '';
        this.currentUserAvatar = this.userInfo.headimgurl || '/static/images/we.png';

        // #ifdef H5
        this.scrollView.safeAreaHeight = uni.getSystemInfoSync().safeArea.height;
        // #endif

        // 加载消息
        this.loadMessages(false);

        // 启动轮询
        this.startPolling();
    },

    onShow() {
        // 页面显示时确保滚动到底部
        setTimeout(() => {
            this.scrollToBottom(true);
        }, 500);

        // 如果轮询已停止，重新启动轮询
        if (this.isPollingStopped) {
            this.startPolling();
        }
    },

    // 页面初始化完成
    onReady() {
        // 页面初始化完成后确保滚动到底部
        setTimeout(() => {
            this.scrollToBottom(true);
        }, 500);
    },

    // 页面隐藏
    onHide() {
        // 页面隐藏时暂停轮询
        this.stopPolling();
    },

    // 页面卸载
    onUnload() {
        // 页面卸载时清除轮询定时器
        this.stopPolling();
        // 重置刷新状态
        this.scrollView.refresherTriggered = false;
        this.isLoading = false;
    },

    methods: {
        // 清理过期的消息缓存
        cleanupOldSentMessages() {
            if (this.recentSentMessages.length === 0) return;

            const now = new Date();
            // 清理超过5分钟的消息缓存
            this.recentSentMessages = this.recentSentMessages.filter(msg => {
                const msgTime = msg.time;
                const timeDiff = now - msgTime;
                return timeDiff < 5 * 60 * 1000; // 只保留5分钟内的消息
            });
        },

        // 启动轮询
        startPolling() {
            if (this.pollTimer) {
                clearInterval(this.pollTimer);
            }

            this.isPollingStopped = false;

            // 立即执行一次
            this.fetchLatestMessages();

            // 设置轮询定时器
            this.pollTimer = setInterval(() => {
                // 每次轮询前清理过期的消息缓存
                this.cleanupOldSentMessages();
                this.fetchLatestMessages();
            }, this.pollInterval);
        },

        // 停止轮询
        stopPolling() {
            if (this.pollTimer) {
                clearInterval(this.pollTimer);
                this.pollTimer = null;
            }
            this.isPollingStopped = true;
        },

        // 获取最新消息（用于轮询）
        async fetchLatestMessages() {
            // 如果已经在轮询中，避免重复请求
            if (this.isPolling) return;

            this.isPolling = true;

            try {
                const res = await getMsgDetail({
                    msg_session_id: this.sessionId,
                    pagesize: 5, // 获取较少数量的最新消息
                    access_token: uni.getStorageSync('token'),
                });

                if (res && res.data && res.data.list && res.data.list.length > 0) {
                    const newMessages = res.data.list;

                    // 找出真正的新消息（需要进行更严格的去重）
                    const actualNewMessages = newMessages.filter(serverMsg => {
                        // 1. 基于ID去重 - 检查消息ID是否已存在于消息列表中
                        const existsById = this.messageList.some(existing => existing.id === serverMsg.id);
                        if (existsById) return false;

                        // 2. 基于内容和发送者去重 - 检查是否是本地刚发送的消息
                        const isRecentSentMessage = this.recentSentMessages.some(recent => {
                            // 如果服务器ID与记录的服务器ID匹配
                            if (recent.serverId && recent.serverId === serverMsg.id) {
                                return true;
                            }

                            // 基于内容和发送者检查
                            if (recent.content === serverMsg.content && recent.uid === serverMsg.uid) {
                                // 检查时间差是否在合理范围内（2分钟内）
                                const serverTime = new Date(serverMsg.addtime.replace(/-/g, '/'));
                                const timeDiff = Math.abs(serverTime - recent.time);
                                return timeDiff < 2 * 60 * 1000; // 2分钟内的消息视为同一条
                            }

                            return false;
                        });

                        return !isRecentSentMessage;
                    });

                    if (actualNewMessages.length > 0) {
                        // 计算未读消息数量（只计算对方发送的消息）
                        const otherUserNewMessages = actualNewMessages.filter(msg => msg.uid !== this.currentUid);

                        // 如果有对方发来的新消息且用户不在底部，增加未读计数
                        if (otherUserNewMessages.length > 0 && !this.autoScrollToBottom) {
                            this.unreadCount += otherUserNewMessages.length;
                            this.showUnreadTip = true;
                        }

                        // 有新消息，合并到消息列表
                        const mergedList = [...this.messageList, ...actualNewMessages];

                        // 确保消息按时间从旧到新排序
                        this.messageList = mergedList.sort((a, b) => {
                            const timeA = new Date(a.addtime.replace(/-/g, '/')).getTime();
                            const timeB = new Date(b.addtime.replace(/-/g, '/')).getTime();
                            return timeA - timeB; // 从旧到新排序
                        });

                        // 更新最新消息ID
                        if (this.messageList.length > 0) {
                            const latestMsg = this.messageList[this.messageList.length - 1];
                            this.lastNewMsgId = latestMsg.id;
                        }

                        // 如果用户处于底部或新消息是自己发送的，自动滚动到底部
                        const hasOwnNewMessage = actualNewMessages.some(msg => msg.uid === this.currentUid);
                        if (this.autoScrollToBottom || hasOwnNewMessage) {
                            this.$nextTick(() => {
                                this.scrollToBottom(true);
                                // 滚动到底部时清除未读消息计数
                                this.clearUnreadCount();
                            });
                        }
                    }
                }
            } catch (error) {
                console.error('轮询获取消息失败', error);
                // 轮询失败不显示错误提示，避免频繁打扰用户
            } finally {
                this.isPolling = false;
            }
        },

        // 加载消息
        async loadMessages(isLoadingMore = false) {
            // 如果正在刷新或者没有更多消息且是加载更多操作，则不再加载
            if (this.scrollView.refresherTriggered || (this.noMoreMessages && isLoadingMore)) return;

            this.isLoading = true;

            try {
                const res = await getMsgDetail({
                    msg_session_id: this.sessionId,
                    pagesize: this.pagesize,
                    last_id: isLoadingMore ? this.lastId : 0, // 首次加载不使用lastId，获取最新消息
                    access_token: uni.getStorageSync('token'),
                });

                if (res && res.data && res.data.list) {
                    const newMessages = res.data.list;

                    if (isLoadingMore) {
                        // 如果是加载更多历史消息，将消息加到列表前面
                        this.messageList = [...newMessages, ...this.messageList].sort((a, b) => {
                            const timeA = new Date(a.addtime.replace(/-/g, '/')).getTime();
                            const timeB = new Date(b.addtime.replace(/-/g, '/')).getTime();
                            return timeA - timeB; // 从旧到新排序
                        });
                    } else {
                        // 如果是首次加载，直接替换消息列表
                        this.messageList = newMessages.sort((a, b) => {
                            const timeA = new Date(a.addtime.replace(/-/g, '/')).getTime();
                            const timeB = new Date(b.addtime.replace(/-/g, '/')).getTime();
                            return timeA - timeB; // 从旧到新排序
                        });
                    }

                    // 更新last_id
                    if (newMessages.length > 0) {
                        this.lastId = newMessages[newMessages.length - 1].id;
                    }

                    // 更新最新消息ID
                    if (this.messageList.length > 0) {
                        const latestMsg = this.messageList[this.messageList.length - 1];
                        this.lastNewMsgId = latestMsg.id;
                    }

                    // 检查是否还有更多消息
                    if (newMessages.length < this.pagesize) {
                        this.noMoreMessages = true;
                    }

                    // 首次加载时滚动到底部
                    if (!isLoadingMore && this.messageList.length > 0) {
                        this.$nextTick(() => {
                            this.scrollToBottom(true);
                        });
                    }

                    // 设置防抖为可用状态
                    this.loadMoreDebounce = true;
                }
            } catch (error) {
                console.error('获取消息失败', error);
                uni.showToast({
                    title: '获取消息失败',
                    icon: 'none',
                });
            } finally {
                this.isLoading = false;
                this.isLoadingMore = false;
            }
        },

        // 记录触摸开始位置
        touchStart(e) {
            this.touchStartY = e.changedTouches[0].pageY;
            this.touchStartTime = Date.now();

            // 用户主动滚动时，暂时禁用自动滚动
            this.autoScrollToBottom = false;
        },

        // 处理触摸结束
        touchEnd(e) {
            const touchEndY = e.changedTouches[0].pageY;
            const touchEndTime = Date.now();
            const touchDuration = touchEndTime - this.touchStartTime;

            // 计算滑动距离和方向
            const distanceY = touchEndY - this.touchStartY;

            // 如果滑动时间过短或距离过小，可能是点击而非滑动，不改变自动滚动状态
            if (Math.abs(distanceY) < 10 || touchDuration < 100) {
                return;
            }

            // 如果是向上滑动，说明用户正在查看历史消息，不自动滚动到底部
            if (distanceY < 0) {
                this.autoScrollToBottom = false;
            } else {
                // 检测是否滚动到了底部附近，如果是则重新启用自动滚动
                this.checkIfNearBottom();
            }
        },

        // 检查是否接近底部
        checkIfNearBottom() {
            const query = uni.createSelectorQuery().in(this);
            query.select(`#${this.scrollViewId}`).boundingClientRect();
            query.selectViewport().scrollOffset();
            query.exec(res => {
                if (res[0] && res[1]) {
                    const scrollViewHeight = res[0].height;
                    const scrollTop = res[1].scrollTop;
                    const scrollHeight = res[1].scrollHeight;

                    // 如果距离底部不足50px，视为接近底部，启用自动滚动
                    const isNearBottom = scrollHeight - scrollTop - scrollViewHeight < 50;
                    if (isNearBottom) {
                        this.autoScrollToBottom = true;

                        // 如果接近底部，清除未读消息提示
                        if (this.showUnreadTip) {
                            this.clearUnreadCount();
                        }
                    }
                }
            });
        },

        // 输入框获取焦点
        onInputFocus() {
            // 输入框获取焦点时滚动到底部
            // this.autoScrollToBottom = true;
            // setTimeout(() => {
            //   this.scrollToBottom(true);
            // }, 300);
        },

        // 输入框失去焦点
        onInputBlur() {
            // 可以在这里添加额外的处理逻辑
        },

        // 发送文本消息
        async sendTextMessage() {
            if (!this.messageText.trim()) return;

            const msgContent = this.messageText.trim();
            this.messageText = '';

            // 重新启用自动滚动
            this.autoScrollToBottom = true;

            try {
                // 生成临时ID
                const tempId = Date.now().toString();
                this.sendingMessageId = tempId;

                // 记录当前发送的消息，用于后续去重
                const sentTime = new Date();
                this.recentSentMessages.push({
                    id: tempId,
                    content: msgContent,
                    time: sentTime,
                    uid: this.currentUid,
                });

                // 最多保留最近5条发送记录
                if (this.recentSentMessages.length > 5) {
                    this.recentSentMessages.shift();
                }

                // 构建本地消息对象（乐观更新UI）
                const localMessage = {
                    id: tempId,
                    addtime: this.$util.getCurrentDate(),
                    content: msgContent,
                    uid: this.currentUid,
                    to_uid: this.targetUserId,
                    message_type: '1', // 文本消息
                    is_read: '0',
                    sendStatus: this.MESSAGE_STATUS.SENDING, // 初始状态为发送中
                    user: {
                        uid: this.currentUid,
                        nickname: this.userInfo.nickname || '我',
                        headimgurl: this.currentUserAvatar,
                    },
                    to_user: this.targetUserInfo,
                };

                // 添加到消息列表末尾（最新消息）
                this.messageList.push(localMessage);

                // 强制在下一个事件循环滚动到底部
                this.$nextTick(() => {
                    this.scrollToBottom(true);
                });

                // 发送到服务器
                const response = await sendMsg({
                    to_uid: this.targetUserId,
                    msg: msgContent,
                    message_type: 1, // 文本消息
                    access_token: uni.getStorageSync('token'),
                });

                // 更新消息状态为已发送
                const messageIndex = this.messageList.findIndex(msg => msg.id === tempId);
                if (messageIndex !== -1) {
                    // 如果服务器返回了实际ID，更新本地消息ID
                    if (response && response.data && response.data.id) {
                        // 同时更新缓存中的ID
                        const recentIndex = this.recentSentMessages.findIndex(msg => msg.id === tempId);
                        if (recentIndex !== -1) {
                            this.recentSentMessages[recentIndex].serverId = response.data.id;
                        }

                        this.messageList[messageIndex].id = response.data.id;
                    }
                    this.messageList[messageIndex].sendStatus = this.MESSAGE_STATUS.SENT;

                    // 5秒后自动隐藏状态提示
                    setTimeout(() => {
                        const updatedIndex = this.messageList.findIndex(msg => msg.id === (response?.data?.id || tempId));
                        if (updatedIndex !== -1) {
                            this.$set(this.messageList[updatedIndex], 'sendStatus', undefined);
                        }
                    }, 3000);
                }

                // 发送消息后立即获取最新消息，确保消息列表同步
                setTimeout(() => {
                    this.fetchLatestMessages();
                }, 500);
            } catch (error) {
                console.error('发送消息失败', error);

                // 更新消息状态为发送失败
                const messageIndex = this.messageList.findIndex(msg => msg.id === this.sendingMessageId);
                if (messageIndex !== -1) {
                    this.$set(this.messageList[messageIndex], 'sendStatus', -1); // -1 表示发送失败
                }

                uni.showToast({
                    title: '发送失败，请重试',
                    icon: 'none',
                });
            } finally {
                this.sendingMessageId = null;
            }
        },

        // 滚动到底部
        scrollToBottom(force = false) {
            // 如果不是强制滚动且用户禁用了自动滚动，则不执行
            if (!force && !this.autoScrollToBottom) return;

            setTimeout(() => {
                if (this.messageList.length > 0) {
                    const lastId = this.messageList[this.messageList.length - 1].id;
                    this.scrollView.intoView = 'msg-' + lastId;

                    // 使用原生滚动方法确保滚动到底部
                    const query = uni.createSelectorQuery().in(this);
                    query.select(`#${this.scrollViewId}`).boundingClientRect();
                    query.exec(res => {
                        if (res[0] && res[0].bottom) {
                            uni.pageScrollTo({
                                scrollTop: 99999,
                                duration: 100,
                            });

                            // 滚动到底部时清除未读消息提示
                            this.clearUnreadCount();
                        }
                    });
                }
            }, 200);
        },

        // 是否显示时间
        showMessageTime(item, index) {
            // 第一条消息总是显示时间
            if (index === 0) return true;

            const currentDate = new Date(item.addtime.replace(/-/g, '/'));
            const prevDate = new Date(this.messageList[index - 1].addtime.replace(/-/g, '/'));

            // 日期变化（跨天）时显示完整日期
            if (currentDate.toDateString() !== prevDate.toDateString()) {
                return true;
            }

            // 如果是对方发送的消息，每一条都显示时间
            if (item.uid !== this.currentUid) {
                return true;
            }

            // 如果是自己发送的消息，检查时间间隔
            const currentTime = currentDate.getTime();
            const prevTime = prevDate.getTime();

            // 超过5分钟间隔显示时间
            return currentTime - prevTime > this.timeInterval;
        },

        // 格式化聊天时间
        formatChatTime(timestamp) {
            if (!timestamp) return '';

            const msgDate = new Date(timestamp.replace(/-/g, '/'));
            const now = new Date();

            // 提取时间部分（时:分）
            const timeStr = msgDate.getHours().toString().padStart(2, '0') + ':' + msgDate.getMinutes().toString().padStart(2, '0');

            // 日期相同判断（同年同月同日）
            const isSameDay = (date1, date2) => {
                return date1.getFullYear() === date2.getFullYear() && date1.getMonth() === date2.getMonth() && date1.getDate() === date2.getDate();
            };

            // 今天只显示时间
            if (isSameDay(msgDate, now)) {
                return timeStr;
            }

            // 昨天
            const yesterday = new Date(now);
            yesterday.setDate(now.getDate() - 1);
            if (isSameDay(msgDate, yesterday)) {
                return '昨天 ' + timeStr;
            }

            // 本周内（显示星期+时间）
            const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
            const dayDiff = Math.floor((now - msgDate) / (24 * 60 * 60 * 1000));
            if (dayDiff < 7) {
                return weekdays[msgDate.getDay()] + ' ' + timeStr;
            }

            // 今年内（显示月日+时间）
            const isThisYear = now.getFullYear() === msgDate.getFullYear();
            if (isThisYear) {
                return msgDate.getMonth() + 1 + '月' + msgDate.getDate() + '日 ' + timeStr;
            }

            // 更早（显示年月日+时间）
            return msgDate.getFullYear() + '年' + (msgDate.getMonth() + 1) + '月' + msgDate.getDate() + '日 ' + timeStr;
        },

        // 媒体功能（预留）
        onImageAction() {
            uni.showToast({
                title: '图片功能开发中',
                icon: 'none',
            });
        },

        onVideoAction() {
            uni.showToast({
                title: '视频功能开发中',
                icon: 'none',
            });
        },

        onAudioAction() {
            uni.showToast({
                title: '语音功能开发中',
                icon: 'none',
            });
        },

        // 滚动到未读消息
        scrollToUnread() {
            this.scrollToBottom(true);
            this.clearUnreadCount();
        },

        // 清除未读消息计数
        clearUnreadCount() {
            this.unreadCount = 0;
            this.showUnreadTip = false;

            // 更新最后已读消息ID
            if (this.messageList.length > 0) {
                this.lastReadMsgId = this.messageList[this.messageList.length - 1].id;
            }
        },

        // 刷新消息
        async refresherrefresh() {
            if (!this.loadMoreDebounce) {
                return;
            }

            this.loadMoreDebounce = false;
            this.scrollView.refresherTriggered = true;

            try {
                // 记录第一条消息ID，用于保持位置
                let firstMsgId = '';
                if (this.messageList.length > 0) {
                    firstMsgId = this.messageList[0].id;
                }

                const res = await getMsgDetail({
                    msg_session_id: this.sessionId,
                    pagesize: this.pagesize,
                    last_id: this.lastId,
                    access_token: uni.getStorageSync('token'),
                });

                this.scrollView.refresherTriggered = false;

                if (res && res.data && res.data.list && res.data.list.length > 0) {
                    const historyMessages = res.data.list;

                    // 更新lastId为加载到的最早消息ID
                    if (historyMessages.length > 0) {
                        this.lastId = historyMessages[historyMessages.length - 1].id;
                    }

                    // 计算滚动到的元素ID
                    const selector = `msg-${historyMessages?.[0]?.id}`;

                    // 将历史消息添加到列表头部
                    this.messageList = [...historyMessages, ...this.messageList].sort((a, b) => {
                        const timeA = new Date(a.addtime.replace(/-/g, '/')).getTime();
                        const timeB = new Date(b.addtime.replace(/-/g, '/')).getTime();
                        return timeA - timeB; // 从旧到新排序
                    });

                    // 数据挂载后执行
                    this.$nextTick(() => {
                        // 设置当前滚动的位置
                        this.scrollView.intoView = selector;

                        if (historyMessages.length < this.pagesize) {
                            // 当前消息数据条数小于请求要求条数时，则无更多消息
                            this.noMoreMessages = true;
                        } else {
                            this.loadMoreDebounce = true;
                        }
                    });
                } else {
                    // 没有更多历史消息
                    this.noMoreMessages = true;
                }
            } catch (error) {
                console.error('加载历史消息失败', error);
                this.scrollView.refresherTriggered = false;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.chat-container {
    height: calc(100vh - var(--window-top));
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    align-content: center;
    justify-content: space-between;
    align-items: stretch;
    background-color: #f3f3f3;
}

.box-1 {
    width: 100%;
    height: 0;
    flex: 1 0 auto;
    box-sizing: content-box;
}

.message-list {
    height: 100%;
    background-color: #f3f3f3;
    padding: 0rpx 0rpx 30rpx;
    display: flex;
    flex-direction: column-reverse;
    flex-wrap: nowrap;
    align-content: flex-start;
    justify-content: flex-end;
    align-items: stretch;

    // 添加弹性容器，让内容自动在顶部
    &::before {
        content: '.';
        display: inline;
        visibility: hidden;
        line-height: 0;
        font-size: 0;
        flex: 1 0 auto;
        height: 1px;
    }
}

.input-area {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 20rpx 30rpx;
    background-color: #f8f8f8;
    border-top: 1px solid #e6e6e6;
    z-index: 100;
    box-shadow: 0 -2rpx 6rpx rgba(0, 0, 0, 0.05);

    .input-box {
        display: flex;
        align-items: center;

        .message-input {
            flex: 1;
            height: 80rpx;
            background-color: #fff;
            border-radius: 40rpx;
            padding: 0 30rpx;
            font-size: 28rpx;
            border: 1rpx solid #e6e6e6;
        }

        .send-btn {
            width: 120rpx;
            height: 80rpx;
            line-height: 80rpx;
            text-align: center;
            margin-left: 20rpx;
            font-size: 28rpx;
            color: #999;
            background-color: #f5f5f5;
            border-radius: 40rpx;

            &.send-btn-active {
                background-color: #07c160;
                color: #fff;
            }
        }
    }

    .media-actions {
        display: flex;
        justify-content: space-around;
        margin-top: 20rpx;

        .action-btn {
            width: 100rpx;
            height: 100rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }
    }
}

.load-more {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20rpx 0;
    height: 80rpx;
    border-radius: 8rpx;
    margin-bottom: 20rpx;

    .load-text {
        font-size: 28rpx;
        color: #2979ff;
        font-weight: 500;
        margin-left: 16rpx;
    }
}

.no-more {
    text-align: center;
    font-size: 24rpx;
    color: #999;
    padding: 20rpx 0;
    margin-bottom: 20rpx;
    position: relative;

    &::before,
    &::after {
        content: '';
        position: absolute;
        top: 50%;
        width: 60rpx;
        height: 1px;
        background: #ddd;
    }

    &::before {
        left: 50%;
        margin-left: 80rpx;
    }

    &::after {
        right: 50%;
        margin-right: 80rpx;
    }
}

.empty-messages {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400rpx;
}

.message-time {
    text-align: center;
    font-size: 24rpx;
    color: #999;
    padding: 10rpx 0;
    margin: 10rpx 0;
    position: relative;

    &::before,
    &::after {
        content: '';
        position: absolute;
        top: 50%;
        width: 80rpx;
        height: 1px;
        background: #ddd;
    }

    &::before {
        left: 50%;
        margin-left: 60rpx;
    }

    &::after {
        right: 50%;
        margin-right: 60rpx;
    }
}

.message-item {
    display: flex;
    margin: 30rpx 20rpx 0;
    &.message-mine {
        flex-direction: row-reverse;

        .message-content {
            align-items: flex-end;
            margin-right: 20rpx;
            margin-left: 80rpx;

            .message-bubble {
                background-color: #95ec69;
                color: #333;
                border-radius: 20rpx 0 20rpx 20rpx;

                &::after {
                    content: '';
                    position: absolute;
                    right: -10rpx;
                    top: 14rpx;
                    border-width: 10rpx;
                    border-style: solid;
                    border-color: transparent transparent transparent #95ec69;
                }
            }

            .message-status {
                margin-top: 8rpx;
                text-align: right;

                .status-text {
                    font-size: 24rpx;

                    &.sending {
                        color: #999;
                    }

                    &.sent {
                        color: #07c160;
                    }

                    &.read {
                        color: #07c160;
                    }
                }
            }
        }
    }

    &.message-other {
        .message-content {
            align-items: flex-start;
            margin-left: 20rpx;
            margin-right: 80rpx;

            .message-bubble {
                background-color: #fff;
                border-radius: 0 20rpx 20rpx 20rpx;

                &::before {
                    content: '';
                    position: absolute;
                    left: -10rpx;
                    top: 14rpx;
                    border-width: 10rpx;
                    border-style: solid;
                    border-color: transparent #fff transparent transparent;
                }
            }
        }
    }
}

.avatar-wrapper {
    width: 80rpx;
    height: 80rpx;
    flex-shrink: 0;

    .avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 8rpx;
    }
}

.message-content {
    display: flex;
    flex-direction: column;
    max-width: 70%;

    .nickname {
        font-size: 24rpx;
        color: #999;
        margin-bottom: 10rpx;
    }

    .message-bubble {
        position: relative;
        padding: 20rpx 24rpx;
        word-break: break-all;
        box-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);

        .message-text {
            font-size: 28rpx;
            line-height: 1.5;
        }
    }

    .image-message,
    .video-message,
    .audio-message {
        padding: 10rpx;

        .placeholder {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-width: 200rpx;
            min-height: 200rpx;
            padding: 20rpx;
            border: 1px dashed #ddd;
            border-radius: 8rpx;

            text {
                font-size: 24rpx;
                margin-top: 10rpx;
            }
        }
    }
}

.bottom-space {
    height: 120rpx;
    width: 100%;
}
.top-space {
    height: 30rpx;
    width: 100%;
}
/* 兼容iPhoneX */
@supports (bottom: constant(safe-area-inset-bottom)) {
    .input-area {
        padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
        padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
    }
}

/* 未读消息提示 */
.unread-tip {
    position: fixed;
    left: 50%;
    bottom: 180rpx;
    transform: translateX(-50%);
    background-color: rgba(7, 193, 96, 0.9);
    color: #fff;
    border-radius: 40rpx;
    padding: 12rpx 30rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);

    .unread-count {
        font-size: 24rpx;
        background-color: #ff3a30;
        color: #fff;
        border-radius: 20rpx;
        padding: 4rpx 12rpx;
        margin-right: 10rpx;
        min-width: 32rpx;
        text-align: center;
    }

    .unread-text {
        font-size: 26rpx;
        margin: 0 10rpx;
    }

    .unread-arrow {
        margin-left: 6rpx;
    }
}

.input-area {
    .input-box {
        display: flex;
        align-items: center;

        .message-input {
            flex: 1;
            height: 80rpx;
            background-color: #fff;
            border-radius: 40rpx;
            padding: 0 30rpx;
            font-size: 28rpx;
            border: 1rpx solid #e6e6e6;
        }

        .send-btn {
            width: 120rpx;
            height: 80rpx;
            line-height: 80rpx;
            text-align: center;
            margin-left: 20rpx;
            font-size: 28rpx;
            color: #999;
            background-color: #f5f5f5;
            border-radius: 40rpx;

            &.send-btn-active {
                background-color: #07c160;
                color: #fff;
            }
        }
    }
}

.flex-col {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.flex-grow {
    flex: 1;
}
</style>
