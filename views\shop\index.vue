<template>
    <view class="content">
        <!-- 头部搜索栏 -->
        <!-- <view class="header-search">
            <view class="search-box">
                <u-icon name="search" color="#999" size="32"></u-icon>
                <text class="search-placeholder">搜索商品</text>
            </view>
        </view> -->

        <!-- 筛选标签 - 暂时注释 -->
        <!-- <view class="filter-tabs">
            <view class="tab-item" :class="{ active: current === index }" v-for="(item, index) in tabs" :key="item.id" @click="changeTab(index)">
                <text class="tab-text">{{ item.name }}</text>
                <view v-if="item.id === 3" class="tab-icon">
                    <u-icon name="arrow-up" :color="getIconColor('desc')" size="20"></u-icon>
                    <u-icon name="arrow-down" :color="getIconColor('asc')" size="20"></u-icon>
                </view>
            </view>
        </view> -->

        <!-- 商品列表 -->
        <view class="shop-wrap">
            <view class="shop-item" v-for="(item, index) in list" :key="item.id" @click="toShopDetail(item)">
                <!-- 用户信息 -->
                <view class="shop-item-user" v-if="item.web_user">
                    <view class="user-avatar">
                        <u-image width="48rpx" height="48rpx" :src="item.web_user.headimgurl" mode="aspectFill" border-radius="50%"></u-image>
                    </view>
                    <text class="user-nickname">{{ item.web_user.nickname }}</text>
                </view>

                <view class="shop-item-img">
                    <image  :src="item.cover_img" mode="aspectFill" ></image>
                </view>
                <view class="shop-item-content">
                    <view class="shop-item-name u-line-2">{{ item.title }}</view>
                    <view class="shop-item-detail">
                        <view class="shop-item-price">
                            <text class="price-symbol">¥</text>
                            <text class="price-value">{{ getMinPrice(item.sku_list) }}</text>
                            <text class="price-up" v-if="getMaxPrice(item.sku_list) !== getMinPrice(item.sku_list)">起</text>
                        </view>
                        <view class="shop-item-views">
                            <u-icon name="eye" color="#999" size="24"></u-icon>
                            <text>{{ item.view_num || 0 }}</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 加载更多提示 -->
        <view class="load-more" v-if="list.length > 0">
            <text v-if="hasMore">加载更多...</text>
            <text v-else>没有更多商品了</text>
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-if="list.length === 0 && !loading">
            <u-icon name="shopping-bag" color="#ccc" size="120"></u-icon>
            <text class="empty-text">暂无商品</text>
            <text class="empty-desc">快去发布第一个商品吧</text>
        </view>

        <!-- 底部操作栏 -->
        <view class="bottom-bar" v-if="hasLogin">
            <view class="bottom-btn" @click="goToMyShop">
                <u-icon name="moments-circel-fill" color="#8966ef" size="50"></u-icon>
                <text>我的橱窗</text>
            </view>
            <view class="bottom-btn" @click="goToPublish">
                <u-icon name="plus-circle" color="#8966ef" size="50"></u-icon>
                <text>发布商品</text>
            </view>
        </view>
        <vip-popup v-model="showVip" @single-pay="handleSinglePay" @member-pay="handleMemberPay" @close="handleClose"></vip-popup>
    </view>
</template>

<script>
import { mapState } from 'vuex';
import vipPopup from '@/components/vip-popup.vue';

export default {
    components: { vipPopup },
    computed: {
        ...mapState(['hasLogin', 'isValidMember']),
    },
    data() {
        return {
            showVip: false,
            tabs: [
                {
                    id: 1,
                    name: '综合',
                },
                {
                    id: 2,
                    name: '销量',
                },
                {
                    name: '价格',
                    id: 3,
                },
            ],
            bannerData: [],
            current: 0,
            sort: 'asc',
            list: [],
            page: 1,
            pagesize: 10,
            hasMore: true,
            loading: false,
            channelCode: 'inside', // inside-非渠道，gzhwz-公众号文章，gzh_1-公众号电池，gzh_2-公众号电商，index_1-设备首页顶部，index_2-设备首页浮窗，menu_1-菜单
        };
    },
    watch: {},
    onLoad(e) {
        if (e.channel_code) {
            this.channelCode = e.channel_code;
            if (e.channel_code == 'gzh_1') {
                uni.navigateTo({
                    url: `./goodsBattery?channel_code=${this.channelCode}`,
                });
            }
        }
        if (e.id) {
            let url;
            if (e.id == 7) {
                url = `./goodsBattery?channel_code=${this.channelCode}`;
            } else {
                url = `./shopDetail?id=${e.id}&channel_code=${this.channelCode}`;
            }
            uni.navigateTo({
                url: url,
            });
        }
    },
    onShow() {
        this.init();
    },
    onShareAppMessage: function (res) {
        this.showSharePanel = false;
        let shareObj = {};
        shareObj = {
            title: `${this.$t('views.shop.share.title')}`,
            path: `views/develop/shop?channel_code=${this.channelCode}`,
            imageUrl: this.$familyImg,
        };
        return shareObj;
    },
    onShareTimeline: function (res) {
        return {
            title: `${this.$t('views.shop.share.title')}`,
            path: `channel_code=pyq_1`,
            imageUrl: this.$familyImg,
        };
    },
    onHide() {},
    onPullDownRefresh: function () {
        setTimeout(() => {
            uni.stopPullDownRefresh();
        }, 500);
        this.init();
    },
    onReachBottom() {
        console.log('触底加载，hasMore:', this.hasMore);
        if (this.hasMore && !this.loading) {
            this.page++;
            this.getGoodsList();
        }
    },

    methods: {
        loginSuccess() {
            this.init();
        },
        // 初始化
        init() {
            this.page = 1;
            this.hasMore = true;
            this.list = [];
            this.getGoodsList();
        },

        getIconColor(sort) {
            return this.current === 2 && this.sort === sort ? '#8966ef' : '#999';
        },

        // 获取商品列表 - 使用新的API
        async getGoodsList() {
            if (this.loading) return;

            this.loading = true;
            if (this.page === 1) {
                uni.showLoading({
                    title: '加载中...',
                });
            }

            try {
                const res = await this.$api.getAllShopGoodList({
                    access_token: uni.getStorageSync('token'),
                    page: this.page,
                    pagesize: this.pagesize,
                });

                const { list, total } = res.data;

                // 处理数据格式
                const processedList = list.map(item => ({
                    ...item,
                    // 确保图片字段正确
                    cover_img: item.cover_img || '',
                    // 解析images字段
                    images: typeof item.images === 'string' ? JSON.parse(item.images || '[]') : item.images || [],
                    // 确保sku_list存在
                    sku_list: item.sku_list || [],
                }));

                if (this.page === 1) {
                    this.list = processedList;
                } else {
                    this.list = this.list.concat(processedList);
                }

                // 判断是否还有更多数据
                this.hasMore = this.list.length < total;
            } catch (error) {
                uni.hideLoading();
            } finally {
                this.loading = false;
                uni.hideLoading();
            }
        },

        // 获取SKU最低价格
        getMinPrice(skuList) {
            if (!skuList || skuList.length === 0) return '0.00';
            const prices = skuList.map(sku => parseFloat(sku.price || 0));
            return Math.min(...prices).toFixed(2);
        },

        // 获取SKU最高价格
        getMaxPrice(skuList) {
            if (!skuList || skuList.length === 0) return '0.00';
            const prices = skuList.map(sku => parseFloat(sku.price || 0));
            return Math.max(...prices).toFixed(2);
        },

        // 跳转到我的橱窗
        goToMyShop() {
            uni.navigateTo({
                url: './goodsList',
            });
        },

        // 跳转到发布商品
        goToPublish() {
            if (!this.isValidMember) {
                this.showVip = true;
                return;
            }
            uni.navigateTo({
                url: './shopPublish',
            });
        },

        toShopDetail(item) {
            uni.navigateTo({
                url: `./detail?id=${item.id}`,
            });
        },

        routeTo(url) {
            uni.navigateTo({
                url: url,
            });
        },
        bannerClick(index) {
            this.routeTo(this.bannerData[index].url);
        },

        // 切换选项栏
        changeTab(index) {
            this.current = parseInt(index);
            switch (this.current) {
                case 0:
                    this.sortedProducts('all');
                    break;
                case 1:
                    this.sortedProducts('sales');
                    break;
                case 2:
                    this.sortedProducts('price');
                    this.sort = this.sort === 'desc' ? 'asc' : 'desc';
                    break;
            }
        },

        sortedProducts(type) {
            // 根据 sortCriteria 和 sortOrder 的值决定排序顺序
            this.list = this.list.slice().sort((a, b) => {
                if (type === 'all') {
                    // 先比较销量
                    if (a.price !== b.price) {
                        return a.price - b.price;
                    }
                    // 销量相同则按价格排序
                    return a.sale_num - b.sale_num;
                } else if (type === 'sales') {
                    return a.sale_num - b.sale_num;
                } else if (type === 'price') {
                    return this.sort === 'asc' ? a.price - b.price : b.price - a.price;
                }
                return 0;
            });
        },

        change(index) {
            this.current = index;
        },
        // 处理会员支付
        async handleMemberPay(paymentData) {
            console.log('会员支付:', paymentData);

            try {
                uni.showLoading({
                    title: '处理中...',
                });

                // 创建会员订单
                const orderRes = await this.$api.createMemberOrder({
                    good_id: paymentData.product.id,
                    num: 1,
                    access_token: uni.getStorageSync('token'),
                });

                if (orderRes.status !== 0) {
                    throw new Error(orderRes.msg || '创建订单失败');
                }

                // 获取用户openid（小程序支付需要）
                const openid = this.userInfo.openid;
                if (!openid) {
                    throw new Error('获取用户信息失败，请重新登录');
                }

                // 调用支付接口
                const payRes = await this.$api.createMiniPay({
                    openid: openid,
                    orderid: orderRes.data.order_no,
                    access_token: uni.getStorageSync('token'),
                });

                if (payRes.status !== 0) {
                    throw new Error(payRes.msg || '支付失败');
                }

                // 调用微信支付
                await this.requestPayment(payRes.data);

                uni.hideLoading();
                uni.showToast({
                    title: '开通成功',
                    icon: 'success',
                });

                // 更新用户信息
                await this.VAgetUser();
            } catch (error) {
                console.error('会员支付失败:', error);
                uni.hideLoading();
                uni.showToast({
                    title: error.message || '支付失败，请重试',
                    icon: 'none',
                });
            }
        },

        // 调用微信支付
        async requestPayment(paymentData) {
            return new Promise((resolve, reject) => {
                uni.requestPayment({
                    provider: 'wxpay',
                    timeStamp: paymentData.timeStamp,
                    nonceStr: paymentData.nonceStr,
                    package: paymentData.package,
                    signType: paymentData.signType,
                    paySign: paymentData.paySign,
                    success: res => {
                        console.log('支付成功:', res);
                        resolve(res);
                    },
                    fail: err => {
                        console.error('支付失败:', err);
                        reject(new Error('支付失败'));
                    },
                });
            });
        },
        handleClose() {
            this.showVip = false;
        },
    },
};
</script>

<style lang="scss" scoped>
// 引入项目主题色变量
$theme-primary: #8966ef;
$theme-primary-light: #a584f2;
$theme-secondary: #f0cc6c;
$border-color: #f0f0f0;
$text-color: #333;
$text-gray: #666;
$text-light: #999;
$bg-gray: #f7f8f7;

.content {
    min-height: 100vh;
   background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);
    padding-bottom: 20rpx;
}

// 头部搜索栏
.header-search {
    padding: 20rpx;
    background: white;
    border-bottom: 1rpx solid $border-color;

    .search-box {
        display: flex;
        align-items: center;
        background: #f8f9fa;
        border-radius: 50rpx;
        padding: 20rpx 30rpx;

        .search-placeholder {
            margin-left: 16rpx;
            color: $text-light;
            font-size: 28rpx;
        }
    }
}

// 筛选标签 - 暂时注释的样式
.filter-tabs {
    display: flex;
    background: white;
    padding: 0 20rpx;
    border-bottom: 1rpx solid $border-color;

    .tab-item {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 24rpx 32rpx;
        position: relative;

        .tab-text {
            font-size: 28rpx;
            color: $text-gray;
            font-weight: 400;
        }

        .tab-icon {
            margin-left: 8rpx;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        &.active {
            .tab-text {
                color: $theme-primary;
                font-weight: 500;
            }

            &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 40rpx;
                height: 4rpx;
                background: $theme-primary;
                border-radius: 2rpx;
            }
        }
    }
}

// 商品列表
.shop-wrap {
    padding: 20rpx;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    .shop-item {
        width: 345rpx;
        background: white;
        border-radius: 16rpx;
        margin-bottom: 20rpx;
        overflow: hidden;
        box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
        border: 1rpx solid $border-color;
        transition: all 0.3s ease;

        &:active {
            transform: scale(0.98);
            box-shadow: 0 4rpx 20rpx rgba(137, 102, 239, 0.1);
        }

        .shop-item-img {
            width: 100%;
            height: 310rpx;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            image{
                width: 96%;
                height: 100%;
            }
        }

        .shop-item-content {
            padding: 20rpx;
        }

        // 用户信息
        .shop-item-user {
            display: flex;
            align-items: center;
            padding: 16rpx 20rpx 12rpx 20rpx;

            .user-avatar {
                margin-right: 12rpx;

                ::v-deep .u-image {
                    border: 1rpx solid $border-color;
                }
            }

            .user-nickname {
                font-size: 28rpx;
                color: $text-gray;
                font-weight: 400;
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }

        .shop-item-name {
            font-size: 28rpx;
            color: $text-color;
            font-weight: 400;
            line-height: 1.4;
            height: 76rpx;
            margin-bottom: 16rpx;
        }

        .shop-item-detail {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .shop-item-price {
                display: flex;
                align-items: baseline;

                .price-symbol {
                    color: #ff4757;
                    font-size: 24rpx;
                    font-weight: 500;
                }

                .price-value {
                    color: #ff4757;
                    font-size: 32rpx;
                    font-weight: 600;
                    margin-left: 2rpx;
                }

                .price-up {
                    color: #ff4757;
                    font-size: 22rpx;
                    margin-left: 4rpx;
                }
            }

            .shop-item-views {
                display: flex;
                align-items: center;
                color: $text-light;
                font-size: 24rpx;

                text {
                    margin-left: 8rpx;
                }
            }
        }
    }
}

// 加载更多提示
.load-more {
    text-align: center;
    padding: 40rpx 0;
    color: $text-light;
    font-size: 26rpx;
}

// 空状态
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 40rpx;
    text-align: center;

    .empty-text {
        margin-top: 32rpx;
        font-size: 32rpx;
        color: $text-gray;
        font-weight: 500;
    }

    .empty-desc {
        margin-top: 16rpx;
        font-size: 26rpx;
        color: $text-light;
    }
}

// 底部操作栏
.bottom-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1rpx solid $border-color;
    display: flex;
    padding: 20rpx 0;
    box-shadow: 0 -2rpx 12rpx rgba(0, 0, 0, 0.04);
    z-index: 100;

    .bottom-btn {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 16rpx 0;
        transition: all 0.3s ease;

        &:active {
            transform: scale(0.95);
            background: rgba(137, 102, 239, 0.05);
        }

        text {
            margin-top: 8rpx;
            font-size: 24rpx;
            color: $theme-primary;
            font-weight: 500;
        }
    }
}
</style>
