/**
 * emoji表情工具类
 * 支持文本中[表情名]格式的解析和转换
 */

// 表情映射表：表情名 -> emoji unicode 或图片路径
const emojiMap = {
  // 基础表情
  '笑脸': '😊',
  '大笑': '😄',
  '哭泣': '😢',
  '惊讶': '😲',
  '疑问': '🤔',
  '点赞': '👍',
  '踩': '👎',
  '爱心': '❤️',
  '心碎': '💔',
  '生气': '😠',
  '开心': '😃',
  '哭笑': '😂',
  '得意': '😏',
  '悲伤': '😔',
  '害羞': '😳',
  '拥抱': '🤗',
  '思考': '🤔',
  '困惑': '😕',
  '无语': '😒',
  '调皮': '😜',
  '晕': '😵',
  '衰': '😫',
  '骷髅': '💀',
  '打哈欠': '😴',
  '闭嘴': '🤐',
  '睡': '😪',
  '斜眼': '😏',
  '流汗': '😅',
  '鬼脸': '👻',
  '鲜花': '🌹',
  '太阳': '☀️',
  '月亮': '🌙',
  '星星': '⭐',
  '音乐': '🎵',
  '蛋糕': '🎂',
  '礼物': '🎁',
  '祈祷': '🙏',
  '强': '💪',
  '弱': '👎',
  '握手': '🤝',
  '跑': '🏃',
};

/**
 * 解析文本中的表情符号
 * @param {String} text 包含表情标记的文本
 * @return {String} 解析后的文本，表情标记被替换为实际表情
 */
const parseEmoji = (text) => {
  if (!text) return '';
  
  // 替换[表情名]为对应的emoji
  return text.replace(/\[(.*?)\]/g, (match, emojiName) => {
    return emojiMap[emojiName] || match;
  });
};

/**
 * 获取所有可用的emoji表情
 * @return {Object} 表情映射表
 */
const getAllEmojis = () => {
  return { ...emojiMap };
};

/**
 * 检查文本中是否包含表情
 * @param {String} text 文本内容
 * @return {Boolean} 是否包含表情
 */
const hasEmoji = (text) => {
  if (!text) return false;
  
  // 检查是否包含[表情名]格式
  const hasNamedEmoji = /\[(.*?)\]/g.test(text);
  
  // 检查是否包含Unicode emoji
  // 注意：这是一个简化的检测方式，不是100%准确
  const unicodeEmojiRegex = /[\u{1F300}-\u{1F5FF}\u{1F900}-\u{1F9FF}\u{1F600}-\u{1F64F}\u{1F680}-\u{1F6FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}\u{1F1E6}-\u{1F1FF}\u{1F191}-\u{1F251}\u{1F004}\u{1F0CF}\u{1F170}-\u{1F171}\u{1F17E}-\u{1F17F}\u{1F18E}\u{3030}\u{2B50}\u{2B55}\u{2934}-\u{2935}\u{2B05}-\u{2B07}\u{2B1B}-\u{2B1C}\u{3297}\u{3299}\u{303D}\u{00A9}\u{00AE}\u{2122}\u{23F3}\u{24C2}\u{23E9}-\u{23EF}\u{25AA}-\u{25AB}\u{25FB}-\u{25FE}\u{25B6}\u{25C0}\u{2705}\u{270A}-\u{270B}\u{2728}\u{274C}\u{274E}\u{2753}-\u{2755}\u{2795}-\u{2797}\u{27B0}\u{27BF}\u{2753}\u{2757}]/u;
  const hasUnicodeEmoji = unicodeEmojiRegex.test(text);
  
  return hasNamedEmoji || hasUnicodeEmoji;
};

export default emojiMap;

export {
  parseEmoji,
  getAllEmojis,
  hasEmoji
}; 