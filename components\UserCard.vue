<template>
    <view class="user-card" @click="handleCardClick">
        <!-- 用户图片容器 -->
        <view class="image-container">
            <image class="user-image" :src="workData.img" mode="aspectFill"></image>

            <!-- 在线时间 -->
            <view class="online-time" v-if="shouldShowOnlineTime">{{ $util.formatTimeString(workData.addtime) }}</view>

            <!-- 右上角控制按钮 - 只有自己的作品且在showView模式下显示 -->
            <view class="top-right-controls" v-if="shouldShowControls">
                <button class="control-btn" @click.stop="toggleStatus" :class="{ hidden: Number(workData.status) === 2 }">
                    <u-icon :name="Number(workData.status) === 2 ? 'eye' : 'eye-off'" size="20" :color="Number(workData.status) === 2 ? '#fff' : '#666'"></u-icon>
                    <text class="btn-text">{{ Number(workData.status) === 2 ? '显示' : '隐藏' }}</text>
                </button>
            </view>

            <!-- 底部信息区域 -->
            <view class="bottom-info" v-if="shouldShowBottomInfo" :class="{ 'profile-mode-no-tags': componentMode === 'profile' && !shouldShowTags }">
                <!-- mine模式且showView的布局：浏览量 + 点赞按钮 -->
                <block v-if="showView && componentMode === 'mine'">
                    <!-- 浏览量显示 -->
                    <view class="view-count" v-if="isOwnWork">
                        <u-icon name="eye" size="24" color="#333"></u-icon>
                        <text>{{ workData.view_num || 0 }}</text>
                    </view>
                    <view class="like-btn" :class="{ disabled: shouldDisableLike }">
                        <u-icon :name="likeStatus ? 'heart-fill' : 'heart'" :color="likeStatus ? '#FF5722' : '#fff'" size="32"></u-icon>
                        <text>{{ workData.like_num || 0 }}</text>
                    </view>
                </block>
                <!-- profile模式的布局：标签 + 点赞按钮，或者只有点赞按钮（右对齐） -->
                <block v-else-if="componentMode === 'profile'">
                    <!-- 标签容器 -->
                    <view class="tags-container" v-if="shouldShowTags">
                        <block v-for="(tag, tagIndex) in workData.user.tag_data_arr" :key="tagIndex">
                            <view class="tag" v-if="tagIndex <= 1">
                                {{ tag.name }}
                            </view>
                        </block>
                    </view>
                    <!-- 点赞按钮 -->
                    <view class="like-btn" @click.stop="handleLike" :class="{ disabled: shouldDisableLike }">
                        <u-icon :name="likeStatus ? 'heart-fill' : 'heart'" :color="likeStatus ? '#FF5722' : '#fff'" size="32"></u-icon>
                        <text>{{ workData.like_num || 0 }}</text>
                    </view>
                </block>
                <!-- 其他模式的布局：标签 + 点赞按钮 -->
                <block v-else>
                    <!-- 标签容器 -->
                    <view class="tags-container" v-if="shouldShowTags">
                        <block v-for="(tag, tagIndex) in workData.user.tag_data_arr" :key="tagIndex">
                            <view class="tag" v-if="tagIndex <= 1">
                                {{ tag.name }}
                            </view>
                        </block>
                    </view>
                    <!-- 点赞按钮 -->
                    <view class="like-btn" @click.stop="handleLike" :class="{ disabled: shouldDisableLike }">
                        <u-icon :name="likeStatus ? 'heart-fill' : 'heart'" :color="likeStatus ? '#FF5722' : '#fff'" size="32"></u-icon>
                        <text>{{ workData.like_num || 0 }}</text>
                    </view>
                </block>
            </view>

            <!-- 已隐藏状态遮罩 -->
            <view v-if="shouldShowHiddenOverlay" class="hidden-overlay">
                <view class="hidden-text">已隐藏</view>
            </view>
        </view>

        <!-- 用户信息 -->
        <view class="user-info">
            <!-- 头像昵称和位置信息对齐 -->
            <view class="user-location-row" :class="{ 'has-user-info': shouldShowUserInfo }">
                <!-- 左侧头像昵称 -->
                <view class="user-info-left" v-if="shouldShowUserInfo">
                    <image class="user-avatar" :src="workData.user.headimgurl" mode="aspectFill"></image>
                    <view class="user-nickname">{{ displayNickname }}</view>
                </view>
                <!-- 右侧位置信息 -->
                <view class="location-info" :class="{ 'full-width': shouldFullWidthLocation }">
                    <u-icon name="map" color="#333" size="24"></u-icon>
                    <text class="location-text">{{ formattedLocation }}</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex';

export default {
    name: 'UserCard',
    props: {
        // 兼容原有的info属性
        info: {
            type: Object,
            default: null,
        },
        // 兼容UserCardMine和UserCardProfile的workInfo属性
        workInfo: {
            type: Object,
            default: null,
        },
        // 兼容原有的index属性
        index: {
            type: Number,
            default: 0,
        },
        // 显示模式控制
        showView: {
            type: Boolean,
            default: true,
        },
        // 隐藏用户信息
        hideUserInfo: {
            type: Boolean,
            default: false,
        },
        // 组件模式：'basic'(基础模式), 'mine'(我的作品), 'profile'(用户资料)
        mode: {
            type: String,
            default: 'auto', // 自动检测模式
        },
    },
    computed: {
        ...mapState(['userInfo']),

        // 统一的作品数据，兼容info和workInfo两种属性名
        workData() {
            return this.workInfo || this.info || {};
        },

        // 自动检测组件模式
        componentMode() {
            if (this.mode !== 'auto') return this.mode;

            // 如果有workInfo属性，说明是mine或profile模式
            if (this.workInfo) {
                // 如果有showView属性且为false，通常是profile模式
                return this.showView === false ? 'profile' : 'mine';
            }
            // 否则是基础模式
            return 'basic';
        },

        // 判断是否为自己的作品
        isOwnWork() {
            return this.userInfo && this.workData.user && this.userInfo.uid === this.workData.user.uid;
        },

        // 判断是否有标签
        hasTags() {
            return this.workData.user && this.workData.user.tag_data_arr && this.workData.user.tag_data_arr.length > 0;
        },

        // 获取用户城市信息
        userCity() {
            if (!this.workData.user) return '';
            const user = this.workData.user;
            return user.city_code_txt?.name || '';
        },

        // 格式化位置信息
        formattedLocation() {
            if (this.workData.address && this.workData.address.trim()) {
                return this.formatAddressString(this.workData.address);
            }
            return this.formatUserLocation();
        },

        // 显示昵称，最多2个字符
        displayNickname() {
            if (!this.workData.user || !this.workData.user.nickname) return '';
            const nickname = this.workData.user.nickname;
            return nickname.length > 2 ? nickname.substring(0, 2) : nickname;
        },

        // 控制显示逻辑
        shouldShowOnlineTime() {
            return this.componentMode === 'basic' || (this.componentMode !== 'basic' && !this.showView);
        },

        shouldShowControls() {
            return (this.componentMode === 'mine' || this.componentMode === 'profile') && this.showView && this.isOwnWork;
        },

        shouldShowBottomInfo() {
            if (this.componentMode === 'basic') {
                return this.hasTags;
            }
            return true;
        },

        shouldShowTags() {
            if (this.componentMode === 'basic') {
                return this.hasTags;
            }
            if (this.componentMode === 'mine') {
                return !this.showView && this.hasTags;
            }
            if (this.componentMode === 'profile') {
                return this.hasTags;
            }
            return false;
        },

        shouldDisableLike() {
            return (this.componentMode === 'profile' && this.isOwnWork) ||
                   (this.componentMode === 'mine' && this.showView);
        },

        shouldShowHiddenOverlay() {
            return (this.componentMode === 'mine' || this.componentMode === 'profile') &&
                   !this.showView && this.workData.status != 1;
        },

        shouldShowUserInfo() {
            if (this.componentMode === 'basic') return true;
            if (this.componentMode === 'mine') return !this.showView;
            if (this.componentMode === 'profile') return !this.isOwnWork && !this.hideUserInfo;
            return false;
        },

        shouldFullWidthLocation() {
            return this.isOwnWork || this.hideUserInfo ||
                   (this.componentMode !== 'basic' && this.showView);
        },

        // 判断是否在点赞列表模式
        isInLikeList() {
            return this.componentMode === 'mine' && !this.showView;
        },

        // 获取点赞状态，点赞列表中强制为已点赞
        likeStatus() {
            return this.isInLikeList ? true : this.workData.is_like;
        },
    },
    methods: {
        // 统一的卡片点击处理
        handleCardClick() {
            // 如果是点赞收藏列表中的非上架作品，不允许进入详情
            if (!this.showView && this.workData.status != 1) {
                uni.showToast({
                    title: '该作品已隐藏，无法查看',
                    icon: 'none',
                });
                return;
            }
            this.goDetail();
        },

        goDetail() {
            uni.navigateTo({
                url: '/views/moments/detail?id=' + this.workData.id,
            });
        },

        // 点赞处理
        async handleLike() {
            if (this.shouldDisableLike) return;

            try {
                const res = await this.$api.likeWork({
                    work_id: this.workData.id,
                    access_token: uni.getStorageSync('token'),
                });

                // 更新点赞状态
                this.workData.is_like = res.data.is_like;
                this.workData.like_num = res.data.is_like ?
                    Number(this.workData.like_num || 0) + 1 :
                    Number(this.workData.like_num || 0) - 1;

                // 通知父组件
                if (this.componentMode === 'basic') {
                    this.$emit('like', res.data.is_like, this.index);
                } else {
                    this.$emit('like', res.data.is_like, this.workData.id);
                }
                uni.$emit('refreshLike');
            } catch (error) {
                console.error('点赞失败:', error);
                uni.showToast({
                    title: '网络异常，请稍后再试',
                    icon: 'none',
                });
            }
        },

        // 切换展示/隐藏状态
        async toggleStatus() {
            const currentStatus = this.workData.status;
            const newStatus = Number(currentStatus) === 1 ? 2 : 1;
            const statusText = this.getStatusTextByValue(newStatus);

            uni.showModal({
                title: '确认操作',
                content: `确定要${statusText}这个作品吗？`,
                success: async res => {
                    if (res.confirm) {
                        try {
                            await this.$api.hideWork({
                                work_id: this.workData.id,
                                access_token: uni.getStorageSync('token'),
                            });

                            this.workData.status = newStatus;

                            uni.showToast({
                                title: `${statusText}成功`,
                                icon: 'success',
                            });

                            this.$emit('statusChanged', {
                                workId: this.workData.id,
                                newStatus: newStatus,
                            });
                        } catch (error) {
                            console.error('状态变更失败:', error);
                            uni.showToast({
                                title: '操作失败，请重试',
                                icon: 'none',
                            });
                        }
                    }
                },
            });
        },

        // 根据状态值获取文本
        getStatusTextByValue(status) {
            switch (Number(status)) {
                case 1: return '展示';
                case 2: return '隐藏';
                case 3: return '下架';
                default: return '展示';
            }
        },

        // 格式化地址字符串
        formatAddressString(address) {
            if (!address || !address.trim()) return '';

            if (address.includes('-') && address.split('-').length === 2) {
                const parts = address.split('-');
                let cityPart = parts[0].trim();
                let areaPart = parts[1].trim();

                if (cityPart.includes('市')) {
                    cityPart = cityPart.replace('市', '');
                }
                cityPart = cityPart.substring(0, 4);
                areaPart = areaPart.substring(0, 2);

                return cityPart + '-' + areaPart;
            }

            return this.parseFullAddress(address);
        },

        // 格式化用户信息中的地址
        formatUserLocation() {
            if (!this.workData.user) return '';

            const user = this.workData.user;
            let result = '';

            // 处理省/自治区信息
            let provinceName = '';
            if (user.province_code_txt && user.province_code_txt.name) {
                provinceName = user.province_code_txt.name;
            }

            // 处理市信息
            if (user.city_code_txt && user.city_code_txt.name) {
                let cityName = user.city_code_txt.name;
                let areaName = user.area_code_txt?.name || '';

                // 特殊处理自治区
                if (provinceName.includes('新疆') || provinceName.includes('内蒙古') || provinceName.includes('西藏') || provinceName.includes('宁夏') || provinceName.includes('广西')) {
                    // 自治区名称处理
                    let regionName = provinceName;
                    if (regionName.includes('维吾尔自治区')) {
                        regionName = '新疆';
                    } else if (regionName.includes('内蒙古自治区')) {
                        regionName = '内蒙古';
                    } else if (regionName.includes('西藏自治区')) {
                        regionName = '西藏';
                    } else if (regionName.includes('宁夏回族自治区')) {
                        regionName = '宁夏';
                    } else if (regionName.includes('广西壮族自治区')) {
                        regionName = '广西';
                    }

                    // 城市名称处理
                    if (cityName.includes('市')) {
                        cityName = cityName.replace('市', '');
                    }
                    if (cityName.includes('地区')) {
                        cityName = cityName.replace('地区', '');
                    }
                    if (cityName.includes('州')) {
                        cityName = cityName.replace('州', '');
                    }
                    if (areaName.includes('市')) {
                        areaName = areaName.replace('市', '');
                    }
                    if (areaName.includes('地区')) {
                        areaName = areaName.replace('地区', '');
                    }
                    if (areaName.includes('州')) {
                        areaName = areaName.replace('州', '');
                    }

                    result = regionName + '-' + areaName;
                } else {
                    // 普通省市处理
                    if (cityName.includes('市')) {
                        cityName = cityName.replace('市', '');
                    }
                    result = cityName.substring(0, 4);

                    // 处理区信息
                    if (user.area_code_txt && user.area_code_txt.name) {
                        let areaName = user.area_code_txt.name;
                        areaName = areaName.substring(0, 2);
                        result += '-' + areaName;
                    }
                }
            }

            return result;
        },

        // 解析完整地址，提取市区信息
        parseFullAddress(address) {
            if (!address || !address.trim()) return '';

            // 正则表达式匹配省市区结构
            const patterns = [
                // 自治区格式：自治区+市+区（优先匹配，避免被省格式误匹配）
                /(.+?自治区)(.+?市)(.+?[区县])/,
                // 完整格式：省+市+区
                /(.+?省)(.+?市)(.+?[区县])/,
                // 直辖市格式：市+区
                /^(北京市|上海市|天津市|重庆市)(.+?[区县])/,
                // 简化格式：市+区（没有省）
                /^(.+?市)(.+?[区县])/,
            ];

            let cityName = '';
            let areaName = '';
            let provinceName = '';

            for (let pattern of patterns) {
                const match = address.match(pattern);
                if (match) {
                    if (pattern.source.includes('省')) {
                        provinceName = match[1] || '';
                        cityName = match[2] || '';
                        areaName = match[3] || '';
                    } else if (pattern.source.includes('自治区')) {
                        provinceName = match[1] || '';
                        cityName = match[2] || '';
                        areaName = match[3] || '';
                    } else if (pattern.source.includes('北京市|上海市|天津市|重庆市')) {
                        cityName = match[1] || '';
                        areaName = match[2] || '';
                    } else {
                        cityName = match[1] || '';
                        areaName = match[2] || '';
                    }
                    break;
                }
            }

            // 如果没有匹配到，尝试简单的文本分析
            if (!cityName || !areaName) {
                const cityIndex = address.indexOf('市');
                const areaIndex = Math.max(address.indexOf('区'), address.indexOf('县'));

                if (cityIndex > 0 && areaIndex > cityIndex) {
                    let startIndex = 0;
                    const provinceIndex = Math.max(address.indexOf('省'), address.indexOf('自治区'));
                    if (provinceIndex >= 0) {
                        startIndex = provinceIndex + (address.indexOf('省') >= 0 ? 1 : 3);
                    }

                    cityName = address.substring(startIndex, cityIndex + 1);
                    areaName = address.substring(cityIndex + 1, areaIndex + 1);
                }
            }

            // 格式化处理
            if (cityName && areaName) {
                // 处理市名称
                if (cityName.includes('市')) {
                    cityName = cityName.replace('市', '');
                }
                cityName = cityName.substring(0, 4);

                // 处理区名称
                if (areaName.includes('区')) {
                    areaName = areaName.replace('区', '');
                } else if (areaName.includes('县')) {
                    areaName = areaName.replace('县', '');
                }
                areaName = areaName.substring(0, 2);

                // 特殊处理自治区
                if (provinceName && (provinceName.includes('新疆') || provinceName.includes('内蒙古') ||
                    provinceName.includes('西藏') || provinceName.includes('宁夏') || provinceName.includes('广西'))) {
                    let regionName = provinceName;
                    if (regionName.includes('维吾尔自治区')) {
                        regionName = '新疆';
                    } else if (regionName.includes('内蒙古自治区')) {
                        regionName = '内蒙古';
                    } else if (regionName.includes('西藏自治区')) {
                        regionName = '西藏';
                    } else if (regionName.includes('宁夏回族自治区')) {
                        regionName = '宁夏';
                    } else if (regionName.includes('广西壮族自治区')) {
                        regionName = '广西';
                    }
                    return regionName + '-' + areaName;
                }

                return cityName + '-' + areaName;
            }

            // 如果解析失败，返回前8个字符作为备用
            return address.substring(0, 8);
        },
    },
};
</script>

<style lang="scss" scoped>
.user-card {
    width: 100%;
    border-radius: 12rpx;
    background-color: #ffffff;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-bottom: 20rpx;

    .image-container {
        position: relative;
        width: 100%;
        height: 360rpx;

        .user-image {
            width: 100%;
            height: 100%;
        }

        .online-time {
            position: absolute;
            top: 16rpx;
            right: 16rpx;
            color: #ffffff;
            font-size: 22rpx;
            padding: 4rpx 12rpx;
            border-radius: 20rpx;
        }

        .view-count {
            background-color: rgba(255, 255, 255, 0.7);
            border-radius: 30rpx;
            padding: 4rpx 10rpx;
            display: flex;
            align-items: center;

            text {
                margin-left: 4rpx;
                font-size: 24rpx;
                color: #333;
            }
        }

        // 右上角控制按钮
        .top-right-controls {
            position: absolute;
            top: 16rpx;
            right: 16rpx;
            z-index: 10;

            .control-btn {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 110rpx;
                height: 50rpx;
                border-radius: 24rpx;
                background: rgba(255, 255, 255, 0.95);
                border: none;
                font-size: 22rpx;
                padding:0 0rpx;
                box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
                backdrop-filter: blur(10rpx);
                transition: all 0.3s ease;

                .btn-text {
                    margin-left: 6rpx;
                    color: #666;
                    font-size: 22rpx;
                    font-weight: 500;
                }

                &.hidden {
                    background: linear-gradient(135deg, #8966ef, #a584f2);
                    box-shadow: 0 4rpx 16rpx rgba(137, 102, 239, 0.4);

                    .btn-text {
                        color: #fff;
                    }
                }

                &:active {
                    transform: scale(0.95);
                }
                
            }
        }

        .bottom-info {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            padding: 16rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            // background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);

            &.profile-mode-no-tags {
                justify-content: flex-end;
            }

            .tags-container {
                display: flex;
                flex-wrap: wrap;
                gap: 8rpx;

                .tag {
                    display: flex;
                    align-items: center;
                    font-size: 22rpx;
                    box-shadow: 0 4rpx 10rpx rgba(12, 57, 73, 0.2);
                    color: #ffffff;
                    background: linear-gradient(135deg, #8966ef, #a584f2);
                    padding: 4rpx 12rpx;
                    border-radius: 8rpx;
                    margin-right: 8rpx;
                }
            }

            .like-btn {
                display: flex;
                align-items: center;
                z-index: 10;

                text {
                    font-size: 22rpx;
                    color: #ffffff;
                    margin-left: 6rpx;
                }

                &.disabled {
                    opacity: 0.7;
                    cursor: not-allowed;
                }
            }
        }

        .hidden-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12rpx 12rpx 0 0;

            .hidden-text {
                background-color: rgba(255, 255, 255, 0.9);
                color: #666;
                font-size: 28rpx;
                font-weight: 500;
                padding: 12rpx 24rpx;
                border-radius: 20rpx;
                border: 2rpx solid #ddd;
            }
        }
    }

    .user-info {
        padding: 16rpx;

        .user-location-row {
            display: flex;
            justify-content: flex-end;
            align-items: center;

            &.has-user-info {
                justify-content: space-between;
            }

            .user-info-left {
                display: flex;
                align-items: center;

                .user-avatar {
                    width: 40rpx;
                    height: 40rpx;
                    border-radius: 50%;
                    margin-right: 12rpx;
                }

                .user-nickname {
                    font-size: 26rpx;
                    color: #333;
                    font-weight: 500;
                }
            }

            .location-info {
                display: flex;
                align-items: center;
                font-size: 24rpx;
                color: #666;

                .location-text {
                    margin-left: 4rpx;
                }

                &.full-width {
                    justify-content: flex-start;
                }
            }
        }
    }
}
</style>
