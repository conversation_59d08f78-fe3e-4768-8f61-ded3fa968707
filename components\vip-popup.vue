<template>
  <u-popup v-model="show" mode="center" border-radius="14" width="650rpx" height="auto" :mask-close-able="true" :safe-area-inset-bottom="true">
    <view class="vip-popup">
      <!-- 顶部区域 -->
      <view class="vip-header">
        <view class="close-btn" @click="closePopup">
          <u-icon name="close" color="#ffffff" size="30"></u-icon>
        </view>
        <view class="vip-crown">
          <u-icon name="level" color="#FFD700" size="60"></u-icon>
          <view class="crown-glow"></view>
        </view>
        <view class="vip-title">
          <text class="main-title">会员特权</text>
          <text class="sub-title">加微信是会员的专属权益</text>
        </view>
      </view>
      
      <!-- 特权内容区域 -->
      <view class="vip-content">
        <!-- 特权列表 -->
        <!-- <view class="privilege-list">
          <view class="privilege-item highlight">
            <view class="privilege-icon">
              <u-icon name="weixin-circle-fill" color="#4a90e2" size="50"></u-icon>
              <view class="highlight-badge">
                <text>VIP</text>
              </view>
            </view>
            <view class="privilege-info">
              <text class="privilege-title">无限制加微信</text>
              <text class="privilege-desc">解锁添加微信限制，随时畅聊</text>
            </view>
          </view>
          
          <view class="privilege-divider"></view>
          
          <view class="privilege-item">
            <view class="privilege-icon">
              <u-icon name="calendar" color="#5e62b0" size="40"></u-icon>
            </view>
            <view class="privilege-info">
              <text class="privilege-title">定制活动</text>
              <text class="privilege-desc">每月专属线下聚会活动邀请</text>
            </view>
          </view>
          
          <view class="privilege-divider"></view>
          
          <view class="privilege-item">
            <view class="privilege-icon">
              <u-icon name="heart" color="#dc2e9d" size="40"></u-icon>
            </view>
            <view class="privilege-info">
              <text class="privilege-title">退款保障</text>
              <text class="privilege-desc">5天内对方未回复，费用退回平台</text>
            </view>
          </view>
        </view> -->
        
        <!-- 会员状态显示 -->
        <view class="member-status" v-if="hasLogin && memberStatus.isValid">
          <view class="status-info">
            <u-icon name="checkmark-circle-fill" :color="memberStatus.statusColor" size="24"></u-icon>
            <text class="status-text" :style="{ color: memberStatus.statusColor }">{{ memberStatus.statusText }}</text>
            <text class="expire-date" v-if="memberStatus.expireDate">至{{ memberStatus.expireDate }}</text>
          </view>
        </view>

        <!-- 价格信息区域 -->
        <view class="price-section">
          <view class="price-header">
            <view class="price-title">会员价格</view>
            <view class="best-value-tag">超值优惠</view>
          </view>

          <!-- 加载状态 -->
          <view class="loading-container" v-if="loading">
            <u-loading mode="circle" size="28" color="#8966ef"></u-loading>
            <text class="loading-text">加载套餐中...</text>
          </view>

          <!-- 产品选项 -->
          <view class="price-options" v-else-if="memberProducts.length > 0">
            <view
              v-for="product in memberProducts"
              :key="product.id"
              class="price-option"
              :class="{ 'selected': selectedPlan === product.id }"
              @click="selectPlan(product.id)"
            >
              <view class="price-option-header">
                <text class="option-name">{{ product.title }}</text>
                <view class="popular-tag" v-if="product.isPopular">热门</view>
              </view>

              <view class="price-amount">
                <text class="price-currency">¥</text>
                <text class="price-number">{{ product.amount }}</text>
                <text class="price-period" v-if="product.type_value">/{{ product.type_value === 'month' ? '月' : '年' }}</text>
              </view>

              <!-- 优惠信息 -->
              <view class="discount-info" v-if="product.discount.hasDiscount">
                <text class="original-price">原价¥{{ product.originalPrice.toFixed(2) }}</text>
                <!-- <text class="discount-text">{{ product.discount.discountText }}</text> -->
              </view>

              <text class="price-includes">{{ product.content || '无限制加微信权限' }}</text>

              <view class="selected-mark" v-if="selectedPlan === product.id">
                <u-icon name="checkmark" color="#ffffff" size="24"></u-icon>
              </view>
            </view>
          </view>

          <!-- 无产品提示 -->
          <view class="no-products" v-else-if="!loading">
            <text>暂无可用套餐</text>
          </view>
        </view>
      </view>
      
      <!-- 底部按钮区域 -->
      <view class="button-section">
        <view class="refund-notice">
          <u-icon name="info-circle" color="#999" size="24"></u-icon>
          <text>对方5天未回复，费用将退回平台余额</text>
        </view>

        <view class="action-buttons">
          <button
            class="action-button member-btn"
            @click="onClickMemberPay"
            :disabled="!selectedProduct || paymentLoading"
            :class="{ 'disabled': !selectedProduct || paymentLoading }"
          >
            <u-loading v-if="paymentLoading" mode="circle" size="18" color="#ffffff"></u-loading>
            <text class="button-text" v-if="!paymentLoading">
              {{ selectedProduct ? `开通${selectedProduct.title}(¥${selectedProduct.amount})` : '选择套餐' }}
            </text>
            <text class="button-text" v-else>处理中...</text>
          </button>

          <!-- <button
            class="action-button single-btn"
            @click="onClickSinglePay"
            :disabled="paymentLoading"
            :class="{ 'disabled': paymentLoading }"
          >
            <u-loading v-if="paymentLoading" mode="circle" size="18" color="#ffffff"></u-loading>
            <text class="button-text" v-if="!paymentLoading">单次付费(¥{{ singlePayPrice }})</text>
            <text class="button-text" v-else>处理中...</text>
          </button> -->
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
import { mapState } from 'vuex';
import { getOrderGoodList, createMemberOrder, createMiniPay } from '@/utils/vmeitime-http/order.js';
import { getMemberStatus, calculateDiscount } from '@/utils/memberUtils.js';

export default {
  name: 'vip-popup',
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      show: false,
      selectedPlan: null, // 选中的会员套餐ID
      memberProducts: [], // 会员产品列表
      singlePayPrice: 200, // 单次付费价格
      loading: false,
      paymentLoading: false
    };
  },
  computed: {
    ...mapState(['userInfo', 'hasLogin']),

    // 获取当前选中的产品
    selectedProduct() {
      if (!this.selectedPlan || !this.memberProducts.length) {
        return null;
      }
      return this.memberProducts.find(item => item.id === this.selectedPlan);
    },

    // 获取用户会员状态
    memberStatus() {
      return getMemberStatus(this.userInfo);
    }
  },
  watch: {
    value: {
      handler(val) {
        this.show = val;
        if (val) {
          this.loadMemberProducts();
        }
      },
      immediate: true
    },
    show(val) {
      this.$emit('input', val);
    }
  },
  methods: {
    // 加载会员产品列表
    async loadMemberProducts() {
      if (!this.hasLogin) {
        return;
      }

      this.loading = true;

      try {
        const res = await getOrderGoodList({
          type: 2, // 会员费类型
          access_token: uni.getStorageSync('token')
        });

        if (res.status === 0 && res.data && res.data.list) {
          this.memberProducts = res.data.list.map(item => {
            // 计算优惠信息
            const originalPrice = parseFloat(item.amount) * 1.3; // 假设原价为现价的1.3倍
            const discount = calculateDiscount(originalPrice, parseFloat(item.amount));

            return {
              id: item.id,
              title: item.title,
              amount: parseFloat(item.amount),
              originalPrice: originalPrice,
              content: item.content || '',
              type_value: item.type_value || '',
              discount: discount,
              isPopular: item.type_value === 'month' // 月卡设为热门
            };
          });

          // 默认选中第一个产品
          if (this.memberProducts.length > 0) {
            this.selectedPlan = this.memberProducts[0].id;
          }
        } else {
          console.error('获取会员产品失败:', res.msg);
          uni.showToast({
            title: res.msg || '获取会员套餐失败',
            icon: 'none'
          });
        }
      } catch (error) {
        console.error('加载会员产品失败:', error);
        uni.showToast({
          title: '网络异常，请稍后重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },

    closePopup() {
      this.show = false;
      this.$emit('input', false);
      this.$emit('close');
    },

    // 单次付费
    async onClickSinglePay() {
      if (this.paymentLoading) return;

      this.paymentLoading = true;

      try {
        // 发送单次付费事件给父组件
        this.$emit('single-pay', {
          amount: this.singlePayPrice,
          type: 'single'
        });

        uni.showToast({
          title: '正在处理单次付款...',
          icon: 'loading'
        });

        // 关闭弹窗
        setTimeout(() => {
          this.closePopup();
        }, 1000);

      } catch (error) {
        console.error('单次付费失败:', error);
        uni.showToast({
          title: '支付失败，请重试',
          icon: 'none'
        });
      } finally {
        this.paymentLoading = false;
      }
    },

    // 选择套餐
    selectPlan(productId) {
      this.selectedPlan = productId;
    },

    // 会员付费
    async onClickMemberPay() {
      if (this.paymentLoading || !this.selectedProduct) return;

      this.paymentLoading = true;

      try {
        // 发送会员付费事件给父组件
        this.$emit('member-pay', {
          product: this.selectedProduct,
          amount: this.selectedProduct.amount,
          type: 'member'
        });

        uni.showToast({
          title: '正在处理会员付款...',
          icon: 'loading'
        });

        // 关闭弹窗
        setTimeout(() => {
          this.closePopup();
        }, 1000);

      } catch (error) {
        console.error('会员付费失败:', error);
        uni.showToast({
          title: '支付失败，请重试',
          icon: 'none'
        });
      } finally {
        this.paymentLoading = false;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
/* 定义变量 */
$primary-gradient: linear-gradient(135deg, #4a90e2, #5e62b0, #dc2e9d);
$gold-color: #FFD700;
$text-light: #ffffff;
$text-dark: #333333;
$text-gray: #888888;

.vip-popup {
  position: relative;
  background-color: #fff;
  width: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border-radius: 14rpx;
  animation: popup-show 0.4s ease-out;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}

@keyframes popup-show {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.vip-header {
  position: relative;
  background: $primary-gradient;
  padding: 40rpx 30rpx 50rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: -100rpx;
    left: -100rpx;
    width: 200rpx;
    height: 200rpx;
    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%);
    border-radius: 50%;
    z-index: 0;
    animation: float-bubble 15s infinite linear;
  }
  
  &::after {
    content: '';
    position: absolute;
    bottom: -30rpx;
    left: 0;
    right: 0;
    height: 40rpx;
    background-color: #fff;
    border-radius: 50% 50% 0 0;
    z-index: 1;
  }
}

@keyframes float-bubble {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(80rpx, 50rpx);
  }
  50% {
    transform: translate(120rpx, -20rpx);
  }
  75% {
    transform: translate(20rpx, -80rpx);
  }
  100% {
    transform: translate(0, 0);
  }
}

.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  z-index: 10;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  transition: all 0.2s ease;
  
  &:active {
    transform: scale(0.9);
    background-color: rgba(0, 0, 0, 0.2);
  }
}

.vip-crown {
  position: relative;
  margin-bottom: 20rpx;
  animation: float 3s ease-in-out infinite;
  z-index: 2;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30rpx;
    height: 30rpx;
    background-color: rgba(255, 215, 0, 0.4);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    filter: blur(10rpx);
    z-index: -1;
    animation: crown-shadow 3s infinite ease-in-out alternate;
  }
}

@keyframes crown-shadow {
  0% {
    width: 30rpx;
    height: 30rpx;
    opacity: 0.4;
  }
  100% {
    width: 60rpx;
    height: 40rpx;
    opacity: 0.6;
  }
}

@keyframes float {
  0% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-10rpx) rotate(2deg);
  }
  100% {
    transform: translateY(0) rotate(0deg);
  }
}

.crown-glow {
  position: absolute;
  width: 80rpx;
  height: 80rpx;
  background: radial-gradient(circle, rgba(255, 215, 0, 0.8) 0%, rgba(255, 215, 0, 0) 70%);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: -1;
  animation: pulse 2s infinite ease-in-out;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.5;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.5;
  }
}

.vip-title {
  text-align: center;
  z-index: 5;
  
  .main-title {
    font-size: 42rpx;
    font-weight: bold;
    color: $text-light;
    display: block;
    text-shadow: 0 2rpx 6rpx rgba(0,0,0,0.2);
    margin-bottom: 10rpx;
    letter-spacing: 2rpx;
    animation: text-glow 2s infinite alternate;
  }
  
  .sub-title {
    font-size: 28rpx;
    color: rgba(255,255,255,0.9);
    display: block;
    text-shadow: 0 1rpx 3rpx rgba(0,0,0,0.1);
  }
}

@keyframes text-glow {
  from {
    text-shadow: 0 2rpx 6rpx rgba(0,0,0,0.2);
  }
  to {
    text-shadow: 0 2rpx 10rpx rgba(255,255,255,0.4);
  }
}

.vip-content {
  padding: 40rpx 30rpx 20rpx;
  background-color: #fff;
}

/* 会员状态样式 */
.member-status {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: linear-gradient(to right, rgba(76, 175, 80, 0.1), rgba(76, 175, 80, 0.05));
  border-radius: 12rpx;
  border: 1rpx solid rgba(76, 175, 80, 0.2);
}

.status-info {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.status-text {
  font-size: 28rpx;
  font-weight: bold;
}

.expire-date {
  font-size: 24rpx;
  color: #666;
  margin-left: auto;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
  gap: 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #8966ef;
}

/* 无产品提示样式 */
.no-products {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 优惠信息样式 */
.discount-info {
  display: flex;
  align-items: center;
  gap: 10rpx;
  margin: 8rpx 0;
}

.original-price {
  font-size: 22rpx;
  color: #999;
  text-decoration: line-through;
}

.discount-text {
  font-size: 22rpx;
  color: #ff4757;
  background: rgba(255, 71, 87, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
}

.privilege-list {
  margin-top: 20rpx;
}

.privilege-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  position: relative;
  transition: all 0.3s ease;
  
  &:active {
    transform: translateX(5rpx);
  }
  
  &.highlight {
    background: linear-gradient(to right, rgba(74, 144, 226, 0.05), rgba(74, 144, 226, 0.02));
    padding: 30rpx 20rpx;
    border-radius: 16rpx;
    margin: 0 -10rpx 20rpx;
    border: 1rpx dashed rgba(74, 144, 226, 0.3);
    animation: highlight-glow 3s infinite alternate;
  }
}

@keyframes highlight-glow {
  from {
    box-shadow: 0 0 10rpx rgba(74, 144, 226, 0.1);
  }
  to {
    box-shadow: 0 0 20rpx rgba(74, 144, 226, 0.2);
  }
}

.privilege-icon {
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  margin-right: 20rpx;
  flex-shrink: 0;
  transition: transform 0.3s ease;
  
  &:active {
    transform: scale(0.95);
  }
}

.highlight-badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  background: linear-gradient(135deg, #f6d365, #fda085);
  padding: 4rpx 12rpx;
  border-radius: 30rpx;
  transform: scale(0.8);
  animation: badge-pulse 1.5s infinite alternate;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.15);
  
  text {
    color: #fff;
    font-size: 20rpx;
    font-weight: bold;
  }
}

@keyframes badge-pulse {
  from {
    transform: scale(0.8);
  }
  to {
    transform: scale(0.9);
  }
}

.privilege-info {
  flex: 1;
}

.privilege-title {
  font-size: 30rpx;
  font-weight: bold;
  color: $text-dark;
  margin-bottom: 6rpx;
  display: block;
}

.privilege-desc {
  font-size: 24rpx;
  color: $text-gray;
  display: block;
}

.privilege-divider {
  height: 1rpx;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.05));
  margin: 10rpx 0;
}

/* 价格区域样式 */
.price-section {
  margin-top: 40rpx;
  padding-top: 30rpx;
  border-top: 1rpx dashed rgba(0, 0, 0, 0.1);
  animation: fade-in 0.8s ease-out;
  animation-fill-mode: both;
  animation-delay: 0.3s;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.price-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.price-title {
  font-size: 32rpx;
  font-weight: bold;
  color: $text-dark;
}

.best-value-tag {
  background: linear-gradient(135deg, #ff9800, #ff5722);
  color: $text-light;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  margin-left: 16rpx;
  font-weight: 500;
  box-shadow: 0 2rpx 6rpx rgba(255, 87, 34, 0.3);
  animation: tag-flash 2s infinite alternate;
}

@keyframes tag-flash {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  90% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.price-options {
  display: flex;
  justify-content: space-between;
  margin: 0 -10rpx 30rpx;
}

.price-option {
  flex: 1;
  background: linear-gradient(to bottom, rgba(74, 144, 226, 0.05), rgba(94, 98, 176, 0.02));
  border-radius: 16rpx;
  padding: 20rpx;
  margin: 0 10rpx;
  position: relative;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(74, 144, 226, 0.1);
  cursor: pointer;
  overflow: hidden;
  
  &:active {
    transform: translateY(3rpx);
    box-shadow: 0 2rpx 10rpx rgba(74, 144, 226, 0.1);
  }
  
  &.selected {
    background: linear-gradient(to bottom, rgba(74, 144, 226, 0.15), rgba(94, 98, 176, 0.1));
    border: 2rpx solid #dc2e9d;
    box-shadow: 0 6rpx 20rpx rgba(74, 144, 226, 0.2);
    transform: scale(1.02);
    
    .price-number {
      color: #dc2e9d;
    }
  }
  
  &:first-child {
    animation: option-bounce 0.5s ease-out;
    animation-delay: 0.4s;
    animation-fill-mode: both;
  }
  
  &:last-child {
    animation: option-bounce 0.5s ease-out;
    animation-delay: 0.6s;
    animation-fill-mode: both;
  }
}

@keyframes option-bounce {
  0% {
    opacity: 0;
    transform: translateY(20rpx);
  }
  50% {
    opacity: 1;
    transform: translateY(-5rpx);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.price-option-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.option-name {
  font-size: 28rpx;
  font-weight: bold;
  color: $text-dark;
}

.popular-tag {
  background-color: #dc2e9d;
  color: $text-light;
  font-size: 20rpx;
  padding: 2rpx 10rpx;
  border-radius: 20rpx;
  margin-left: 10rpx;
  transform: scale(0.8);
  transform-origin: left;
  box-shadow: 0 2rpx 6rpx rgba(220, 46, 157, 0.3);
  animation: popular-pulse 2s infinite alternate;
}

@keyframes popular-pulse {
  from {
    transform: scale(0.8);
  }
  to {
    transform: scale(0.85);
  }
}

.price-amount {
  display: flex;
  align-items: baseline;
  margin: 10rpx 0;
}

.price-currency {
  font-size: 26rpx;
  color: $text-dark;
  font-weight: bold;
}

.price-number {
  font-size: 42rpx;
  font-weight: bold;
  color: #4a90e2;
  line-height: 1;
  margin: 0 4rpx;
  text-shadow: 0 2rpx 4rpx rgba(74, 144, 226, 0.2);
}

.price-period {
  font-size: 24rpx;
  color: $text-gray;
}

.price-includes {
  font-size: 22rpx;
  color: $text-gray;
  display: block;
  margin-top: 10rpx;
}

.special-offer {
  background: linear-gradient(to right, rgba(255, 215, 0, 0.1), rgba(255, 152, 0, 0.05));
  border: 1rpx dashed rgba(255, 215, 0, 0.6);
  border-radius: 16rpx;
  padding: 20rpx;
  display: flex;
  align-items: center;
  position: relative;
  margin-top: 20rpx;
  animation: special-glow 3s infinite alternate;
  animation-delay: 0.8s;
  animation-fill-mode: both;
  box-shadow: 0 4rpx 15rpx rgba(255, 215, 0, 0.1);
}

@keyframes special-glow {
  from {
    box-shadow: 0 4rpx 15rpx rgba(255, 215, 0, 0.1);
  }
  to {
    box-shadow: 0 4rpx 20rpx rgba(255, 215, 0, 0.2);
  }
}

.special-offer-badge {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
  animation: rotate 10s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.special-offer-info {
  flex: 1;
  display: flex;
  align-items: baseline;
}

.special-offer-title {
  font-size: 28rpx;
  font-weight: bold;
  color: $text-dark;
  margin-right: 10rpx;
}

.special-offer-price {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff9800;
  text-shadow: 0 2rpx 4rpx rgba(255, 152, 0, 0.2);
}

.special-offer-desc {
  font-size: 22rpx;
  color: $text-gray;
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
}

/* 底部按钮区域样式 */
.button-section {
  padding: 30rpx;
  background-color: #fff;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  animation: slide-up 0.8s ease-out;
  animation-delay: 0.6s;
  animation-fill-mode: both;
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.refund-notice {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 30rpx;
  margin-bottom: 30rpx;
  
  text {
    font-size: 22rpx;
    color: #999;
    margin-left: 10rpx;
  }
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-button {
  width: 100%;
  height: auto;
  border: none;
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0rpx 14rpx;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15);
  margin: 0;
  overflow: hidden;
  
  &:before {
    content: '';
    position: absolute;
    top: -10rpx;
    left: -10rpx;
    right: -10rpx;
    bottom: -10rpx;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transform: translateX(-100%);
    transition: transform 0s;
    pointer-events: none;
  }
  
  &:active {
    transform: translateY(3rpx);
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
    
    &:before {
      transform: translateX(100%);
      transition: transform 0.6s ease;
    }
  }
  
  &::after {
    border: none;
  }
}

.member-btn {
  background: $primary-gradient;
  color: $text-light;
  animation: button-intro 0.6s ease-out;
  animation-delay: 0.7s;
  animation-fill-mode: both;
}

.single-btn {
  background: linear-gradient(135deg, #ff9800, #ff5722);
  color: $text-light;
  animation: button-intro 0.6s ease-out;
  animation-delay: 0.9s;
  animation-fill-mode: both;
}

/* 按钮禁用状态 */
.action-button.disabled {
  opacity: 0.6;
  transform: none !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1) !important;
  pointer-events: none;

  &:active {
    transform: none !important;
  }
}

@keyframes button-intro {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.button-text {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 6rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.button-subtext {
  font-size: 24rpx;
  opacity: 0.9;
}

.button-icon {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  animation: icon-bounce 1.5s infinite alternate;
}

@keyframes icon-bounce {
  from {
    transform: translateY(-50%);
  }
  to {
    transform: translateY(-50%) translateX(5rpx);
  }
}

.selected-mark {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: #dc2e9d;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 10rpx rgba(74, 144, 226, 0.3);
  // animation: pop-in 0.3s ease-out;
}

@keyframes pop-in {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  70% {
    opacity: 1;
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
</style> 