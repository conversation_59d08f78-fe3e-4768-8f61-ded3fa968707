# 作品状态管理功能说明

## 功能概述

已为作品添加了状态管理功能，支持三种状态：
- **状态 1**: 展示（默认状态，正常显示）
- **状态 2**: 隐藏（不对外显示，只有自己可见）
- **状态 3**: 下架（预留状态）

## 核心特性

✅ **权限控制**: 只有作品发布者才能看到和操作状态控制按钮
✅ **简化操作**: 使用按钮直接切换展示/隐藏状态，无需复杂菜单
✅ **视觉反馈**: 所有状态都有清晰的背景标识
✅ **API优化**: 只调用hideWork接口，支持展示/隐藏切换

## 功能实现位置

### 1. API 接口
- **文件**: `utils/vmeitime-http/home.js`
- **函数**: `hideWork` (第98-103行)
- **用途**: 调用后端接口修改作品状态

### 2. 作品详情页面操作
- **文件**: `views/moments/detail.vue`
- **位置**: 当用户查看自己的作品时，显示状态管理按钮
- **功能**: 
  - 显示当前状态
  - 提供状态切换选项
  - 确认操作后调用API

### 3. 个人中心作品列表
- **文件**: `components/UserCardMine.vue`
- **功能**:
  - 长按作品卡片显示操作菜单
  - 支持状态切换和删除操作
  - 显示状态标识（下架/隐藏）

### 4. 个人中心页面
- **文件**: `pages/index/mine.vue`
- **功能**: 监听作品状态变更和删除事件，更新本地列表

## 使用方法

### 在作品详情页面
1. 打开自己发布的作品详情
2. 点击状态按钮（显示当前状态）
3. 确认切换操作

### 在个人中心
1. 进入个人中心的"作品"标签
2. 在自己的作品卡片右下角看到控制按钮
3. 点击眼睛图标切换展示/隐藏状态
4. 点击垃圾桶图标删除作品
5. 确认操作

## 状态显示

### 视觉标识
- **展示**: 紫色背景标识显示"展示"
- **隐藏**: 橙色背景标识显示"隐藏"
- **下架**: 红色背景标识显示"下架"（预留）

### 控制按钮
- **眼睛图标**:
  - 展示状态时显示关闭的眼睛（灰色）
  - 隐藏状态时显示睁开的眼睛（紫色）
- **垃圾桶图标**: 红色删除按钮

### 列表行为
- 隐藏的作品在状态变更后会从个人中心列表中移除
- 删除的作品会立即从列表中移除
- 只有作品发布者才能看到控制按钮

## 技术细节

### API 参数
```javascript
{
    work_id: 作品ID,
    access_token: 用户token,
    status: 状态值 (1-上架, 2-下架, 3-隐藏)
}
```

### 事件通信
- 组件间通过 `$emit` 传递状态变更事件
- 父组件监听事件并更新本地数据

## 注意事项

1. **权限控制**: 只有作品的发布者才能看到控制按钮
2. **操作简化**: 只支持展示/隐藏两种状态的切换
3. **即时生效**: 状态变更会立即生效并更新界面
4. **删除确认**: 删除操作不可恢复，会弹出确认对话框
5. **列表更新**: 隐藏的作品会从个人中心列表中移除
6. **API限制**: 只调用hideWork接口，不支持删除接口
