<template>
	<view class="moment-item">
		<view class="moment-header">
			<image class="avatar" :src="moment.userAvatar" mode="aspectFill"></image>
			<view class="user-info">
				<view class="nickname">{{moment.userName}}</view>
				<view class="time">{{moment.createTime}}</view>
			</view>
		</view>
		
		<view class="moment-content" @click="goDetail">
			<view class="text">{{moment.content}}</view>
			
			<!-- 图片展示 -->
			<view class="images" v-if="moment.images && moment.images.length > 0">
				<view class="image-list" :class="{'single-image': moment.images.length === 1, 'multi-image': moment.images.length > 1}">
					<image 
						v-for="(img, imgIndex) in moment.images" 
						:key="imgIndex" 
						:src="img" 
						mode="aspectFill" 
						@click.stop="previewImage(moment.images, imgIndex)"
						:class="{'single': moment.images.length === 1}"
					></image>
				</view>
			</view>
			
			<!-- 位置信息 -->
			<view class="location" v-if="moment.location">
				<u-icon name="map" size="14" color="#999"></u-icon>
				<text>{{moment.location}}</text>
			</view>
		</view>
		
		<view class="moment-footer">
			<view class="action-btn" @click="handleLike">
				<u-icon :name="moment.isLiked ? 'heart-fill' : 'heart'" :color="moment.isLiked ? '#FF5722' : '#999'" size="20"></u-icon>
				<text>{{moment.likeCount || 0}}</text>
			</view>
			<view class="action-btn" @click="showCommentInput">
				<u-icon name="chat" color="#999" size="20"></u-icon>
				<text>{{moment.commentCount || 0}}</text>
			</view>
		</view>
		
		<!-- 点赞列表 -->
		<view class="likes-list" v-if="moment.likes && moment.likes.length > 0">
			<u-icon name="heart-fill" color="#FF5722" size="14"></u-icon>
			<text v-for="(like, likeIndex) in moment.likes" :key="likeIndex">
				{{like.userName}}{{likeIndex < moment.likes.length - 1 ? '，' : ''}}
			</text>
		</view>
		
		<!-- 评论列表 -->
		<view class="comments-list" v-if="moment.comments && moment.comments.length > 0">
			<view class="comment-item" v-for="(comment, commentIndex) in moment.comments" :key="commentIndex">
				<text class="comment-user">{{comment.userName}}：</text>
				<text class="comment-content">{{comment.content}}</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	props: {
		moment: {
			type: Object,
			required: true
		}
	},
	methods: {
		// 跳转到详情页
		goDetail() {
			this.$emit('detail', this.moment.id);
		},
		
		// 处理点赞
		handleLike() {
			this.$emit('like', this.moment.id);
		},
		
		// 显示评论输入框
		showCommentInput() {
			this.$emit('comment', this.moment.id);
		},
		
		// 预览图片
		previewImage(images, current) {
			uni.previewImage({
				urls: images,
				current: images[current]
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.moment-item {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	
	.moment-header {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
		
		.avatar {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			margin-right: 20rpx;
		}
		
		.user-info {
			.nickname {
				font-size: 28rpx;
				font-weight: bold;
				color: #333;
			}
			
			.time {
				font-size: 24rpx;
				color: #999;
				margin-top: 4rpx;
			}
		}
	}
	
	.moment-content {
		.text {
			font-size: 28rpx;
			color: #333;
			line-height: 1.6;
			margin-bottom: 20rpx;
		}
		
		.images {
			margin-bottom: 20rpx;
			
			.image-list {
				display: flex;
				flex-wrap: wrap;
				
				&.single-image {
					image {
						max-width: 400rpx;
						max-height: 400rpx;
						border-radius: 8rpx;
					}
				}
				
				&.multi-image {
					image {
						width: 220rpx;
						height: 220rpx;
						margin-right: 10rpx;
						margin-bottom: 10rpx;
						border-radius: 8rpx;
						
						&:nth-child(3n) {
							margin-right: 0;
						}
					}
				}
			}
		}
		
		.location {
			display: flex;
			align-items: center;
			font-size: 24rpx;
			color: #999;
			
			text {
				margin-left: 6rpx;
			}
		}
	}
	
	.moment-footer {
		display: flex;
		justify-content: flex-end;
		margin-top: 20rpx;
		padding-top: 20rpx;
		border-top: 1rpx solid #f5f5f5;
		
		.action-btn {
			display: flex;
			align-items: center;
			margin-left: 40rpx;
			
			text {
				font-size: 24rpx;
				color: #999;
				margin-left: 6rpx;
			}
		}
	}
	
	.likes-list {
		margin-top: 20rpx;
		padding: 16rpx;
		background-color: #f8f8f8;
		border-radius: 8rpx;
		display: flex;
		align-items: center;
		font-size: 24rpx;
		color: #666;
		
		text {
			margin-left: 6rpx;
		}
	}
	
	.comments-list {
		margin-top: 10rpx;
		padding: 16rpx;
		background-color: #f8f8f8;
		border-radius: 8rpx;
		
		.comment-item {
			font-size: 24rpx;
			margin-bottom: 10rpx;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.comment-user {
				color: #0c3949;
				font-weight: bold;
			}
			
			.comment-content {
				color: #333;
			}
		}
	}
}
</style> 