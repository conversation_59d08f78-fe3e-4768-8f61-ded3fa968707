<template>
    <view>
        <u-popup v-model="showCart" mode="bottom" border-radius="20" :closeable="false">
            <view class="pop-content">
                <view class="pop-close" @click="hide">
                    <u-icon name="close" color="#999999"></u-icon>
                </view>
                <view class="pop-top">
                    <view class="pop-top-img">
                        <image @click="previewImg" :src="goodsId == 7 ? img2 : img1"></image>
                    </view>
                    <view class="pop-top-desc">
                        <view class="pop-top-price">
                            <view class="pop-top-price-wrap">
                                <view class="pop-top-price-y">
                                    <text>¥</text>
                                    <text>{{ goodsPrice }}</text>
                                </view>
                                <view class="pop-top-price-o">
                                    <text>¥</text>
                                    <text>99.00</text>
                                    <view class="pop-top-price-o-line"></view>
                                </view>
                            </view>
                        </view>
                        <view class="pop-top-title">{{ goodsTitle }}</view>
                        <view class="pop-top-store">库存9999+</view>
                    </view>
                </view>
                <view class="pop-color">
                    <view class="pop-color-title">颜色</view>
                    <view class="pop-color-wrap">
                        <view class="pop-color-item" @click.stop="checkGoods(6)" :class="{ 'check-goods': goodsId == '6' }">白色</view>
                        <view class="pop-color-item" @click.stop="checkGoods(7)" :class="{ 'check-goods': goodsId == '7' }">黑色</view>
                    </view>
                </view>
                <view class="pop-buy">
                    <view class="pop-buy-title">购买数量</view>
                    <view class="pop-buy-num">
                        <u-number-box v-model="num" size="32" @change="valChange" :min="1"></u-number-box>
                    </view>
                </view>
                <view class="pop-address">
                    <view class="pop-address-title">收货地址</view>
                    <view class="pop-address-detail u-line-2" @click="goAddress(2)" v-if="addressMsg.address">{{ addressMsg.areacode }}{{ addressMsg.address }}</view>
                    <view v-else class="pop-address-add" @click="goAddress(1)">
                        <u-icon name="plus" color="#088afe" style="margin-right: 6rpx"></u-icon>
                        添加收货地址
                    </view>
                </view>
                <view class="pop-bottom">
                    <view class="pop-bottom-btn" @click.stop="payment">点击支付</view>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
export default {
    props: {
        address: {
            type: Object,
            default: () => {},
        },
    },
    watch: {
        address: {
            handler(n) {
                this.addressMsg = n;
            },
            deep: true,
        },
    },
    data() {
        return {
            showCart: false,
            img1: this.$apiHost + '/static/images/banner/4.jpg',
            img2: this.$apiHost + '/static/images/banner/12.jpg',
            goodsPrice: '89.00',
            goodsTitle: '高石苹果认证TAG无线定位防丢器',
            num: 1,
            addressMsg: {
                uname: '',
                mobile: '',
                areacode: '', // 地区
                address: '',
                id: '',
            },
            goodsId: '',
        };
    },
    computed: {},
    created() {},
    methods: {
        show() {
            this.showCart = true;
        },
        hide() {
            this.showCart = false;
        },
        valChange(e) {
            console.log(e);
        },
        // 选中商品颜色
        checkGoods(val) {
            this.goodsId = val;
        },
        // 点击支付
        payment() {
            if (!this.$store.state.hasLogin) {
                this.$parent.$refs.auth.show();
                console.log(this.$parent);
                return;
            }
            if (!this.goodsId) {
                uni.showModal({
                    title: '提示',
                    content: '请选择商品颜色',
                    showCancel: false,
                });
                return;
            }
            if (this.num == 0) {
                uni.showModal({
                    title: '提示',
                    content: '购买商品的数量不能为零',
                    showCancel: false,
                });
                return;
            }
            if (!this.addressMsg.id) {
                uni.showModal({
                    title: '提示',
                    content: '请添加地址后在进行购买',
                    showCancel: false,
                });
                return;
            }
            this.createOrder();
        },
        // 创建订单
        async createOrder() {
            uni.showLoading({
                title: '购买中',
            });
            let res = await this.$api.createOrder({
                access_token: uni.getStorageSync('token'),
                goodsid: this.goodsId,
                buynum: this.num,
                addressid: this.addressMsg.id,
            });
            console.log(res, 88888);
            console.log(res.status == 0);
            if (res.status == 0) {
                this.payOrder(res.data.orderid);
            }
        },

        // 支付订单
        payOrder(orderId) {
            console.log(orderId, 9999);
            this.$api
                .getPayMsg({
                    orderid: orderId,
                    openid: this.$store.state.userInfo.openid,
                    access_token: uni.getStorageSync('token'),
                })
                .then(res => {
                    console.log(res, 9999);
                    uni.requestPayment({
                        timeStamp: res.data.timeStamp,
                        nonceStr: res.data.nonceStr,
                        package: res.data.package,
                        paySign: res.data.paySign,
                        signType: res.data.signType,
                        success: () => {
                            this.hide();
                            uni.showModal({
                                title: '提示',
                                content: '购买商品成功',
                                cancelText: '我知道了',
                                confirmText: '查看订单',
                                success: res => {
                                    if (res.confirm) {
                                        uni.navigateTo({
                                            url: `/views/mine/historyOrder`,
                                        });
                                    }
                                },
                            });
                        },
                        fail: res => {
                            uni.showToast({ title: '购买失败', icon: 'none' });
                        },
                    });
                });
        },

        goAddress(type) {
            if (!this.$store.state.hasLogin) {
                this.$parent.$refs.auth.show();
                return;
            }
            uni.navigateTo({
                url: `/views/develop/addressEdit?addressMsg=${JSON.stringify(this.addressMsg)}&type=${type}`,
            });
        },

        // 预览大图
        previewImg() {
            var urls = this.goodsId == 7 ? [this.img2] : [this.img1];
            uni.previewImage({
                current: 0,
                urls: urls,
            });
        },
    },
};
</script>

<style lang="scss" scoped>
.pop-content {
    background: #ffffff;
    padding: 50rpx 30rpx 30rpx;
    position: relative;
    .pop-close {
        position: absolute;
        width: 44rpx;
        height: 44rpx;
        border-radius: 50%;
        border: 1px solid #aca9a9;
        display: flex;
        align-items: center;
        justify-content: center;
        top: 20rpx;
        right: 20rpx;
    }
    .pop-top {
        display: flex;
        align-items: flex-start;
        &-img {
            image {
                width: 156rpx;
                height: 156rpx;
                border-radius: 16rpx;
            }
        }
        &-desc {
            margin-left: 30rpx;
        }
        &-price {
            &-wrap {
                display: flex;
                align-items: center;
            }
            &-y {
                text:nth-child(1) {
                    font-size: 32rpx;
                    font-weight: 400;
                    color: #f70612;
                }
                text:nth-child(2) {
                    font-size: 48rpx;
                    font-weight: 400;
                    color: #f70612;
                }
            }
            &-o {
                font-size: 30rpx;
                font-weight: 400;
                color: #999999;
                margin-left: 30rpx;
                position: relative;
                &-line {
                    background: #666666;
                    width: 100%;
                    height: 1px;
                    position: absolute;
                    top: 50%;
                    left: 0;
                    transform: translateY(-50%);
                }
            }
        }
        &-title {
            margin-top: 4rpx;
            font-size: 32rpx;
            color: #333333;
            font-weight: bold;
        }
        &-store {
            margin-top: 8rpx;
            font-size: 28rpx;
            font-weight: 400;
            color: #999999;
        }
    }
    .pop-color {
        margin-top: 40rpx;
        &-title {
            font-size: 32rpx;
            color: #333333; /*  */
        }
        &-wrap {
            display: flex;
            align-items: center;
            margin-top: 16rpx;
        }
        &-item {
            background: #eeeeee;
            color: #333333;
            width: 156rpx;
            height: 58rpx;
            margin-right: 24rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 26rpx;
            border-radius: 49rpx;
        }
    }
    .pop-address {
        display: flex;
        align-items: flex-start;
        margin-top: 40rpx;
        &-title {
            font-size: 32rpx;
            color: #333333;
            width: 230rpx;
        }
        &-detail {
            font-size: 30rpx;
        }
        &-add {
            color: #088afe;
            font-size: 32rpx;
            display: flex;
            align-items: center;
        }
    }
    .pop-buy {
        display: flex;
        align-content: center;
        justify-content: space-between;
        margin-top: 40rpx;
        &-title {
            font-size: 32rpx;
            color: #333333;
        }
    }
    .check-goods {
        background-color: #088afe;
        color: #ffffff;
    }
    .pop-bottom {
        margin-top: 30rpx;
        border-top: 1px solid #efefef;
        padding: 40rpx 0 20rpx;
        &-btn {
            background: #088afe;
            color: #ffffff;
            width: 690rpx;
            height: 88rpx;
            border-radius: 49rpx;
            font-size: 34rpx;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}
</style>
