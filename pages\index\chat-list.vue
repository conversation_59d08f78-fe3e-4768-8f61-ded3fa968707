<template>
    <view class="chat-list-container">
        <!-- 页面标题 -->
        <view class="page-header">
            <view class="header-content">
                <text class="page-title">消息中心</text>
                <text class="page-subtitle">与好友保持联系</text>
            </view>
            <view class="header-decoration">
                <view class="decoration-circle circle-1"></view>
                <view class="decoration-circle circle-2"></view>
            </view>
        </view>

        <!-- 快捷操作按钮 -->
        <view class="action-buttons">
            <view class="action-btn" @tap="handleLike">
                <view class="icon-wrapper like-icon">
                    <u-icon name="heart-fill" size="50" color="#8966ef"></u-icon>
                    <view class="unread-count" v-if="unRead.no_read_like_num && unRead.no_read_like_num > 0">
                        {{ unRead.no_read_like_num > 99 ? '99+' : unRead.no_read_like_num }}
                    </view>
                </view>
                <text class="btn-text">点赞收藏</text>
            </view>
            <view class="action-btn" @tap="handleFollow">
                <view class="icon-wrapper follow-icon">
                    <u-icon name="plus-circle-fill" size="50" color="#8966ef"></u-icon>
                    <view class="unread-count" v-if="unRead.no_read_follow_num && unRead.no_read_follow_num > 0">
                        {{ unRead.no_read_follow_num > 99 ? '99+' : unRead.no_read_follow_num }}
                    </view>
                </view>
                <text class="btn-text">新增关注</text>
            </view>
            <view class="action-btn" @tap="handleComment">
                <view class="icon-wrapper comment-icon">
                    <u-icon name="chat-fill" size="50" color="#8966ef"></u-icon>
                    <view class="unread-count" v-if="unRead.no_read_comment_num && unRead.no_read_comment_num > 0">
                        {{ unRead.no_read_comment_num > 99 ? '99+' : unRead.no_read_comment_num }}
                    </view>
                </view>
                <text class="btn-text">新增评论</text>
            </view>
            <view class="action-btn" @tap="handleRobot">
                <view class="icon-wrapper robot-icon">
                    <u-icon name="setting-fill" size="50" color="#f0cc6c"></u-icon>
                </view>
                <text class="btn-text">自动回复</text>
            </view>
        </view>

        <!-- 聊天列表 -->
        <scroll-view scroll-y class="chat-list">
            <!-- 空状态 -->
            <empty-state
                :show="msgList.length === 0"
                type="message"
                text="暂无聊天记录"
                tip="开始与好友聊天吧"
            ></empty-state>

            <!-- 聊天项目 -->
            <view class="chat-item" v-for="(item, index) in filteredChatList" :key="index" @tap="gotoChat(item)">
                <view class="avatar-wrapper">
                    <u-avatar :src="getChatAvatar(item)" size="108"></u-avatar>
                    <view class="unread-badge" v-if="item.is_read === '0' && item.uid !== currentUid">{{ 1 }}</view>
                    <view class="online-indicator"></view>
                </view>
                <view class="chat-content">
                    <view class="top-info">
                        <text class="username">{{ getChatName(item) }}</text>
                        <text class="time">{{ formatTime(item.addtime) }}</text>
                    </view>
                    <view class="message-preview">
                        <text class="message">{{ item.content }}</text>
                    </view>
                </view>
                <view class="chat-arrow">
                    <u-icon name="arrow-right" size="32" color="#d1c4e9"></u-icon>
                </view>
            </view>
        </scroll-view>
        <view v-show="!hasLogin" class="content-mask" @touchmove.stop="" @click.stop="touchLogin"></view>
    </view>
</template>

<script>
import { mapState } from 'vuex';
export default {
    data() {
        return {
            keyword: '',
            page: 1,
            pagesize: 10,
            total: 0,
            msgList: [],
            currentUid: '', // 当前用户ID
            unRead: {
                no_read_comment_num: '',
                no_read_follow_num: '',
                no_read_like_num: '',
            },
        };
    },
    computed: {
        ...mapState(['userInfo', 'hasLogin']),
        filteredChatList() {
            return this.msgList;
        },
    },

    onLoad() {
        if (!this.hasLogin) {
            return;
        }
        // 获取当前用户ID
        this.currentUid = this.userInfo.uid || '';
        this.init();
    },
    onShow() {
        if (this.hasLogin) {
            this.init();
        }
    },
    onReachBottom() {
        if (this.msgList.length < this.total) {
            this.page++;
            this.getMsgList();
        }
    },
    onPullDownRefresh: function () {
        setTimeout(() => {
            uni.stopPullDownRefresh();
        }, 500);
        if (!this.hasLogin) return;
        this.init();
    },
    methods: {
        handleSearch(value) {
            this.keyword = value;
        },
        init() {
            this.getMsgList();
            this.getUnreadMsgList();
        },
        async getUnreadMsgList() {
            try {
                const res = await this.$api.getUnreadMsgList({
                    access_token: uni.getStorageSync('token'),
                });

                if (res && res.data) {
                    this.unRead = {
                        no_read_comment_num: parseInt(res.data.no_read_comment_num || 0),
                        no_read_follow_num: parseInt(res.data.no_read_follow_num || 0),
                        no_read_like_num: parseInt(res.data.no_read_like_num || 0),
                    };
                }
            } catch (error) {
                console.error('获取未读消息数量失败:', error);
            }
        },
        // 判断是否授权
        touchLogin() {
            // this.$refs.auth.show();
            uni.navigateTo({
                url: '/pages/index/login',
            });
            return;
        },
        async getMsgList() {
            try {
                const res = await this.$api.getMsgList({
                    page: this.page,
                    pagesize: this.pagesize,
                    access_token: uni.getStorageSync('token'),
                });

                if (res && res.data && res.data.list) {
                    if (this.page === 1) {
                        this.msgList = res.data.list;
                    } else {
                        this.msgList = [...this.msgList, ...res.data.list];
                    }
                    this.total = parseInt(res.data.total || 0);
                }
            } catch (error) {
                console.error('获取消息列表失败', error);
                uni.showToast({
                    title: '获取消息列表失败',
                    icon: 'none',
                });
            }
        },
        // 获取聊天对象的头像
        getChatAvatar(item) {
            // 如果当前用户是发送者，则显示接收者的名称
            if (item.uid === this.currentUid) {
                return item.to_user && item.to_user.headimgurl ? item.to_user.headimgurl : '/static/images/we.png';
            }
            // 否则显示发送者的名称
            return item.user && item.user.headimgurl ? item.user.headimgurl : '/static/images/we.png';
        },
        // 获取聊天对象的名称
        getChatName(item) {
            // 如果当前用户是发送者，则显示接收者的名称
            if (item.uid === this.currentUid) {
                return item.to_user && item.to_user.nickname ? item.to_user.nickname : '微信用户';
            }
            // 否则显示发送者的名称
            return item.user && item.user.nickname ? item.user.nickname : '微信用户';
        },
        handleLike() {
            // 处理点赞收藏事件
            uni.navigateTo({
                url: '/views/mine/likes',
            });
        },
        handleFollow() {
            // 处理新增关注事件
            uni.navigateTo({
                url: '/views/moments/follow',
            });
        },
        handleComment() {
            // 处理评论和@事件
            uni.navigateTo({
                url: '/views/moments/comments',
            });
        },
        handleRobot() {
            // 处理自动回复事件
            uni.navigateTo({
                url: '/views/robot/index',
            });
        },
        gotoChat(item) {
            // 确定聊天对象
            const chatTarget = item.uid === this.currentUid ? item.to_user : item.user;

            if (!chatTarget) {
                uni.showToast({
                    title: '聊天对象信息不完整',
                    icon: 'none',
                });
                return;
            }
            // 导航到聊天详情页
            uni.navigateTo({
                url: `/views/moments/chat?sessionId=${item.msg_session_id}&userId=${chatTarget.uid}&username=${encodeURIComponent(chatTarget.nickname)}&avatar=${encodeURIComponent(chatTarget.headimgurl)}`,
            });
        },
        formatTime(timestamp) {
            if (!timestamp) return '';

            // 处理时间戳或日期字符串
            let msgDate;
            if (typeof timestamp === 'string') {
                msgDate = new Date(timestamp.replace(/-/g, '/'));
            } else {
                msgDate = new Date(timestamp);
            }

            const now = new Date();

            // 今天的消息只显示时间
            if (msgDate.toDateString() === now.toDateString()) {
                return msgDate.getHours().toString().padStart(2, '0') + ':' + msgDate.getMinutes().toString().padStart(2, '0');
            }

            // 昨天的消息显示"昨天"
            const yesterday = new Date(now);
            yesterday.setDate(now.getDate() - 1);
            if (msgDate.toDateString() === yesterday.toDateString()) {
                return '昨天';
            }

            // 其他时间显示日期
            return msgDate.getMonth() + 1 + '-' + msgDate.getDate();
        },
    },
};
</script>

<style lang="scss" scoped>
.content-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    background-color: transparent;
    z-index: 1071;
}
.chat-list-container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f6ff 0%, #fff5e6 100%);
    position: relative;

    // 页面头部
    .page-header {
        position: relative;
        padding: 40rpx 30rpx 30rpx;
        background: linear-gradient(135deg, #8966ef, #a584f2);
        overflow: hidden;

        .header-content {
            position: relative;
            z-index: 2;

            .page-title {
                display: block;
                font-size: 48rpx;
                font-weight: 600;
                color: #ffffff;
                margin-bottom: 8rpx;
            }

            .page-subtitle {
                display: block;
                font-size: 28rpx;
                color: rgba(255, 255, 255, 0.8);
            }
        }

        .header-decoration {
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 100%;
            z-index: 1;

            .decoration-circle {
                position: absolute;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.1);

                &.circle-1 {
                    width: 120rpx;
                    height: 120rpx;
                    top: -30rpx;
                    right: 50rpx;
                    animation: float 6s ease-in-out infinite;
                }

                &.circle-2 {
                    width: 80rpx;
                    height: 80rpx;
                    top: 60rpx;
                    right: 150rpx;
                    animation: float 4s ease-in-out infinite reverse;
                }
            }
        }
    }

    // 快捷操作按钮
    .action-buttons {
        display: flex;
        justify-content: space-around;
        align-items: center;
        padding: 30rpx 20rpx;
        background-color: #ffffff;
        margin: 20rpx;
        border-radius: 24rpx;
        box-shadow: 0 8rpx 32rpx rgba(137, 102, 239, 0.1);

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;

            &:active {
                transform: scale(0.95);
            }

            .icon-wrapper {
                width: 108rpx;
                height: 108rpx;
                border-radius: 24rpx;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-bottom: 16rpx;
                transition: all 0.3s ease;
                position: relative;

                &.like-icon {
                    background: linear-gradient(135deg, rgba(137, 102, 239, 0.1), rgba(165, 132, 242, 0.1));
                }

                &.follow-icon {
                    background: linear-gradient(135deg, rgba(137, 102, 239, 0.1), rgba(165, 132, 242, 0.1));
                }

                &.comment-icon {
                    background: linear-gradient(135deg, rgba(137, 102, 239, 0.1), rgba(165, 132, 242, 0.1));
                }

                &.robot-icon {
                    background: linear-gradient(135deg, rgba(240, 204, 108, 0.1), rgba(237, 195, 83, 0.1));
                }

                .unread-count {
                    position: absolute;
                    top: -8rpx;
                    right: -8rpx;
                    background: linear-gradient(135deg, #ff4757, #ff3838);
                    color: #ffffff;
                    font-size: 20rpx;
                    min-width: 32rpx;
                    height: 32rpx;
                    border-radius: 16rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 0 6rpx;
                    font-weight: 600;
                    box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.3);
                    border: 2rpx solid #ffffff;
                    z-index: 10;
                    animation: pulse 2s infinite;
                }
            }

            .btn-text {
                font-size: 26rpx;
                color: #666666;
                font-weight: 500;
            }
        }
    }

    // 聊天列表
    .chat-list {
        flex: 1;
        padding: 0 0rpx 20rpx;

        // 空状态
        .no-data {
            padding: 120rpx 40rpx;
            display: flex;
            justify-content: center;
            align-items: center;

            .empty-illustration {
                text-align: center;

                .empty-icon {
                    margin-bottom: 40rpx;
                    opacity: 0.6;
                }

                .empty-title {
                    display: block;
                    font-size: 32rpx;
                    color: #666666;
                    font-weight: 500;
                    margin-bottom: 16rpx;
                }

                .empty-subtitle {
                    display: block;
                    font-size: 28rpx;
                    color: #999999;
                }
            }
        }

        // 聊天项目
        .chat-item {
            display: flex;
            padding: 32rpx 24rpx;
            margin: 0 20rpx 16rpx;
            align-items: center;
            background-color: #ffffff;
            border-radius: 20rpx;
            box-shadow: 0 4rpx 20rpx rgba(137, 102, 239, 0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;

            &:active {
                transform: scale(0.98);
                box-shadow: 0 2rpx 12rpx rgba(137, 102, 239, 0.12);
            }

            &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 0;
                width: 6rpx;
                height: 100%;
                background: linear-gradient(135deg, #8966ef, #a584f2);
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            &:active::before {
                opacity: 1;
            }

            .avatar-wrapper {
                position: relative;
                margin-right: 24rpx;

                .unread-badge {
                    position: absolute;
                    top: -8rpx;
                    right: -8rpx;
                    background: linear-gradient(135deg, #ea2e2e, #ed5353);
                    color: #ffffff;
                    font-size: 20rpx;
                    min-width: 32rpx;
                    height: 32rpx;
                    border-radius: 16rpx;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 0 8rpx;
                    font-weight: 600;
                    box-shadow: 0 4rpx 12rpx rgba(240, 204, 108, 0.3);
                }

                .online-indicator {
                    position: absolute;
                    bottom: 4rpx;
                    right: 4rpx;
                    width: 24rpx;
                    height: 24rpx;
                    background-color: #4cd964;
                    border-radius: 50%;
                    border: 4rpx solid #ffffff;
                }
            }

            .chat-content {
                flex: 1;
                overflow: hidden;

                .top-info {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 12rpx;

                    .username {
                        font-size: 32rpx;
                        color: #333333;
                        font-weight: 600;
                        max-width: 400rpx;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        white-space: nowrap;
                    }

                    .time {
                        font-size: 24rpx;
                        color: #999999;
                        flex-shrink: 0;
                    }
                }

                .message-preview {
                    .message {
                        font-size: 28rpx;
                        color: #666666;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        white-space: nowrap;
                        line-height: 1.4;
                    }
                }
            }

            .chat-arrow {
                margin-left: 16rpx;
                opacity: 0.6;
                transition: all 0.3s ease;
            }

            &:active .chat-arrow {
                opacity: 1;
                transform: translateX(4rpx);
            }
        }
    }
}

// 浮动动画
@keyframes float {
    0%,
    100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20rpx) rotate(180deg);
    }
}

// 脉动动画
@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}
</style>
